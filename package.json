{"name": "veatime<PERSON><PERSON>", "version": "2.0.0", "private": true, "type": "module", "scripts": {"start": "node server/index.js", "predev": "./scripts/kill-port-4000.sh && ./generate-ip-cert.sh", "dev": "NODE_NO_WARNINGS=1 concurrently \"cd server && nodemon index.js\" \"cd admin && vite\"", "build": "NODE_ENV=production cd admin && vite build", "heroku-postbuild": "npm run build", "heroku:token:check": "node server/scripts/renew-heroku-token.js check", "heroku:token:renew": "node server/scripts/renew-heroku-token.js renew", "heroku:token:info": "node server/scripts/renew-heroku-token.js info", "heroku:setup-cron": "bash server/scripts/setup-token-renewal-cron.sh"}, "dependencies": {"@ant-design/charts": "^2.3.0", "@ant-design/colors": "^7.2.1", "@ant-design/icons": "^6.0.0", "@aws-sdk/client-s3": "^3.817.0", "@aws-sdk/s3-request-presigner": "^3.826.0", "@docuseal/api": "^1.0.16", "@emotion/react": "^11.14.0", "@googlemaps/markerclusterer": "^2.5.3", "@react-google-maps/api": "^2.20.6", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@slack/web-api": "^7.9.2", "@tanem/react-nprogress": "^5.0.55", "@tanstack/react-query": "^5.76.2", "@tanstack/react-query-devtools": "^5.76.2", "@vis.gl/react-google-maps": "^1.5.2", "antd": "^5.25.2", "apexcharts": "^4.7.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cli-progress": "^3.12.0", "cors": "^2.8.5", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "firebase": "^11.8.1", "firebase-admin": "^13.4.0", "framer-motion": "^12.17.0", "googleapis": "^149.0.0", "i18next": "^25.2.1", "interactjs": "^1.10.27", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lodash.groupby": "^4.6.0", "multer": "^2.0.0", "node-cache": "^5.1.2", "node-cron": "^4.1.1", "node-emoji": "^2.2.0", "node-fetch": "^3.3.2", "node-qpdf2": "^6.0.0", "numeral": "^2.0.6", "os": "^0.1.2", "pdf-lib": "^1.17.1", "pdfjs-dist": "^5.2.133", "pdfmake": "^0.2.20", "pg": "^8.16.0", "qrcode": "^1.5.4", "react": "^18.2.0", "react-apexcharts": "^1.7.0", "react-countup": "^6.5.3", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.5", "react-i18next": "^15.5.3", "react-icons": "^5.5.0", "react-image-crop": "^11.0.10", "react-imask": "^7.6.1", "react-infinite-scroll-component": "^6.1.0", "react-responsive": "^10.0.1", "react-router": "^7.6.1", "react-timeago": "^8.2.0", "react-transition-group": "^4.4.5", "redis": "^5.5.6", "systeminformation": "^5.27.1", "tls": "^0.0.1", "twilio": "^5.7.0", "zustand": "^5.0.5"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@react-router/dev": "^7.5.3", "@react-router/serve": "^7.5.3", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20.11.24", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.4.1", "babel-plugin-import": "^1.13.8", "concurrently": "^8.2.2", "nodemon": "^3.1.0", "patch-package": "^8.0.0", "prettier": "^3.1.1", "rollup-plugin-visualizer": "^6.0.1", "sass-embedded": "^1.89.0", "tailwindcss": "^4.1.4", "terser": "^5.40.0", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-babel": "^1.3.1", "vite-tsconfig-paths": "^5.1.4"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}, "engines": {"node": "22.x", "npm": "11.x"}}