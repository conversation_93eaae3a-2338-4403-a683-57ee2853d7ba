# 📄 Paystub Extraction Catch-Up Guide

## 🚨 Problem Solved
When the scheduled paystub extraction job fails (like it did for 3 weeks due to import path issues), you can now easily catch up on all missed extractions.

## 🛠️ Available Tools

### 1. **Catch-Up Script** (`catch_up_paystubs.js`)
A comprehensive script for processing missed paystub extractions.

### 2. **Enhanced Main Function** (`extractPaystubsMultipleWeeks`)
Added to the main `extract_paystubs.js` file for programmatic use.

## 🚀 Quick Start - Catch Up Last 3 Weeks

```bash
# Run from project root
node scripts/jobs/catch_up_paystubs.js
```

This will automatically:
- ✅ Calculate the last 3 Tuesday dates
- ✅ Process paystubs for each date
- ✅ Show progress and results
- ✅ Handle errors gracefully
- ✅ Add delays between batches (gentle on Gmail API)

## 📋 Usage Examples

### Process Different Number of Weeks
```bash
# Last 5 weeks
node scripts/jobs/catch_up_paystubs.js --weeks 5

# Last 1 week only
node scripts/jobs/catch_up_paystubs.js --weeks 1
```

### Process Specific Date
```bash
# Process specific Tuesday (YYYY-MM-DD format)
node scripts/jobs/catch_up_paystubs.js --date 2025-01-07
```

### Process Date Range
```bash
# Process all Tuesdays between two dates
node scripts/jobs/catch_up_paystubs.js --from 2025-01-07 --to 2025-01-21
```

### Dry Run (Preview Only)
```bash
# See what would be processed without actually doing it
node scripts/jobs/catch_up_paystubs.js --weeks 3 --dry-run
```

### Get Help
```bash
node scripts/jobs/catch_up_paystubs.js --help
```

## 🔧 Technical Details

### How It Works
1. **Date Calculation**: Automatically finds the correct Tuesday dates based on current date
2. **Gmail Integration**: Uses existing Gmail API setup to search for paystub emails
3. **Batch Processing**: Processes each week separately with progress tracking
4. **Error Handling**: Continues processing even if one week fails
5. **API Throttling**: Adds 2-second delays between batches to respect Gmail API limits

### Date Logic
- **If today is Wednesday or later**: Gets last Tuesday, then goes back N weeks
- **If today is Monday/Tuesday**: Gets previous Tuesday, then goes back N weeks
- **Validates**: Warns if specified dates aren't Tuesdays (paystub email day)

### Safety Features
- ✅ **Dry run mode** - Preview what will be processed
- ✅ **Date validation** - Warns about non-Tuesday dates
- ✅ **Error logging** - All errors logged to `paystub_errors.log`
- ✅ **Progress tracking** - Shows current progress and results
- ✅ **API throttling** - Gentle on Gmail API with delays

## 📊 Expected Output

```
🔄 PAYSTUB CATCH-UP SCRIPT
==========================

📅 Processing last 3 weeks
📅 Processing paystubs for these Tuesday dates: 2025-01-14, 2025-01-07, 2024-12-31

🔄 Processing paystubs for 2025-01-14...
🔄 Fetching paystub emails...
Progress: 45 / 45
✅ Successfully processed paystubs for 2025-01-14

🔄 Processing paystubs for 2025-01-07...
🔄 Fetching paystub emails...
Progress: 42 / 42
✅ Successfully processed paystubs for 2025-01-07

🔄 Processing paystubs for 2024-12-31...
🔄 Fetching paystub emails...
Progress: 38 / 38
✅ Successfully processed paystubs for 2024-12-31

📊 Multi-week extraction complete:
   ✅ Successful: 3 weeks
   ❌ Errors: 0 weeks
   📅 Total weeks processed: 3
```

## 🔄 Future Use Cases

This system is now reusable for:
- **Server outages** - Catch up after downtime
- **API issues** - Retry failed extractions
- **Holiday processing** - Process missed holiday weeks
- **Manual runs** - Process specific date ranges on demand
- **Testing** - Dry run to verify what would be processed

## 🚨 Important Notes

1. **Tuesday Focus**: Paystub emails are sent on Tuesdays, so the script focuses on Tuesday dates
2. **Gmail API Limits**: The script includes delays to respect Gmail API rate limits
3. **Error Logging**: All errors are logged to `paystub_errors.log` for debugging
4. **Database Required**: Requires database connection (won't work in local dev without DB setup)
5. **Permissions**: Requires Google OAuth refresh token to be set up for the user

## 🔧 Integration with Existing System

The catch-up functionality integrates seamlessly with your existing paystub system:
- Uses same Gmail search logic
- Uses same PDF processing pipeline
- Uses same database storage
- Uses same error handling and logging
- Maintains same progress tracking

## 📝 Maintenance

- **Regular use**: Run catch-up script whenever scheduled jobs fail
- **Monitoring**: Check `paystub_errors.log` for any processing issues
- **Validation**: Use dry-run mode to verify date calculations before processing
