#!/usr/bin/env node

/**
 * PAYSTUB CATCH-UP SCRIPT
 * =======================
 * 
 * This script allows you to catch up on missed paystub extractions.
 * It can process paystubs for multiple weeks back or specific date ranges.
 * 
 * USAGE EXAMPLES:
 * ---------------
 * 
 * # Process last 3 weeks (default)
 * node scripts/jobs/catch_up_paystubs.js
 * 
 * # Process last 5 weeks
 * node scripts/jobs/catch_up_paystubs.js --weeks 5
 * 
 * # Process specific date (Tuesday format: YYYY-MM-DD)
 * node scripts/jobs/catch_up_paystubs.js --date 2025-01-07
 * 
 * # Process date range (both dates should be Tuesdays)
 * node scripts/jobs/catch_up_paystubs.js --from 2025-01-07 --to 2025-01-21
 * 
 * # Dry run (show what dates would be processed without actually processing)
 * node scripts/jobs/catch_up_paystubs.js --weeks 3 --dry-run
 * 
 * FEATURES:
 * ---------
 * ✅ Process multiple weeks automatically
 * ✅ Process specific dates or date ranges
 * ✅ Dry run mode to preview what will be processed
 * ✅ Detailed progress reporting
 * ✅ Error handling and logging
 * ✅ Gentle on Gmail API with delays between batches
 * 
 * <AUTHOR> Timeclock Team
 * @version 1.0.0
 * @created 2025-01-18
 */

import { extractPaystubsMultipleWeeks, extractPaystubs } from './extract_paystubs.js';
import dayjs from '../../server/utils/dayjs.js';
import { TIMEZONE } from '../../server/consts.js';

// Parse command line arguments
const args = process.argv.slice(2);
const getArgValue = (flag) => {
  const index = args.indexOf(flag);
  return index !== -1 && index + 1 < args.length ? args[index + 1] : null;
};

const hasFlag = (flag) => args.includes(flag);

const weeksBack = parseInt(getArgValue('--weeks')) || 3;
const specificDate = getArgValue('--date');
const fromDate = getArgValue('--from');
const toDate = getArgValue('--to');
const isDryRun = hasFlag('--dry-run');
const showHelp = hasFlag('--help') || hasFlag('-h');

// ** HELP DISPLAY ** ==========================================================

if (showHelp) {
  console.log(`
🔄 PAYSTUB CATCH-UP SCRIPT
==========================

This script helps you catch up on missed paystub extractions.

USAGE:
  node scripts/jobs/catch_up_paystubs.js [options]

OPTIONS:
  --weeks <number>     Process last N weeks (default: 3)
  --date <YYYY-MM-DD>  Process specific Tuesday date
  --from <YYYY-MM-DD>  Start date for range processing
  --to <YYYY-MM-DD>    End date for range processing
  --dry-run           Show what would be processed without actually processing
  --help, -h          Show this help message

EXAMPLES:
  # Process last 3 weeks
  node scripts/jobs/catch_up_paystubs.js

  # Process last 5 weeks
  node scripts/jobs/catch_up_paystubs.js --weeks 5

  # Process specific Tuesday
  node scripts/jobs/catch_up_paystubs.js --date 2025-01-07

  # Process date range
  node scripts/jobs/catch_up_paystubs.js --from 2025-01-07 --to 2025-01-21

  # Dry run to see what would be processed
  node scripts/jobs/catch_up_paystubs.js --weeks 3 --dry-run

NOTES:
  - Dates should be Tuesdays (when paystub emails are sent)
  - The script will validate dates and show warnings for non-Tuesdays
  - Processing includes delays between batches to be gentle on Gmail API
  - All errors are logged to paystub_errors.log
`);
  process.exit(0);
}

// ** VALIDATION FUNCTIONS ** ==========================================================

function validateTuesday(dateString, label = 'Date') {
  const date = dayjs(dateString).tz(TIMEZONE);
  if (!date.isValid()) {
    throw new Error(`${label} '${dateString}' is not a valid date format (use YYYY-MM-DD)`);
  }
  if (date.day() !== 2) {
    console.warn(`⚠️  Warning: ${label} '${dateString}' is not a Tuesday (day ${date.format('dddd')})`);
    console.warn(`   Paystub emails are typically sent on Tuesdays. Continue anyway? (y/N)`);
  }
  return date;
}

function generateTuesdayRange(fromDate, toDate) {
  const start = dayjs(fromDate).tz(TIMEZONE);
  const end = dayjs(toDate).tz(TIMEZONE);
  const dates = [];
  
  let current = start;
  while (current.isSameOrBefore(end)) {
    if (current.day() === 2) { // Tuesday
      dates.push(current.format('YYYY-MM-DD'));
    }
    current = current.add(1, 'day');
  }
  
  return dates;
}

// ** MAIN EXECUTION ** ==========================================================

(async () => {
  console.log('🔄 PAYSTUB CATCH-UP SCRIPT');
  console.log('==========================\n');

  try {
    
    if (specificDate) {
      // Process single specific date
      console.log(`📅 Processing specific date: ${specificDate}`);
      validateTuesday(specificDate, 'Specific date');
      
      if (isDryRun) {
        console.log(`🔍 DRY RUN: Would process paystubs for ${specificDate}`);
        process.exit(0);
      }
      
      await extractPaystubs(specificDate);
      console.log(`✅ Completed processing for ${specificDate}`);
      
    } else if (fromDate && toDate) {
      // Process date range
      console.log(`📅 Processing date range: ${fromDate} to ${toDate}`);
      validateTuesday(fromDate, 'From date');
      validateTuesday(toDate, 'To date');
      
      const tuesdayDates = generateTuesdayRange(fromDate, toDate);
      console.log(`📋 Found ${tuesdayDates.length} Tuesdays in range: ${tuesdayDates.join(', ')}`);
      
      if (isDryRun) {
        console.log(`🔍 DRY RUN: Would process paystubs for ${tuesdayDates.length} dates:`);
        tuesdayDates.forEach(date => console.log(`   - ${date}`));
        process.exit(0);
      }
      
      let successCount = 0;
      let errorCount = 0;
      
      for (const date of tuesdayDates) {
        console.log(`\n🔄 Processing ${date}...`);
        try {
          await extractPaystubs(date);
          successCount++;
          console.log(`✅ Successfully processed ${date}`);
          await new Promise(resolve => setTimeout(resolve, 2000)); // Delay between batches
        } catch (err) {
          errorCount++;
          console.error(`❌ Error processing ${date}:`, err.message);
        }
      }
      
      console.log(`\n📊 Range processing complete:`);
      console.log(`   ✅ Successful: ${successCount} dates`);
      console.log(`   ❌ Errors: ${errorCount} dates`);
      
    } else {
      // Process multiple weeks (default behavior)
      console.log(`📅 Processing last ${weeksBack} weeks`);
      
      if (isDryRun) {
        console.log(`🔍 DRY RUN: Would process paystubs for last ${weeksBack} weeks`);
        // Show what dates would be processed
        const today = dayjs().tz(TIMEZONE);
        const previewDates = [];
        
        for (let i = 0; i < weeksBack; i++) {
          let targetDate = today;
          if (today.day() >= 3) {
            targetDate = today.subtract(today.day() - 2, 'day');
          } else {
            targetDate = today.subtract(today.day() + 5, 'day');
          }
          targetDate = targetDate.subtract(i, 'week');
          previewDates.push(targetDate.format('YYYY-MM-DD'));
        }
        
        console.log(`📋 Would process these Tuesday dates:`);
        previewDates.forEach(date => console.log(`   - ${date}`));
        process.exit(0);
      }
      
      const result = await extractPaystubsMultipleWeeks(weeksBack);
      console.log(`\n🎉 Catch-up processing completed!`);
      console.log(`   📊 Results: ${result.successCount}/${result.totalWeeks} weeks successful`);
    }

  } catch (error) {
    console.error('🚨 Catch-up script failed:', error.message);
    process.exit(1);
  }
})();
