// React Query
import { useQuery } from '@tanstack/react-query';

// Utils
import { api } from '@/utils/api';

// Store
import { useAuthStore } from '@/store/authStore';

// Hooks
import useFeedback from '@/hooks/useFeedback';

interface UseSystemUsersProps {
  account_id?: number | string;
  roles?: number[];
  active_only?: boolean;
  client_id?: number | null;
}

interface SystemUser {
  id: number;
  name: string;
  last_name: string;
  email: string;
  title: string | null;
  avatar: string | null;
  role: number;
  last_access: string | null;
  created_at: string;
  account_id: number;
  account_name?: string;
  online: boolean;
}

const useSystemUsers = (props: UseSystemUsersProps = {}) => {
  const { account_id, roles = [1, 4], active_only = true, client_id } = props;

  // Store
  const { user } = useAuthStore();
  const currentAccountId = user?.account?.id || '';

  // Hooks
  const { Ntf } = useFeedback();

  //* -------------------------------------------------------------------------------------------------- */
  //* React Query

  const op = "GetSystemUsers";

  async function fetchData() {
    if (!currentAccountId) {
      Ntf('error', 'Error!', 'Account ID is missing. Please log in again.');
      return null;
    }

    // Ensure all required authentication parameters are present
    if (!user?.id || !user?.role || !currentAccountId) {
      console.warn('[GetSystemUsers] Missing user authentication data:', {
        user_id: user?.id,
        role: user?.role,
        user_account_id: currentAccountId
      });
      return [];
    }

    try {
      const requestPayload = {
        account_id: account_id || currentAccountId || 1,
        roles,
        active_only,
        client_id,
        user_id: user?.id,
        role: user?.role,
        user_account_id: currentAccountId
      };

      const res = await api(op, requestPayload);
      const { status, data } = res || {};

      console.log(`[${op}]`, data || res);

      if (status === 'error') {
        // Show error message
        Ntf('error', 'Error!', res?.message || 'Failed to fetch system users');
        throw new Error(`Failed to fetch ${op} data`);
      }

      return data || [];

    } catch (error) {
      console.error(`[${op}] Error fetching data:`, error);

      // Show error message
      Ntf('error', 'Error!', 'An error occurred while fetching system users. Please try again later.');
      return [];
    }
  }

  const { data: systemUsers, isFetching: isLoading, refetch } = useQuery<SystemUser[]>({
    queryKey: [op, account_id || 1, roles, active_only, client_id, currentAccountId],
    queryFn: async () => await fetchData(),
    staleTime: (1000 * 60) * 5, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: !!currentAccountId && !!user?.id && !!user?.role,
  });

  return {
    systemUsers: systemUsers || [],
    isLoading,
    refetch
  };
};

export default useSystemUsers; 
