export interface SystemUser {
  id: number;
  name: string;
  last_name: string;
  email: string;
  title: string | null;
  avatar: string | null;
  role: number;
  last_access: string | null;
  created_at: string;
  account_id: number;
  account_name?: string;
  online: boolean;
  phone?: string | null;
  active?: boolean;
  timezone?: string | null;
}

export interface UseSystemUsersProps {
  account_id?: number | string;
  roles?: number[];
  active_only?: boolean;
  client_id?: number | null;
}

export interface RoleConfig {
  label: string;
  color: string;
  icon: React.ReactElement;
  bgColor: string;
  borderColor: string;
} 
