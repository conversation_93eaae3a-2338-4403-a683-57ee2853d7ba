import React, { useState, useMemo } from 'react';

// UI Components
import {
  Card,
  Table,
  Avatar,
  Tag,
  Select,
  Space,
  Input,
  Button,
  Typography,
  Dropdown,
  Badge,
  Tooltip,
  Row,
  Col,
  Spin,
  Switch
} from 'antd';

// Icons
import {
  SearchOutlined,
  UserOutlined,
  CrownOutlined,
  TeamOutlined,
  SettingOutlined,
  FilterOutlined,
  ReloadOutlined,
  SwapOutlined,
  MailOutlined,
  PlusOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
  ExportOutlined,
  UserSwitchOutlined,
  EyeOutlined,
  DollarOutlined,
  FileTextOutlined
} from '@ant-design/icons';

// Hooks & Store
import { useAuthStore } from '@/store/authStore';
import useSystemUsers from './useSystemUsers';
import useSystemAccounts from './useSystemAccounts';
import useClients from '@/hooks/useClients';

// Router
import { useNavigate } from 'react-router';

// Modals
import { AddSystemUserModal } from './Modals';

// Components
import UserAvatar from '@/components/UserAvatar';

// Utils
import dayjs from '@/utils/dayjs';
import { Roles } from '@/utils/consts';

const { Title, Text } = Typography;
const { Search } = Input;

// Generate role configuration from constants, excluding roles 2 and 6
interface RoleConfig {
  label: string;
  color: string;
  icon: React.ReactNode;
  bgColor: string;
  borderColor: string;
}

const ROLE_CONFIG: Record<number, RoleConfig> = Roles.filter(role => ![2, 6].includes(role.id)).reduce((config, role) => {
  // Map role to appropriate icon
  const getIcon = (roleId: number) => {
    switch (roleId) {
      case 1: return <CrownOutlined />;
      case 3: return <UserSwitchOutlined />;
      case 4: return <SettingOutlined />;
      case 5: return <DollarOutlined />;
      case 7: return <EyeOutlined />;
      default: return <UserOutlined />;
    }
  };

  // Map role to appropriate background colors
  const getBgColor = (color: string) => {
    const colorMap: { [key: string]: string } = {
      '#722ed1': '#f9f0ff',
      '#52c41a': '#f6ffed',
      '#fa8c16': '#fff7e6',
      '#f5222d': '#fff1f0',
      '#8c8c8c': '#f5f5f5'
    };
    return colorMap[color] || '#f5f5f5';
  };

  const getBorderColor = (color: string) => {
    const colorMap: { [key: string]: string } = {
      '#722ed1': '#d3adf7',
      '#52c41a': '#b7eb8f',
      '#fa8c16': '#ffd591',
      '#f5222d': '#ffa39e',
      '#8c8c8c': '#d9d9d9'
    };
    return colorMap[color] || '#d9d9d9';
  };

  config[role.id] = {
    label: role.short_name || role.name,
    color: role.color,
    icon: getIcon(role.id),
    bgColor: getBgColor(role.color),
    borderColor: getBorderColor(role.color)
  };

  return config;
}, {} as Record<number, RoleConfig>);

const SystemUsers: React.FC = () => {
  const { user } = useAuthStore();
  const navigate = useNavigate();
  const [selectedAccount, setSelectedAccount] = useState(user?.account?.id || 1);
  const [searchText, setSearchText] = useState('');
  const [selectedRole, setSelectedRole] = useState<number | null>(null);
  const [selectedClient, setSelectedClient] = useState<number | null>(null);
  const [showActiveOnly, setShowActiveOnly] = useState(true); // Initially show only active users
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  const { systemUsers, isLoading, refetch } = useSystemUsers({
    account_id: selectedAccount,
    roles: [1, 3, 4, 5, 7], // Exclude roles 2 (Supervisor) and 6 (Employee)
    active_only: showActiveOnly,
    client_id: selectedClient
  });

  const { systemAccounts, isLoading: accountsLoading, hasRootAccess } = useSystemAccounts();

  // Get clients for filtering (only when temp_agency is true)
  const currentAccount = systemAccounts.find(acc => acc.id === selectedAccount);
  const isTemporaryAgency = currentAccount?.temp_agency || false;

  const { clients, clientsLoading } = useClients({
    account_id: selectedAccount
  });

  // Prepare clients data for dropdown
  const clientsData = Array.isArray(clients) ? clients : [];

  // Filter users based on search and role (client filtering is handled by the backend)
  const filteredUsers = useMemo(() => {
    if (!systemUsers) return [];

    return systemUsers.filter(userData => {
      const matchesSearch = !searchText ||
        `${userData.name} ${userData.last_name}`.toLowerCase().includes(searchText.toLowerCase()) ||
        userData.email.toLowerCase().includes(searchText.toLowerCase());

      const matchesRole = !selectedRole || userData.role === selectedRole;

      return matchesSearch && matchesRole;
    });
  }, [systemUsers, searchText, selectedRole]);

  // Table columns
  const columns = [
    {
      title: 'User',
      key: 'user',
      sorter: (a: any, b: any) => {
        const nameA = `${a.name} ${a.last_name}`.toLowerCase();
        const nameB = `${b.name} ${b.last_name}`.toLowerCase();
        return nameA.localeCompare(nameB);
      },
      defaultSortOrder: 'ascend' as const,
      render: (record: any) => (
        <Space size="small" className="py-1">
          <UserAvatar
            account_id={String(record.account_id)}
            id={String(record.id)}
            avatar={record.avatar}
            role={record.role}
            size={32}
            className="flex-shrink-0"
          />
          <div>
            <div className="fw-semibold text-dark mb-0">
              {record.name} {record.last_name}
            </div>
            <Text type="secondary" className="small d-flex align-items-center" style={{ marginTop: -3 }}>
              <MailOutlined className="me-1" style={{ fontSize: '12px' }} />
              {record.email}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      sorter: (a: any, b: any) => a.role - b.role,
      filters: Object.entries(ROLE_CONFIG).map(([roleId, config]) => ({
        text: config.label,
        value: Number(roleId)
      })),
      onFilter: (value: any, record: any) => record.role === value,
      render: (role: number) => {
        const config = ROLE_CONFIG[role as keyof typeof ROLE_CONFIG];
        return (
          <Tag
            icon={config?.icon}
            color={config?.color}
            className="px-3 py-1 rounded-pill border-0"
            style={{
              backgroundColor: config?.bgColor,
              color: config?.color,
              fontWeight: 500
            }}
          >
            {config?.label || 'Unknown'}
          </Tag>
        );
      },
    },
    {
      title: 'Account Access',
      key: 'access',
      sorter: (a: any, b: any) => {
        const accountA = a.account_name || '';
        const accountB = b.account_name || '';
        return accountA.localeCompare(accountB);
      },
      filters: systemAccounts.map(account => ({
        text: account.company_name,
        value: account.id,
      })),
      onFilter: (value: any, record: any) => record.account_id === value,
      render: (record: any) => (
        <div>
          {record.account_name && (
            <div className="fw-medium text-dark">
              {record.account_name}
            </div>
          )}
          <Badge
            style={{ marginTop: -3 }}
            status={record.role === 1 ? "processing" : "default"}
            text={
              <Text type="secondary" className="small">
                {record.role === 1 ? "Full access" : "Limited access"}
              </Text>
            }
          />
        </div>
      ),
    },
    {
      title: 'Last Active',
      dataIndex: 'last_access',
      key: 'last_access',
      sorter: (a: any, b: any) => {
        const dateA = a.last_access ? dayjs(a.last_access).valueOf() : 0;
        const dateB = b.last_access ? dayjs(b.last_access).valueOf() : 0;
        return dateB - dateA; // Most recent first
      },
      defaultSortOrder: 'ascend' as const,
      render: (lastAccess: string) => (
        <div>
          {lastAccess ? (
            <>
              <div className="text-dark">
                {dayjs(lastAccess).fromNow()}
              </div>
              <Text type="secondary" className="small" style={{ marginTop: -5 }}>
                {dayjs(lastAccess).format('MMM DD, YYYY')}
              </Text>
            </>
          ) : (
            <Text type="secondary">Never</Text>
          )}
        </div>
      ),
    },
    {
      title: 'Status',
      key: 'status',
      sorter: (a: any, b: any) => {
        const isOnlineA = a.last_access && dayjs().diff(dayjs(a.last_access), 'hours') < 24;
        const isOnlineB = b.last_access && dayjs().diff(dayjs(b.last_access), 'hours') < 24;
        return Number(isOnlineB) - Number(isOnlineA); // Active users first
      },
      filters: [
        { text: 'Active (within 24h)', value: true },
        { text: 'Inactive', value: false },
      ],
      onFilter: (value: any, record: any) => {
        const isOnline = record.last_access && dayjs().diff(dayjs(record.last_access), 'hours') < 24;
        return isOnline === value;
      },
      render: (record: any) => {
        const isOnline = record.last_access && dayjs().diff(dayjs(record.last_access), 'hours') < 24;
        return (
          <Badge
            status={isOnline ? "success" : "default"}
            text={
              <Text className={isOnline ? "text-success" : "text-muted"}>
                {isOnline ? "Active" : "Inactive"}
              </Text>
            }
          />
        );
      },
    }
  ];

  // Role filter options
  const roleFilterOptions = [
    { label: 'All Roles', value: null, icon: null },
    ...Object.entries(ROLE_CONFIG)
      .map(([roleId, config]) => ({
        label: config.label,
        value: Number(roleId),
        icon: config.icon
      }))
  ];

  return (
    <div className="system-users-page">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <Title level={2} className="mb-1">
            <UserOutlined className="me-2" />
            System Users
          </Title>
          <Text type="secondary">
            Manage system administrators and account managers
          </Text>
        </div>

        <Space>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'add-user',
                  label: 'Add New System User',
                  icon: <PlusOutlined />,
                  onClick: () => {
                    setIsAddModalOpen(true);
                  }
                },
                {
                  type: 'divider'
                },
                {
                  key: 'export',
                  label: 'Export Users',
                  icon: <ExportOutlined />,
                  disabled: true,
                  onClick: () => {
                    // Handle export functionality
                    console.log('Export users');
                  }
                },
                {
                  key: 'bulk-edit',
                  label: 'Bulk Operations',
                  icon: <EditOutlined />,
                  disabled: true,
                  onClick: () => {
                    // Handle bulk operations
                    console.log('Bulk operations');
                  }
                }
              ]
            }}
            placement="bottomRight"
            trigger={['click']}
          >
            <div className="fw-bold pointer mx-3 ns">
              <MoreOutlined size={20} className="me-1" />
              Operations
            </div>
          </Dropdown>

          <Button
            type="link"
            icon={<ReloadOutlined />}
            onClick={() => refetch()}
            loading={isLoading}
            title="Refresh"
          />
        </Space>
      </div>

      {/* Filters Card */}
      <Card className="mb-4 border-0 shadow-sm">
        <Row gutter={[16, 16]} align="middle">
          {/* Account Switcher - Only visible for root users */}
          {hasRootAccess && (
            <Col xs={24} sm={6} md={4}>
              <div>
                <Text strong className="d-block mb-2">
                  <SwapOutlined className="me-1" />
                  Account
                </Text>
                <Select
                  value={selectedAccount}
                  onChange={setSelectedAccount}
                  placeholder="Select Account"
                  className="w-100"
                  variant="filled"
                  loading={accountsLoading}
                  options={[
                    { label: 'All Accounts', value: 'all' },
                    ...systemAccounts.map(account => ({
                      label: account.company_name,
                      value: account.id
                    }))
                  ]}
                />
              </div>
            </Col>
          )}

          {/* Search */}
          <Col xs={24} sm={6} md={isTemporaryAgency ? 4 : 5}>
            <div>
              <Text strong className="d-block mb-2">
                <SearchOutlined className="me-1" />
                Search
              </Text>
              <Search
                placeholder="Search users..."
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                allowClear
                variant="filled"
              />
            </div>
          </Col>

          {/* Role Filter */}
          <Col xs={24} sm={6} md={isTemporaryAgency ? 3 : 4}>
            <div>
              <Text strong className="d-block mb-2">
                <FilterOutlined className="me-1" />
                Role
              </Text>
              <Select
                value={selectedRole}
                onChange={setSelectedRole}
                placeholder="Filter by role"
                className="w-100"
                variant="filled"
                allowClear
                options={roleFilterOptions.map(option => ({
                  ...option,
                  label: (
                    <Space>
                      {option.icon && option.icon}
                      {option.label}
                    </Space>
                  )
                }))}
              />
            </div>
          </Col>

          {/* Client Filter - Only show for temp agencies */}
          {isTemporaryAgency && (
            <Col xs={24} sm={6} md={4}>
              <div>
                <Text strong className="d-block mb-2">
                  <FilterOutlined className="me-1" />
                  Client
                </Text>
                <Select
                  value={selectedClient}
                  onChange={setSelectedClient}
                  placeholder="Filter by client"
                  className="w-100"
                  variant="filled"
                  allowClear
                  loading={clientsLoading}
                  options={[
                    { label: 'All Clients', value: null },
                    ...clientsData.map((client: any) => ({
                      label: client.name,
                      value: client.id
                    }))
                  ]}
                />
              </div>
            </Col>
          )}

          {/* Status Filter Switch */}
          <Col xs={24} sm={6} md={3}>
            <div>
              <Text strong className="d-block mb-2">Status</Text>
              <div className="d-flex align-items-center">
                <Switch
                  checked={showActiveOnly}
                  onChange={setShowActiveOnly}
                  size="small"
                />
                <Text className="ms-2 small">
                  {showActiveOnly ? 'Active only' : 'Show all'}
                </Text>
              </div>
            </div>
          </Col>

          {/* Stats Cards */}
          <Col xs={24} sm={24} md={isTemporaryAgency ? 6 : 8}>
            <div className="d-flex justify-content-end">
              <Space size="middle">
                <div className="text-center px-4 py-3 bg-light rounded">
                  <div className="fw-bold text-primary mb-1" style={{ fontSize: '20px' }}>
                    {filteredUsers.length}
                  </div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    Total Users
                  </Text>
                </div>
                <div className="text-center px-4 py-3 bg-light rounded">
                  <div className="fw-bold text-success mb-1" style={{ fontSize: '20px' }}>
                    {filteredUsers.filter(u =>
                      u.last_access && dayjs().diff(dayjs(u.last_access), 'hours') < 24
                    ).length}
                  </div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    Active Today
                  </Text>
                </div>
              </Space>
            </div>
          </Col>
        </Row>
      </Card>

      {/* Users Table */}
      <Card className="border-0 shadow-sm">
        <Table
          columns={columns}
          dataSource={filteredUsers}
          rowKey="id"
          loading={isLoading}
          onRow={(record) => ({
            onClick: () => navigate(`/settings/system-users/${record.id}`),
            style: { cursor: 'pointer' }
          })}
          pagination={filteredUsers.length > 10 ? {
            total: filteredUsers.length,
            pageSize: 10,
            position: ['bottomCenter'],
            showSizeChanger: true,
            showQuickJumper: false,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} users`,
            style: { textAlign: 'center' }
          } : false}
          className="modern-table"
          size="middle"
          scroll={{ x: 800 }}
          showSorterTooltip={{ title: 'Click to sort' }}
        />
      </Card>

      <style>{`
        .system-users-page .modern-table .ant-table-thead > tr > th {
          background: #fafafa;
          border-bottom: 2px solid #f0f0f0;
          font-weight: 600;
          padding: 12px 16px;
        }
        
        .system-users-page .modern-table .ant-table-tbody > tr:hover > td {
          background: #f8f9fa;
        }
        
        .system-users-page .modern-table .ant-table-tbody > tr > td {
          border-bottom: 1px solid #f5f5f5;
          padding: 8px 16px;
        }
        
        .system-users-page .modern-table .ant-table-tbody > tr {
          height: 56px;
        }
        
        .system-users-page .modern-table .ant-table-tbody > tr:hover {
          cursor: pointer;
        }
        
        .system-users-page .ant-pagination {
          text-align: center;
        }
      `}</style>

      {/* Add System User Modal */}
      <AddSystemUserModal
        open={isAddModalOpen}
        onCancel={() => setIsAddModalOpen(false)}
        onSuccess={() => {
          setIsAddModalOpen(false);
          refetch(); // Refresh the users list
        }}
      />
    </div>
  );
};

export default SystemUsers; 
