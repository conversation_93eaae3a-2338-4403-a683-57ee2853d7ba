import { getMenuItems } from '@/constants/Menu';

/**
 * Gets the original menu items without any conversion
 * This maintains full compatibility with the existing menu structure
 */
export const getOriginalMenuItems = (pathname: string) => {
  return getMenuItems(pathname);
};

/**
 * Finds the selected menu item based on current pathname
 * Uses the same logic as the original SideNav component
 */
export const findSelectedMenuItem = (items: any[], pathname: string): { selectedKey: string; openKeys: string[] } => {
  const normalize = (p: string) => p.replace(/^\/+|\/+$/g, '');
  const currentPath = normalize(pathname);

  const findMatch = (items: any[]): { selectedKey: string, groupKey: string } => {
    for (const item of items) {
      if (item.children) {
        for (const child of item.children) {
          const to = normalize((child.label as any)?.props?.to || '');
          if (currentPath === to) return { selectedKey: child.key, groupKey: item.key };
        }
      } else {
        const to = normalize((item.label as any)?.props?.to || '');
        if (currentPath === to) return { selectedKey: item.key, groupKey: '' };
      }
    }
    return { selectedKey: '', groupKey: '' };
  };

  const { selectedKey, groupKey } = findMatch(items);

  return {
    selectedKey,
    openKeys: groupKey ? [groupKey] : []
  };
};
