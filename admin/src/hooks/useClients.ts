
// NPM
import { useQuery } from '@tanstack/react-query';

// Utils
import { api } from '@/utils/api';

// ** Store
import { useAuthStore } from '@/store/authStore';

// ** Hooks
import useFeedback from './useFeedback';

const useClients = (payload: any = {

}) => {

  // ** Store
  const { user } = useAuthStore();
  const account_id = user?.account?.id || '';

  // ** Hooks
  const { Ntf } = useFeedback();

  //* -------------------------------------------------------------------------------------------------- */
  //* React Query

  const op = "GetClients";

  async function fetchData() {

    if (!account_id) {
      Ntf('error', 'Error!', 'Account ID is missing. Please log in again.');
      return null;
    }

    try {

      const res = await api(op, {
        ...payload,
        account_id,
      });
      const { status, data } = res || {};
      console.log(`[${op}]`, data || res);

      if (status === 'error') {

        // ** Show error message
        Ntf('error', 'Error!', res?.message || 'Failed to fetch data');
        throw new Error(`Failed to fetch ${op} data`);

      }

      return data || {};

    } catch (error) {

      console.error(`[${op}] Error fetching data:`, error);

      // ** Show error message:
      Ntf('error', 'Error!', 'An error occurred while fetching data. Please try again later.');
      return null;
    }
  }

  const { data, isFetching: clientsLoading } = useQuery({
    queryKey: [op, payload],
    queryFn: async () => await fetchData(),
    staleTime: (1000 * 60) * 5,
    refetchOnWindowFocus: false,
  });

  // Always return an array for clients
  const clients = Array.isArray(data) ? data : [];
  return { clients, clientsLoading }

}

export default useClients
