import { checkRedisHealth } from '../../config/redis.js';
import { payrollCache } from '../../utils/payroll/payrollCache.js';
import { refreshScheduler } from '../../jobs/refreshScheduler.js';
import { request } from '../../db/index.js';

/**
 * Redis Health Check Endpoint
 * 
 * Provides detailed information about Redis connection status,
 * performance metrics, and cache statistics.
 */
export async function GetRedisHealth(req, res) {
  try {
    console.log('🏥 [ENDPOINT] GetRedisHealth called');

    // Get Redis health information
    const redisHealth = await checkRedisHealth();

    // Get cache statistics
    const cacheStats = payrollCache.getStats();

    // Get basic system info
    const systemInfo = {
      node_version: process.version,
      uptime: process.uptime(),
      memory_usage: process.memoryUsage(),
      timestamp: new Date().toISOString()
    };

    const response = {
      status: 'success',
      data: {
        redis: redisHealth,
        cache: cacheStats,
        system: systemInfo,
        recommendations: generateRecommendations(redisHealth, cacheStats)
      }
    };

    console.log('✅ Redis health check completed:', {
      redis_status: redisHealth.status,
      cache_hit_rate: cacheStats.hitRate,
      memory_keys: cacheStats.memoryKeys
    });

    return res.status(200).json(response);

  } catch (error) {
    console.error('❌ Redis health check failed:', error);

    return res.status(500).json({
      status: 'error',
      message: 'Failed to check Redis health',
      error: error.message
    });
  }
}

/**
 * Generate performance recommendations based on health data
 */
function generateRecommendations(redisHealth, cacheStats) {
  const recommendations = [];

  // Redis connection recommendations
  if (redisHealth.status === 'disconnected') {
    recommendations.push({
      type: 'error',
      message: 'Redis is disconnected. Cache performance will be degraded.',
      action: 'Check Redis connection and credentials'
    });
  } else if (redisHealth.status === 'error') {
    recommendations.push({
      type: 'warning',
      message: 'Redis connection issues detected.',
      action: 'Monitor Redis logs and connection stability'
    });
  }

  // Cache hit rate recommendations
  const hitRate = parseFloat(cacheStats.hitRate?.replace('%', '') || 0);
  if (hitRate < 70) {
    recommendations.push({
      type: 'warning',
      message: `Cache hit rate is ${cacheStats.hitRate}, below optimal 70%+`,
      action: 'Consider increasing cache TTL or warming cache with common queries'
    });
  } else if (hitRate > 90) {
    recommendations.push({
      type: 'success',
      message: `Excellent cache hit rate: ${cacheStats.hitRate}`,
      action: 'Cache performance is optimal'
    });
  }

  // Memory usage recommendations
  if (cacheStats.memoryKeys > 8000) {
    recommendations.push({
      type: 'info',
      message: `High memory cache usage: ${cacheStats.memoryKeys} keys`,
      action: 'Monitor memory usage and consider cache cleanup'
    });
  }

  // Redis latency recommendations
  if (redisHealth.latency) {
    const latency = parseFloat(redisHealth.latency.replace('ms', ''));
    if (latency > 100) {
      recommendations.push({
        type: 'warning',
        message: `High Redis latency: ${redisHealth.latency}`,
        action: 'Check network connection to Upstash Redis'
      });
    }
  }

  return recommendations;
}

/**
 * Clear Cache Endpoint
 * 
 * Allows manual cache clearing for testing and maintenance
 */
export async function ClearCache(req, res) {
  try {
    console.log('🗑️ [ENDPOINT] ClearCache called');

    const { pattern } = req.body || {};

    if (pattern) {
      // Clear specific pattern
      await payrollCache.invalidate(pattern);
      console.log(`✅ Cache cleared for pattern: ${pattern}`);

      return res.status(200).json({
        status: 'success',
        message: `Cache cleared for pattern: ${pattern}`
      });
    } else {
      // Clear all cache
      await payrollCache.clear();
      console.log('✅ All cache cleared');

      return res.status(200).json({
        status: 'success',
        message: 'All cache cleared successfully'
      });
    }

  } catch (error) {
    console.error('❌ Cache clear failed:', error);

    return res.status(500).json({
      status: 'error',
      message: 'Failed to clear cache',
      error: error.message
    });
  }
}

/**
 * Cache Warm-up Endpoint
 * 
 * Pre-loads commonly accessed data into cache
 */
export async function WarmUpCache(req, res) {
  try {
    console.log('🔥 [ENDPOINT] WarmUpCache called');

    const { account_id } = req.body || {};

    if (!account_id) {
      return res.status(400).json({
        status: 'error',
        message: 'account_id is required for cache warm-up'
      });
    }

    // Warm up common queries
    const warmupTasks = [];
    const dateRange = {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
      end: new Date().toISOString().split('T')[0] // today
    };

    // Note: You would call your actual statistics functions here
    // This is a placeholder for the warm-up logic
    console.log(`🔥 Warming up cache for account ${account_id} (${dateRange.start} to ${dateRange.end})`);

    const results = await Promise.allSettled(warmupTasks);
    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;

    console.log(`✅ Cache warm-up completed: ${successful} successful, ${failed} failed`);

    return res.status(200).json({
      status: 'success',
      message: `Cache warm-up completed for account ${account_id}`,
      data: {
        successful_tasks: successful,
        failed_tasks: failed,
        date_range: dateRange
      }
    });

  } catch (error) {
    console.error('❌ Cache warm-up failed:', error);

    return res.status(500).json({
      status: 'error',
      message: 'Failed to warm up cache',
      error: error.message
    });
  }
}

/**
 * Materialized View Refresh Management
 */
export async function GetRefreshSchedule(req, res) {
  try {
    console.log('📅 [ENDPOINT] GetRefreshSchedule called');

    const stats = refreshScheduler.getStats();

    return res.status(200).json({
      status: 'success',
      data: {
        scheduler_stats: stats,
        current_time: new Date().toISOString(),
        timezone: 'America/New_York'
      }
    });

  } catch (error) {
    console.error('❌ Get refresh schedule failed:', error);

    return res.status(500).json({
      status: 'error',
      message: 'Failed to get refresh schedule',
      error: error.message
    });
  }
}

export async function TriggerManualRefresh(req, res) {
  try {
    console.log('🔧 [ENDPOINT] TriggerManualRefresh called');

    // Trigger manual refresh
    await refreshScheduler.triggerManualRefresh();

    return res.status(200).json({
      status: 'success',
      message: 'Manual materialized view refresh completed',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Manual refresh failed:', error);

    return res.status(500).json({
      status: 'error',
      message: 'Failed to trigger manual refresh',
      error: error.message
    });
  }
}

/**
 * Get System Users - Fetch users with specific roles and account access
 */
export async function GetSystemUsers(req, res) {
  const {
    account_id,
    roles = [1, 2, 3, 4, 5, 6, 7], // Default to all roles
    active_only = true,
    client_id,
    user_id,
    role,
    user_account_id
  } = req.body;

  if (!user_id || !role || !user_account_id) {
    return res.status(400).json({
      status: 'error',
      message: 'Missing required authentication parameters'
    });
  }

  // Check permissions - admin (role 1), account manager (role 4), or supervisor (role 2) can access
  if (![1, 2, 4].includes(role)) {
    return res.status(403).json({
      status: 'error',
      message: 'Insufficient permissions to access system users'
    });
  }

  // Handle null account_id - default to 1
  let targetAccountId = account_id || 1;

  // For non-root users, restrict to their own account
  if (role !== 1 || user_account_id !== 1) {
    targetAccountId = user_account_id || 1;
  }

  try {
    // Build the main query with optional client filtering
    let sql = `
      SELECT DISTINCT
        u.id,
        u.name,
        u.lastname as last_name,
        u.email,
        u.title,
        u.avatar,
        u.role,
        u.last_access,
        u.created_at,
        u.account_id,
        u.online,
        a.company_name as account_name
      FROM users u
      LEFT JOIN accounts a ON u.account_id = a.id
    `;

    // Add client filtering join if client_id is provided
    if (client_id) {
      sql += `
      LEFT JOIN user_clients uc ON u.id = uc.user_id
      `;
    }

    sql += ` WHERE u.role = ANY($1::int[])`;

    const params = [roles];

    // Add active filter if specified
    if (active_only) {
      sql += ' AND u.active = true';
    }

    // Add client filter if specified
    if (client_id) {
      sql += ` AND uc.client_id = $${params.length + 1}`;
      params.push(Number(client_id));
    }

    // Add account filter if specified
    if (targetAccountId && targetAccountId !== 'all') {
      sql += ` AND u.account_id = $${params.length + 1}`;
      params.push(Number(targetAccountId));
    }

    sql += ' ORDER BY u.last_access DESC NULLS LAST, u.created_at DESC';

    const result = await request(sql, params);
    const users = result || [];

    console.log(`[GetSystemUsers] Found ${users.length} users for account ${targetAccountId}`);

    return res.status(200).json({
      status: 'success',
      data: users,
      message: `Found ${users.length} system users`
    });

  } catch (error) {
    console.error('[GetSystemUsers] Database error:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Failed to fetch system users',
      error: error.message
    });
  }
}

/**
 * Get System Accounts - Fetch all accounts for the account switcher (root users only)
 */
export async function GetSystemAccounts(req, res) {
  const {
    user_id,
    role,
    account_id
  } = req.body;

  if (!user_id || !role || !account_id) {
    return res.status(400).json({
      status: 'error',
      message: 'Missing required authentication parameters'
    });
  }

  // Only root users (role 1 with account_id 1) can access all accounts
  if (role !== 1 || account_id !== 1) {
    return res.status(403).json({
      status: 'error',
      message: 'Insufficient permissions to access system accounts'
    });
  }

  try {
    const sql = `
      SELECT 
        a.id,
        a.company_name,
        a.temp_agency,
        COUNT(u.id) as user_count
      FROM accounts a
      LEFT JOIN users u ON a.id = u.account_id AND u.active = true AND u.role IN (1, 4)
      WHERE a.id IS NOT NULL
      GROUP BY a.id, a.company_name, a.temp_agency
      ORDER BY a.company_name ASC
    `;

    const result = await request(sql, []);
    const accounts = result || [];

    console.log(`[GetSystemAccounts] Found ${accounts.length} accounts`);

    return res.status(200).json({
      status: 'success',
      data: accounts,
      message: `Found ${accounts.length} accounts`
    });

  } catch (error) {
    console.error('[GetSystemAccounts] Database error:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Failed to fetch system accounts',
      error: error.message
    });
  }
}

/**
 * Add System User - Create a new system user with any available role
 */
export async function AddSystemUser(req, res) {
  let {
    name,
    lastname,
    email,
    title,
    role,
    password,
    account_id,
    user_id,
    user_role,
    user_account_id
  } = req.body;

  try {
    // **Security check - only roles 1, 2, and 4 can add system users
    if (![1, 2, 4].includes(user_role)) {
      return res.json({
        status: 'error',
        message: 'Insufficient permissions. Only Super Admin, Account Manager, and Supervisor roles can add system users.'
      });
    }

    // **Validation
    if (!name || !lastname || !email || !role || !password) {
      return res.json({
        status: 'error',
        message: 'Missing required fields: name, lastname, email, role, and password are required.'
      });
    }

    // **Role hierarchy validation - users can only create users at their level or below
    const roleHierarchy = {
      1: [1, 2, 3, 4, 5, 6, 7], // Super Admin can create any role
      4: [2, 3, 5, 6, 7],        // Account Manager can create below admin level
      2: [3, 5, 6, 7]            // Supervisor can create non-management roles
    };

    if (!roleHierarchy[user_role]?.includes(role)) {
      return res.json({
        status: 'error',
        message: 'Invalid role. You can only create users with roles at or below your authorization level.'
      });
    }

    // **Account restriction - non-root users can only create users in their account
    if (user_role !== 1 || user_account_id !== 1) {
      account_id = user_account_id;
    }

    // **Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.json({
        status: 'error',
        message: 'Invalid email address format.'
      });
    }

    // **Password validation
    if (password.length < 8) {
      return res.json({
        status: 'error',
        message: 'Password must be at least 8 characters long.'
      });
    }

    // **Check if email already exists
    const emailCheckQuery = `
      SELECT id FROM users 
      WHERE email = $1
    `;
    const emailCheckResult = await request(emailCheckQuery, [email]);

    if (emailCheckResult.length > 0) {
      return res.json({
        status: 'error',
        message: 'A user with this email address already exists.'
      });
    }

    // **Hash password
    const bcrypt = require('bcrypt');
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // **Insert new system user
    const insertQuery = `
      INSERT INTO users (
        name, 
        lastname, 
        email, 
        title,
        role, 
        password, 
        account_id,
        created_at,
        updated_at,
        active
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW(), true)
      RETURNING id, name, lastname, email, title, role, account_id, created_at
    `;

    const insertResult = await request(insertQuery, [
      name.trim(),
      lastname.trim(),
      email.toLowerCase().trim(),
      title ? title.trim() : null,
      role,
      hashedPassword,
      account_id
    ]);

    if (insertResult.length === 0) {
      return res.json({
        status: 'error',
        message: 'Failed to create system user. Please try again.'
      });
    }

    const newUser = insertResult[0];

    // **Log the action
    console.log(`[AddSystemUser] User ${user_id} created new system user ${newUser.id} (${newUser.email}) with role ${role} in account ${account_id}`);

    res.json({
      status: 'success',
      message: 'System user created successfully',
      data: {
        id: newUser.id,
        name: newUser.name,
        lastname: newUser.lastname,
        email: newUser.email,
        title: newUser.title,
        role: newUser.role,
        account_id: newUser.account_id,
        created_at: newUser.created_at
      }
    });

  } catch (error) {
    console.error('[AddSystemUser] Error:', error);
    res.json({
      status: 'error',
      message: 'An internal server error occurred. Please try again.'
    });
  }
}

/**
 * Get System User - Fetch individual system user by ID
 */
export async function GetSystemUser(req, res) {
  const {
    user_id,
    auth_user_id,
    auth_user_role,
    auth_user_account_id
  } = req.body;

  if (!auth_user_id || !auth_user_role || !auth_user_account_id) {
    return res.status(400).json({
      status: 'error',
      message: 'Missing required authentication parameters'
    });
  }

  if (!user_id) {
    return res.status(400).json({
      status: 'error',
      message: 'User ID is required'
    });
  }

  // Check permissions - admin (role 1), account manager (role 4), or supervisor (role 2) can access
  if (![1, 2, 4].includes(auth_user_role)) {
    return res.status(403).json({
      status: 'error',
      message: 'Insufficient permissions to access system user details'
    });
  }

  try {
    // Build the main query
    const sql = `
      SELECT 
        u.id,
        u.name,
        u.lastname as last_name,
        u.email,
        u.title,
        u.avatar,
        u.role,
        u.last_access,
        u.created_at,
        u.account_id,
        u.online,
        u.phone,
        u.active,
        u.timezone,
        u.signature,
        a.company_name as account_name
      FROM users u
      LEFT JOIN accounts a ON u.account_id = a.id
      WHERE u.id = $1 AND u.role IN (1, 2, 3, 4, 5, 6, 7)
    `;

    const result = await request(sql, [user_id]);

    if (!result || result.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'System user not found'
      });
    }

    const user = result[0];

    // For non-root users, restrict access to users in their own account
    if (auth_user_role !== 1 || auth_user_account_id !== 1) {
      if (user.account_id !== auth_user_account_id) {
        return res.status(403).json({
          status: 'error',
          message: 'Access denied to this user profile'
        });
      }
    }

    console.log(`[GetSystemUser] Found user ${user.id} for requesting user ${auth_user_id}`);

    return res.status(200).json({
      status: 'success',
      data: user,
      message: 'System user retrieved successfully'
    });

  } catch (error) {
    console.error('[GetSystemUser] Database error:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Failed to fetch system user',
      error: error.message
    });
  }
}

