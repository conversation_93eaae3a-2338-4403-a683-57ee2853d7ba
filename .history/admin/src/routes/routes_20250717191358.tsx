import React, { useEffect } from 'react';
import { createBrowserRouter, useLocation } from 'react-router';


// ** Layouts
import { DashboardLayout, LayoutRouter } from '../layouts';
import { ErrorPage } from '../pages';
import { Error404Page } from '@/pages/errors/Error404';


// ** Routes
import PublicRoute from './PublicRoute';
import ProtectedRoute from './ProtectedRoute';

// ** Pages
// ** |_Protected Routes
const Dashboard = React.lazy(() => import('@/pages/Dashboard'));

// Demo components removed - not available in current codebase

const Clients = React.lazy(() => import('@/pages/Clients'));
const Client = React.lazy(() => import('@/pages/Clients/Client'));

const Employees = React.lazy(() => import('@/pages/Employees'));
const Employee = React.lazy(() => import('@/pages/Employees/Employee'));

const Applications = React.lazy(() => import('@/pages/Applications'));
const ApplicationDetails = React.lazy(() => import('@/pages/Applications/Application'));

const Transportation = React.lazy(() => import('@/pages/Transportation'));

const Communications = React.lazy(() => import('@/pages/Communications'));
const SMSInbox = React.lazy(() => import('@/pages/Communications/SMS/Inbox'));
const BulkSMS = React.lazy(() => import('@/pages/Communications/SMS/BulkSMS'));
const EmailBulk = React.lazy(() => import('@/pages/Communications/EmailBulk'));

const Training = React.lazy(() => import('@/pages/Training'));

const Finance = React.lazy(() => import('@/pages/Finance/Invoices'));
const FinanceStatistics = React.lazy(() => import('@/pages/Finance/Statistics'));

const MobileApp = React.lazy(() => import('@/pages/AppDevices/MobileApp'));
const Timeclocks = React.lazy(() => import('@/pages/AppDevices/Timeclocks'));

// Settings pages removed - not available in current codebase

const Inbox = React.lazy(() => import('@/pages/Inbox'));

// ** Employee Dashboard (AppV2)
const EmployeeDashboard = React.lazy(() => import('@/pages/EmployeeDashboard'));

// ** |_Public Routes
const SignInPage = React.lazy(() => import('@/pages/authentication/SignIn'));
const Playground = React.lazy(() => import('@/pages/Public/Playground'));
const Policies = React.lazy(() => import('@/pages/Public/Policies'));
const ApplicationNew = React.lazy(() => import('@/pages/Public/Application'));

const ScrollToTop: React.FC = () => {
  const { pathname } = useLocation();
  useEffect(() => {
    window.scrollTo({ top: 0, left: 0, behavior: 'smooth' });
  }, [pathname]);
  return null;
};

const RouteWrapper = ({ children }: { children: React.ReactNode }) => (
  <>
    <ScrollToTop />
    {children}
  </>
);


export const router = createBrowserRouter([
  {
    path: '/',
    element: <ProtectedRoute />,
    children: [
      {
        path: '',
        errorElement: <ErrorPage />,
        element: <LayoutRouter />,
        children: [
          { index: true, element: <RouteWrapper><Dashboard /></RouteWrapper> },

          { path: 'inbox', element: <RouteWrapper><Inbox /></RouteWrapper> },

          { path: 'clients', element: <RouteWrapper><Clients /></RouteWrapper> },
          { path: 'clients/:id/:tab?', element: <RouteWrapper><Client /></RouteWrapper> },

          { path: 'employees', element: <RouteWrapper><Employees /></RouteWrapper> },
          { path: 'employees/:id/:tab?', element: <RouteWrapper><Employee /></RouteWrapper> },

          { path: 'applications', element: <RouteWrapper><Applications /></RouteWrapper> },
          { path: 'applications/:id', element: <RouteWrapper><ApplicationDetails /></RouteWrapper> },

          { path: 'trainings', element: <RouteWrapper><Training /></RouteWrapper> },

          { path: 'finance/invoices', element: <RouteWrapper><Finance /></RouteWrapper> },
          { path: 'finance/statistics', element: <RouteWrapper><FinanceStatistics /></RouteWrapper> },

          {
            path: 'communications',
            element: <RouteWrapper><Communications /></RouteWrapper>,
            children: [
              { index: true, element: <React.Suspense fallback={<div>Loading...</div>}><SMSInbox /></React.Suspense> },
              { path: 'bulk-sms', element: <React.Suspense fallback={<div>Loading...</div>}><BulkSMS /></React.Suspense> },
              { path: 'email-bulk', element: <React.Suspense fallback={<div>Loading...</div>}><EmailBulk /></React.Suspense> },
            ]
          },

          { path: 'app-devices/mobile-app/:tab?', element: <RouteWrapper><MobileApp /></RouteWrapper> },
          { path: 'app-devices/timeclocks', element: <RouteWrapper><Timeclocks /></RouteWrapper> },

          // Settings routes removed - components not available

          { path: 'transportation/:tab?', element: <RouteWrapper><Transportation /></RouteWrapper> },

        ],
      },
    ],
  },

  // Employee Dashboard with AppV2 Layout
  {
    path: '/employee-dashboard',
    element: <ProtectedRoute />,
    children: [
      {
        path: '',
        errorElement: <ErrorPage />,
        children: [
          { index: true, element: <RouteWrapper><EmployeeDashboard /></RouteWrapper> },
          { path: 'employee', element: <RouteWrapper><EmployeeDashboard /></RouteWrapper> },
          { path: 'employee-demo', element: <RouteWrapper><EmployeeDashboard /></RouteWrapper> },
          { path: 'hiring', element: <RouteWrapper><EmployeeDashboard /></RouteWrapper> },
          { path: 'payroll', element: <RouteWrapper><EmployeeDashboard /></RouteWrapper> },
          { path: 'performance', element: <RouteWrapper><EmployeeDashboard /></RouteWrapper> },
          { path: 'attendance', element: <RouteWrapper><EmployeeDashboard /></RouteWrapper> },
          { path: 'files', element: <RouteWrapper><EmployeeDashboard /></RouteWrapper> },
          { path: 'schedule', element: <RouteWrapper><EmployeeDashboard /></RouteWrapper> },
        ],
      },
    ],
  },

  // Enhanced layout demo removed - components not available

  // Legacy Dashboard Layout (if needed)
  {
    path: '/legacy-dashboard',
    element: <ProtectedRoute />,
    children: [
      {
        path: '',
        errorElement: <ErrorPage />,
        element: <DashboardLayout />,
        children: [
          { index: true, element: <RouteWrapper><Dashboard /></RouteWrapper> },
        ],
      },
    ],
  },

  {
    path: '/auth',
    errorElement: <ErrorPage />,
    children: [
      { path: 'signin', element: <RouteWrapper><SignInPage /></RouteWrapper> },
    ]
  },

  {
    path: '/playground',
    element: <PublicRoute />,
    children: [
      { index: true, element: <RouteWrapper><Playground /></RouteWrapper> },
    ]
  },

  {
    path: '/legal/:policy',
    element: <PublicRoute />,
    children: [
      { index: true, element: <RouteWrapper><Policies /></RouteWrapper> },
    ]
  },

  {
    path: '/apps/application/:id?',
    element: <PublicRoute />,
    children: [
      { index: true, element: <RouteWrapper><ApplicationNew /></RouteWrapper> },
    ]
  },

  {
    path: '*',
    element: <Error404Page />,
  },


]);

