import React, { useState, useRef, useEffect } from 'react';
import { useLocation, Outlet } from 'react-router';
import { Layout, FloatButton } from 'antd';
import { useMediaQuery } from 'react-responsive';

// Components
import { NProgress } from '@/components';
import ModalController from '../app/ModalController';
import FooterNav from '../app/FooterNav';

// AppV4 Components
import <PERSON>er<PERSON><PERSON> from './HeaderNav';
import SideNav from './SideNav';
import ThemeToggle from './ThemeToggle';

// Store & Utils
import { useThemeStore } from '@/store/themeStore';
import { ThemeManager } from '@/utils/themeUtils';
import { applyAppV4Theme } from './theme';

// Styles
import './AppV4.css';

const { Content } = Layout;

interface AppV4LayoutProps {
  children?: React.ReactNode;
}

export const AppV4Layout: React.FC<AppV4LayoutProps> = ({ children }) => {
  const location = useLocation();
  const isMobile = useMediaQuery({ maxWidth: 768 });
  const isTablet = useMediaQuery({ maxWidth: 1024 });

  // Refs
  const contentRef = useRef<HTMLDivElement>(null);
  const sidenavRef = useRef<HTMLDivElement>(null);

  // Theme store
  const {
    themeMode,
    layoutOptions,
    getCurrentThemeColor,
    collapsed,
    setCollapsed,
    mainLoading,
  } = useThemeStore();

  // Touch handling for mobile swipe gestures
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  // Generate theme-aware component styles
  const componentStyles = ThemeManager.generateComponentStyles(themeMode, getCurrentThemeColor());

  // Apply AppV4-specific theme variables
  useEffect(() => {
    applyAppV4Theme(themeMode);
  }, [themeMode]);

  // Swipe gesture handling
  const minSwipeDistance = 50;

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isRightSwipe && collapsed && isMobile) {
      setCollapsed(false);
    }
    if (isLeftSwipe && !collapsed && isMobile) {
      setCollapsed(true);
    }
  };

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobile && !collapsed && sidenavRef.current && !sidenavRef.current.contains(event.target as Node)) {
        setCollapsed(true);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isMobile, collapsed, setCollapsed]);

  // Auto-collapse sidebar on mobile
  useEffect(() => {
    if (isMobile && !collapsed) {
      setCollapsed(true);
    }
  }, [isMobile, collapsed, setCollapsed]);

  return (
    <div className="appv4-layout" data-theme={themeMode}>
      <NProgress isAnimating={mainLoading} key={location.key} />

      <Layout className="appv4-main-layout">
        {/* Header */}
        <HeaderNav
          isMobile={isMobile}
          isTablet={isTablet}
          collapsed={collapsed}
          onToggleCollapse={() => setCollapsed(!collapsed)}
          componentStyles={componentStyles}
          onTouchStart={onTouchStart}
          onTouchMove={onTouchMove}
          onTouchEnd={onTouchEnd}
        />

        <Layout className="appv4-content-layout">
          {/* Sidebar */}
          <SideNav
            ref={sidenavRef}
            isMobile={isMobile}
            collapsed={collapsed}
            layoutOptions={layoutOptions}
            componentStyles={componentStyles}
            themeMode={themeMode}
          />

          {/* Main Content */}
          <Layout
            className="appv4-main-content"
            style={{
              marginLeft: !isMobile ? (collapsed ? 80 : layoutOptions.sidebarWidth) : 0,
              transition: !isMobile ? 'margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1)' : 'none',
            }}
          >
            <Content
              ref={contentRef}
              className="appv4-content"
              style={{
                padding: isMobile ? '16px' : isTablet ? '20px 24px' : '24px 32px',
                minHeight: 'calc(100vh - 64px)',
                background: componentStyles.content.background,
                color: componentStyles.content.textColor,
              }}
            >
              {children || <Outlet />}
            </Content>

            <FooterNav
              style={{
                textAlign: 'center',
                background: 'transparent',
                padding: isMobile ? '12px 16px' : '16px 24px',
                color: componentStyles.content.textColor,
              }}
            />
          </Layout>
        </Layout>
      </Layout>

      {/* Floating Elements */}
      <FloatButton.BackTop
        style={{
          right: isMobile ? 16 : 24,
          bottom: isMobile ? 16 : 24,
          width: isMobile ? 40 : 48,
          height: isMobile ? 40 : 48,
        }}
      />

      <ThemeToggle />

      <ModalController />
    </div>
  );
};
