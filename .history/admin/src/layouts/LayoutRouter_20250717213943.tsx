import React, { useMemo } from 'react';
import { AppLayout } from './app';
import { AppV3Layout } from './appv3';

/**
 * Dynamic layout router that switches between different layout themes
 * based on user preference stored in localStorage
 */
export const LayoutRouter: React.FC = () => {
  const selectedLayout = useMemo(() => {
    return localStorage.getItem('vea-admin-layout') || 'app';
  }, []);

  // Return the appropriate layout component based on user preference
  switch (selectedLayout) {
    case 'appv3':
      return <AppV3Layout />;
    case 'app':
    default:
      return <AppLayout />;
  }
};

export default LayoutRouter;
