import React, { useState, useRef, useEffect, ReactNode } from 'react';
import { useLocation, useNavigate, Outlet } from 'react-router';
import {
  Button,
  FloatButton,
  Layout,
  theme,
} from 'antd';

import ModalController from './ModalController';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';

import { Logo } from '@/components';
import { useMediaQuery } from 'react-responsive';
import SideNav from './SideNav.tsx';
import HeaderNav from './HeaderNav';
import FooterNav from './FooterNav.tsx';
import { NProgress } from '../../components';
import { useThemeStore } from '@/store/themeStore';
import NavSearch from './NavSearch';
import User from './HeaderNav/User';

const { Content } = Layout;

export const AppLayout = () => {

  const { token: { borderRadius } } = theme.useToken();

  const location = useLocation();
  const navigate = useNavigate();
  const isMobile = useMediaQuery({ maxWidth: 769 });
  const isTablet = useMediaQuery({ maxWidth: 1024 });

  const nodeRef = useRef<HTMLDivElement>(null);
  const floatBtnRef = useRef<HTMLDivElement>(null);
  const sidenavRef = useRef<HTMLDivElement>(null);
  const isUpdatingStateRef = useRef(false);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  const {
    mytheme,
    collapsed,
    setCollapsed,
    mainLoading,
    _hasHydrated,
  } = useThemeStore();



  const width = window.innerWidth;

  // EMERGENCY FIX: Remove ALL automatic state management
  useEffect(() => {
    if (!_hasHydrated) return;
    console.log('✅ Store hydrated - NO automatic state changes');
  }, [_hasHydrated]);

  // Fallback hydration timeout (simplified)
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (!_hasHydrated) {
        console.warn('⚠️ Store hydration timeout, forcing hydration');
        useThemeStore.getState().setHasHydrated(true);
      }
    }, 1000); // Reduced to 1 second

    return () => clearTimeout(timeout);
  }, []); // Empty dependency array - run only once

  // Minimum swipe distance (in px)
  const minSwipeDistance = 50;

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isRightSwipe && collapsed && isMobile) {
      setCollapsed(false);
    }
    if (isLeftSwipe && !collapsed && isMobile) {
      setCollapsed(true);
    }
  };

  // DISABLED: Click outside detection (was causing state loops)
  // Will re-enable after fixing the core issue

  // DISABLED: Orientation change handler (was causing state loops)
  // Will re-enable after fixing the core issue

  // Show loading state during hydration to prevent layout flicker
  if (!_hasHydrated) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        background: '#f5f5f5'
      }}>
        <div>Loading theme...</div>
      </div>
    );
  }

  return (
    <>
      <NProgress isAnimating={mainLoading} key={location.key} />

      <HeaderNav
        style={{
          height: isMobile ? 50 : 55,
          padding: isMobile ? '0 8px' : '0 10px',
          background: mytheme === 'light' ? '#1a1a1a' : '#1A1A1A',
          position: 'sticky',
          top: 0,
          zIndex: 1000,
          boxShadow: isMobile ? '0 2px 8px rgba(0,0,0,0.15)' : 'none',
        }}
        className='w-100 d-flex align-items-center'
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
      >

        <div className="logo-container">
          <Logo
            color="white"
            asLink
            href="/"
            imgSize={{ w: isMobile ? 26 : 30, h: isMobile ? 26 : 30 }}
          />
        </div>

        <div className="w-100 d-flex align-items-center justify-content-between">
          <div className="d-flex align-items-center" style={{ flex: 1, minWidth: 0 }}>

            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              title={`Sidebar is ${collapsed ? 'collapsed' : 'expanded'} - Click to ${collapsed ? 'expand' : 'collapse'}`}
              onClick={() => {
                console.log('🔘 Toggle button clicked:', { from: collapsed, to: !collapsed });
                setCollapsed(!collapsed);
              }}
              data-sidebar-toggle="true"
              style={{
                fontSize: isMobile ? '14px' : '16px',
                width: isMobile ? 44 : 70,
                height: isMobile ? 44 : 70,
                color: '#fff',
                minWidth: isMobile ? 44 : 70,
                flexShrink: 0
              }}
            />

            <NavSearch isMobile={isMobile} />

          </div>

          <User borderRadius={borderRadius} navigate={navigate} isMobile={isMobile} />

        </div>

      </HeaderNav>

      <Layout
        style={{
          minHeight: '100vh',
          touchAction: 'pan-y pinch-zoom'
        }}
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
      >

        {/* Mobile Overlay & SideNav */}
        {isMobile && !collapsed && (
          <div
            className="mobile-sidenav-overlay"
            onClick={() => setCollapsed(true)}
            style={{
              animation: 'fadeIn 0.2s ease-out'
            }}
          >
            <div
              ref={sidenavRef}
              onClick={(e) => e.stopPropagation()}
              style={{
                transform: 'translateX(0)',
                transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                animation: 'slideInLeft 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
              }}
            >
              <SideNav
                trigger={null}
                collapsed={false}
                width={280}
                theme="light"
                style={{
                  position: 'fixed',
                  top: 0,
                  bottom: 0,
                  left: 0,
                  height: '100vh',
                  zIndex: 200,
                  background: '#ffffff',
                  backgroundColor: '#ffffff',
                  border: 'none',
                  boxShadow: '4px 0 12px rgba(0, 0, 0, 0.15)',
                  paddingTop: isMobile ? 50 : 60,
                }}
              />
            </div>
          </div>
        )}

        {/* Desktop SideNav */}
        {!isMobile && (
          <SideNav
            trigger={null}
            collapsible
            collapsed={collapsed}
            onCollapse={setCollapsed}
            width={240}
            collapsedWidth={80}
            theme={mytheme}
            style={{
              position: 'fixed',
              top: 55,
              bottom: 0,
              left: 0,
              zIndex: 100,
              background: '#EBEBEB',
              border: 'none',
              transition: 'all 0.2s ease',
            }}
          />
        )}

        {/* Main Content */}
        <Layout
          key="main-layout" // Stable key to prevent remounting
          style={{
            marginLeft: !isMobile ? (collapsed ? 80 : 240) : 0,
            transition: !isMobile ? 'margin-left 0.2s ease' : 'none'
          }}
        >

          <Content
            style={{
              padding: isMobile ? '12px' : (isTablet ? '20px 24px' : '24px 32px'),
              minHeight: 360,
              maxWidth: '100%',
              overflowX: 'hidden'
            }}
          >
            <div ref={nodeRef} style={{ width: '100%', minWidth: 0 }}>
              <Outlet />
            </div>
            <div ref={floatBtnRef}>
              <FloatButton.BackTop
                style={{
                  right: isMobile ? 16 : 24,
                  bottom: isMobile ? 16 : 24,
                  width: isMobile ? 40 : 48,
                  height: isMobile ? 40 : 48
                }}
              />
            </div>
          </Content>

          <FooterNav
            style={{
              textAlign: 'center',
              background: 'none',
              padding: isMobile ? '8px' : '16px'
            }}
          />
        </Layout>
      </Layout>

      <ModalController />

    </>
  );
};
