import { useEffect, useRef, useState, useMemo } from 'react';
import { Layout, Menu } from 'antd';
import type { MenuProps, SiderProps } from 'antd';
import { useLocation } from 'react-router';

import { getMenuItems } from '@/constants/Menu'; // renamed to pure function

const { Sider } = Layout;

type SideNavProps = SiderProps;

const SideNav = ({ ...others }: SideNavProps) => {

  const nodeRef = useRef(null);
  const { pathname } = useLocation();

  const items = useMemo(() => getMenuItems(pathname), [pathname]);

  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const [current, setCurrent] = useState<string>('');

  const rootSubmenuKeys = useMemo(
    () => items.filter(i => 'children' in i && Array.isArray((i as any).children)).map(i => i.key),
    [items]
  );

  const onOpenChange: MenuProps['onOpenChange'] = keys => {
    const latestOpenKey = keys.find(key => !openKeys.includes(key));
    if (latestOpenKey && rootSubmenuKeys.includes(latestOpenKey)) {
      setOpenKeys([latestOpenKey]);
    } else {
      setOpenKeys(keys);
    }
  };

  useEffect(() => {
    const normalize = (p: string) => p.replace(/^\/+|\/+$/g, '');
    const currentPath = normalize(pathname);

    const findMatch = (items: any[]): { selectedKey: string, groupKey: string } => {
      for (const item of items) {
        if (item.children) {
          for (const child of item.children) {
            const to = normalize((child.label as any)?.props?.to || '');
            if (currentPath === to) return { selectedKey: child.key, groupKey: item.key };
          }
        } else {
          const to = normalize((item.label as any)?.props?.to || '');
          if (currentPath === to) return { selectedKey: item.key, groupKey: '' };
        }
      }
      return { selectedKey: '', groupKey: '' };
    };

    const { selectedKey, groupKey } = findMatch(items);

    setCurrent(selectedKey);
    if (groupKey) setOpenKeys([groupKey]);
  }, [pathname, items]);

  return (
    <Sider ref={nodeRef} width={240} {...others}>

      <Menu
        mode="inline"
        openKeys={openKeys}
        onOpenChange={onOpenChange}
        selectedKeys={[current]}
        items={items as MenuProps['items']}
        style={{ border: 'none' }}
      />
    </Sider>
  );
};

export default SideNav;
