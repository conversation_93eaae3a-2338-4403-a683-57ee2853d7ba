// ** UI
import {
  Dropdown,
  Avatar,
  message,
} from 'antd';
import type { MenuProps } from 'antd';

// ** NPM
import Cookies from 'js-cookie';
import { useState } from 'react';

// ** Store
import { useAuthStore } from '@/store/authStore';

// ** Components
import { DisplaySettingsDrawer } from '@/components/DisplaySettingsDrawer';

// ** Utils
import { PRIMARY_COLOR } from '@/utils/consts';

// ** Icons
import {
  LogoutOutlined,
  SettingOutlined,
  UserOutlined,
  EyeOutlined,
} from '@ant-design/icons';


type Props = {
  borderRadius: number;
  navigate: (path: string) => void;
  isMobile?: boolean;
}

const User = ({ borderRadius, navigate, isMobile = false }: Props) => {

  // ** Store
  const { user, logout } = useAuthStore();

  const items: MenuProps['items'] = [
    {
      key: 'user-profile-link',
      label: 'profile',
      icon: <UserOutlined />,
    },
    {
      key: 'user-settings-link',
      label: 'settings',
      icon: <SettingOutlined />,
    },
    {
      type: 'divider',
    },
    {
      key: 'user-logout-link',
      label: 'logout',
      icon: <LogoutOutlined />,
      danger: true,
      onClick: () => {

        message.open({
          type: 'loading',
          content: 'signing you out',
        });

        setTimeout(() => {

          Cookies.remove('jwt');
          Cookies.remove('google_access_token');
          logout();

          message.destroy();
          message.success('signed out successfully');
          navigate('/auth/signin');

        }, 1000);

      },
    },
  ];


  return (
    <Dropdown
      menu={{ items }}
      trigger={['click']}
      placement={isMobile ? 'bottomRight' : 'bottomRight'}
    >

      <div
        className="d-flex align-items-center pointer pe-1 pe-lg-2"
        style={{
          lineHeight: 1,
          minHeight: isMobile ? 44 : 'auto',
          padding: isMobile ? '4px 8px' : '0',
          borderRadius: isMobile ? 8 : 0,
          minWidth: isMobile ? 44 : 'auto'
        }}
      >

        <div className={isMobile ? "d-block" : "d-none d-lg-block"}>
        {user?.avatar ?
            <img
              src="https://avatars.githubusercontent.com/u/33683226?v=4"
              alt="user profile photo"
              height={isMobile ? 24 : 28}
              width={isMobile ? 24 : 28}
              style={{ borderRadius, objectFit: 'cover' }}
            />
            : <Avatar size={isMobile ? 22 : 25} style={{ backgroundColor: PRIMARY_COLOR }} className='me-2'>
              {user?.name?.charAt(0).toUpperCase()}
            </Avatar>}
        </div>

        <div
          className={`text-light text-opacity-75 my-1 ${isMobile ? 'd-none d-sm-block' : ''}`}
          style={{
            fontSize: isMobile ? '14px' : '16px',
            marginLeft: isMobile ? 4 : 0,
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            maxWidth: isMobile ? '120px' : 'none'
          }}
        >
          {user?.name} <span className="fw-bold">{user?.last_name}</span>
        </div>

      </div>
    </Dropdown>
  )
}

export default User
