"use client"

import type React from "react"
import { useState, useRef, useEffect, useCallback } from "react"
import { useLocation } from "react-router"
import { ConfigProvider, theme } from "antd"
import { useMediaQuery } from "react-responsive"
import { useThemeStore } from "@/store/themeStore"
import { NProgress } from "@/components"
import ModalController from "@/layouts/app/ModalController"
import { Sidebar } from "./components/Sidebar"
import { HeaderBar } from "./components/HeaderBar"
import { AppContent } from "./components/AppContent"


const { defaultAlgorithm, darkAlgorithm } = theme

export const AppV3Layout: React.FC = () => {

  const location = useLocation()
  const isMobile = useMediaQuery({ maxWidth: 768 })

  const sidenavRef = useRef<HTMLDivElement>(null)
  const [isScrolled, setIsScrolled] = useState(false)

  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)

  const { mytheme, collapsed, setCollapsed, mainLoading } = useThemeStore()

  const [layoutState, setLayoutState] = useState({
    collapsed: isMobile ? true : false,
    selectedKeys: ["dashboard"],
    openKeys: [] as string[],
  })

  const minSwipeDistance = 50

  const handleToggle = useCallback(() => {
    setCollapsed(!collapsed)
  }, [collapsed, setCollapsed])

  const handleMenuSelect = useCallback((selectedKeys: string[]) => {
    setLayoutState((prev) => ({ ...prev, selectedKeys }))
  }, [])

  const handleOpenChange = useCallback((openKeys: string[]) => {
    setLayoutState((prev) => ({ ...prev, openKeys }))
  }, [])

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return

    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > minSwipeDistance
    const isRightSwipe = distance < -minSwipeDistance

    if (isRightSwipe && collapsed && isMobile) {
      setCollapsed(false)
    }
    if (isLeftSwipe && !collapsed && isMobile) {
      setCollapsed(true)
    }
  }

  // Scroll detection for header shadow
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobile && !collapsed && sidenavRef.current && !sidenavRef.current.contains(event.target as Node)) {
        setCollapsed(true)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [isMobile, collapsed, setCollapsed])

  // Auto-collapse on mobile
  useEffect(() => {
    if (isMobile) {
      setCollapsed(true)
    }
  }, [isMobile, setCollapsed])

  // Set body background to match container
  useEffect(() => {
    document.body.style.background = mytheme === "dark" ? "#111827" : "#f8f9fa"
    document.documentElement.style.background = mytheme === "dark" ? "#111827" : "#f8f9fa"

    return () => {
      document.body.style.background = ""
      document.documentElement.style.background = ""
    }
  }, [mytheme])

  // Modern Professional Theme with 8px Grid System
  const modernProfessionalTheme = {
    algorithm: mytheme === "dark" ? darkAlgorithm : defaultAlgorithm,
    token: {
      // Professional Color System with Project Primary Color
      colorPrimary: "#2E6454", // Project primary color
      colorSuccess: "#10B981",
      colorWarning: "#F59E0B",
      colorError: "#EF4444",
      colorInfo: "#2E6454",

      // Typography with Inter Font
      fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontSize: 14,
      fontSizeHeading1: 32,
      fontSizeHeading2: 24,
      fontSizeHeading3: 20,
      fontSizeHeading4: 16,
      fontSizeHeading5: 14,
      fontSizeSM: 12,
      fontSizeXS: 11,
      fontWeightStrong: 600,

      // 8px Grid Spacing System
      padding: 16,
      paddingXS: 8,
      paddingSM: 12,
      paddingLG: 24,
      paddingXL: 32,
      margin: 16,
      marginXS: 8,
      marginSM: 12,
      marginLG: 24,
      marginXL: 32,

      // Consistent Border Radius
      borderRadius: 8,
      borderRadiusLG: 12,
      borderRadiusSM: 6,
      borderRadiusXS: 4,

      // Subtle Box Shadows for Depth
      boxShadow: "0 2px 4px rgba(0, 0, 0, 0.05)",
      boxShadowSecondary: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
      boxShadowTertiary: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",

      // Container Gray Background System - Matching entire page
      colorBgContainer: mytheme === "dark" ? "#1f2937" : "#ffffff",
      colorBgElevated: mytheme === "dark" ? "#374151" : "#ffffff",
      colorBgLayout: mytheme === "dark" ? "#111827" : "#f8f9fa", // Container gray entire page background
      colorBgBase: mytheme === "dark" ? "#111827" : "#f8f9fa",
      colorBgSpotlight: mytheme === "dark" ? "#1f2937" : "#ffffff",

      // Professional Border Colors
      colorBorder: mytheme === "dark" ? "#374151" : "#e5e7eb",
      colorBorderSecondary: mytheme === "dark" ? "#4b5563" : "#f3f4f6",

      // High Contrast Text Colors (WCAG 2.1 AA)
      colorText: mytheme === "dark" ? "#f9fafb" : "#111827",
      colorTextSecondary: mytheme === "dark" ? "#d1d5db" : "#6b7280",
      colorTextTertiary: mytheme === "dark" ? "#9ca3af" : "#9ca3af",
      colorTextQuaternary: mytheme === "dark" ? "#6b7280" : "#d1d5db",

      // Subtle Interactive States
      colorFill: mytheme === "dark" ? "rgba(255, 255, 255, 0.05)" : "rgba(0, 0, 0, 0.02)",
      colorFillSecondary: mytheme === "dark" ? "rgba(255, 255, 255, 0.03)" : "rgba(0, 0, 0, 0.01)",
      colorFillTertiary: mytheme === "dark" ? "rgba(255, 255, 255, 0.02)" : "rgba(0, 0, 0, 0.005)",

      // Optimized Control Heights
      controlHeight: 40,
      controlHeightSM: 32,
      controlHeightLG: 48,

      // Professional Line Heights
      lineHeight: 1.5,
      lineHeightHeading1: 1.2,
      lineHeightHeading2: 1.3,
      lineHeightHeading3: 1.4,

      // Smooth Motion Design
      motionDurationSlow: "0.3s",
      motionDurationMid: "0.2s",
      motionDurationFast: "0.15s",
      motionEaseInOut: "cubic-bezier(0.4, 0, 0.2, 1)",
      motionEaseOut: "cubic-bezier(0, 0, 0.2, 1)",
      motionEaseIn: "cubic-bezier(0.4, 0, 1, 1)",
    },
    components: {
      Layout: {
        siderBg: mytheme === "dark" ? "#1f2937" : "#ffffff",
        headerBg: "#1e293b", // Keep header dark themed
        bodyBg: mytheme === "dark" ? "#111827" : "#f8f9fa", // Container gray entire page background
        footerBg: mytheme === "dark" ? "#1f2937" : "#f8f9fa",
        headerHeight: 64,
        headerPadding: "0 24px",
        triggerBg: mytheme === "dark" ? "#4b5563" : "#f3f4f6",
        triggerColor: mytheme === "dark" ? "#f9fafb" : "#374151",
      },
      Menu: {
        itemBg: "transparent",
        itemSelectedBg: mytheme === "dark" ? "rgba(46, 100, 84, 0.15)" : "rgba(46, 100, 84, 0.08)", // Background color only for active states
        itemHoverBg: mytheme === "dark" ? "#374151" : "#f1f5f9",
        itemActiveBg: mytheme === "dark" ? "rgba(46, 100, 84, 0.2)" : "rgba(46, 100, 84, 0.12)",
        itemSelectedColor: "#2E6454",
        itemColor: mytheme === "dark" ? "#f9fafb" : "#374151",
        itemHoverColor: mytheme === "dark" ? "#f9fafb" : "#111827",
        subMenuItemBg: "transparent",
        groupTitleColor: mytheme === "dark" ? "#9ca3af" : "#6b7280",
        iconSize: 18,
        itemHeight: 32, // Compact menu items (8-12px padding)
        collapsedIconSize: 32, // Larger icons when collapsed (30-32px)
        itemMarginBlock: 1,
        itemMarginInline: 8,
        itemPaddingInline: 10, // Reduced padding for compact design
        itemBorderRadius: 6,
        subMenuItemBorderRadius: 4,
        fontSize: 14,
        fontWeight: 500,
        // Remove all borders from selected/clicked sidebar items
        itemSelectedBorder: "none",
        itemActiveBorder: "none",
        itemHoverBorder: "none",
      },
      Button: {
        borderRadius: 8,
        controlHeight: 40,
        controlHeightSM: 32,
        controlHeightLG: 48,
        paddingInline: 16,
        paddingInlineSM: 12,
        paddingInlineLG: 24,
        fontWeight: 500,
        primaryShadow: "0 2px 4px rgba(59, 130, 246, 0.15)",
        defaultShadow: "0 2px 4px rgba(0, 0, 0, 0.05)",
      },
      Input: {
        borderRadius: 10, // Slightly more rounded for better header integration
        controlHeight: 42, // Bigger search bar
        controlHeightSM: 32,
        controlHeightLG: 40,
        paddingInline: 16, // More padding for better feel
        fontSize: 14,
        colorBgContainer: "#2d3748", // Slightly lighter than header for depth
        colorBorder: "#4a5568", // Subtle border
        colorBorderHover: "#718096",
        activeShadow: "0 0 0 3px rgba(46, 100, 84, 0.15)",
        hoverBorderColor: "#718096",
        colorText: "#f7fafc", // High contrast white text
        colorTextPlaceholder: "#a0aec0",
      },
      Card: {
        borderRadius: 8,
        boxShadow: "0 2px 4px rgba(0, 0, 0, 0.05)",
        headerBg: "transparent",
        bodyPadding: 24,
        paddingLG: 32,
      },
      Badge: {
        borderRadius: 6,
        fontWeight: 600,
        fontSize: 11,
        fontSizeSM: 10,
      },
      Avatar: {
        borderRadius: 8,
        fontSize: 14,
        fontWeight: 600,
      },
      Switch: {
        colorPrimary: "#2E6454", // Project primary color scheme
        colorPrimaryHover: "#1F4A3A", // Darker green for hover
        colorPrimaryActive: "#0F2A1D", // Even darker for active
        borderRadius: 12,
        trackBg: "#4b5563", // Dark track background matching header
        trackBgChecked: "#2E6454", // Green when checked
      },
      Dropdown: {
        colorBgElevated: mytheme === "dark" ? "#374151" : "#ffffff",
        borderRadius: 8,
        boxShadowSecondary: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
        paddingBlock: 8,
      },
      Tooltip: {
        borderRadius: 6,
        fontSize: 12,
        colorBgSpotlight: mytheme === "dark" ? "#374151" : "#1f2937",
      },
    },
  }

  return (
    <ConfigProvider theme={modernProfessionalTheme}>
      <NProgress isAnimating={mainLoading} key={location.key} />

      {/* Increased Max-width Container to 1400px or 85vw */}
      <div
        className="admin-layout-wrapper"
        style={{
          maxWidth: "min(1800px, 85vw)", // Better utilization on larger screens
          margin: "0 auto",
          minHeight: "100vh",
          background: modernProfessionalTheme.token.colorBgLayout, // Container gray entire page background
          position: "relative",
        }}
      >
        {/* CSS Grid Layout Structure with Responsive Breakpoints */}
        <div
          className="admin-layout"
          style={{
            display: "grid",
            gridTemplateRows: "64px 1fr",
            gridTemplateColumns: isMobile ? "1fr" : collapsed ? "64px 1fr" : "280px 1fr",
            minHeight: "100vh",
            transition: "grid-template-columns 300ms ease-in-out", // Smooth transitions for all interactive elements
          }}
        >
          {/* Mobile Overlay */}
          {isMobile && !collapsed && (
            <div
              className="position-fixed top-0 start-0 w-100 h-100"
              style={{
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                zIndex: 1000,
                backdropFilter: "blur(4px)",
              }}
              onClick={() => setCollapsed(true)}
              onTouchStart={onTouchStart}
              onTouchMove={onTouchMove}
              onTouchEnd={onTouchEnd}
            >
              <div
                ref={sidenavRef}
                onClick={(e) => e.stopPropagation()}
                style={{
                  transform: "translateX(0)",
                  transition: "transform 300ms ease-in-out",
                }}
              >
                <Sidebar
                  collapsed={false}
                  layoutState={layoutState}
                  onMenuSelect={handleMenuSelect}
                  onOpenChange={handleOpenChange}
                  theme={mytheme}
                  isMobile={true}
                />
              </div>
            </div>
          )}

          {/* Dark Themed Header */}
          <HeaderBar collapsed={collapsed} onToggle={handleToggle} isScrolled={isScrolled} />

          {/* Desktop Sidebar */}
          {!isMobile && (
            <Sidebar
              collapsed={collapsed}
              layoutState={layoutState}
              onMenuSelect={handleMenuSelect}
              onOpenChange={handleOpenChange}
              theme={mytheme}
              isMobile={false}
            />
          )}

          {/* Main Content with Natural Footer Flow */}
          <AppContent collapsed={collapsed} />
        </div>
      </div>

      <ModalController />
    </ConfigProvider>
  )
}
