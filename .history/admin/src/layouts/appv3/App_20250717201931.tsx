"use client"

import type React from "react"
import { useState, useRef, useEffect, useCallback } from "react"
import { useLocation } from "react-router"
import { Layout, ConfigProvider, theme } from "antd"
import { useMediaQuery } from "react-responsive"
import { useThemeStore } from "@/store/themeStore"
import { NProgress } from "@/components"
import ModalController from "@/layouts/app/ModalController"
import { Sidebar } from "./components/Sidebar"
import { HeaderBar } from "./components/HeaderBar"
import { AppContent } from "./components/AppContent"
import type { LayoutState } from "./types"

const { defaultAlgorithm, darkAlgorithm } = theme

export const AppV3Layout: React.FC = () => {
  const location = useLocation()
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const isTablet = useMediaQuery({ maxWidth: 1024 })
  const sidenavRef = useRef<HTMLDivElement>(null)

  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)

  const { mytheme, collapsed, setCollapsed, mainLoading } = useThemeStore()

  const [layoutState, setLayoutState] = useState<LayoutState>({
    collapsed: false,
    selectedKeys: ["dashboard"],
    openKeys: ["overview"],
  })

  const minSwipeDistance = 50

  const handleToggle = useCallback(() => {
    setCollapsed(!collapsed)
  }, [collapsed, setCollapsed])

  const handleMenuSelect = useCallback((selectedKeys: string[]) => {
    setLayoutState((prev) => ({ ...prev, selectedKeys }))
  }, [])

  const handleOpenChange = useCallback((openKeys: string[]) => {
    setLayoutState((prev) => ({ ...prev, openKeys }))
  }, [])

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return

    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > minSwipeDistance
    const isRightSwipe = distance < -minSwipeDistance

    if (isRightSwipe && collapsed && isMobile) {
      setCollapsed(false)
    }
    if (isLeftSwipe && !collapsed && isMobile) {
      setCollapsed(true)
    }
  }

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobile && !collapsed && sidenavRef.current && !sidenavRef.current.contains(event.target as Node)) {
        setCollapsed(true)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [isMobile, collapsed, setCollapsed])

  useEffect(() => {
    if (isMobile && !collapsed) {
      setCollapsed(true)
    }
  }, [isMobile, setCollapsed])

  // Modern 2024 Design System Theme
  const modernThemeConfig = {
    algorithm: mytheme === "dark" ? darkAlgorithm : defaultAlgorithm,
    token: {
      // Modern Color Palette
      colorPrimary: "#3B82F6", // Modern blue
      colorSuccess: "#10B981", // Modern green
      colorWarning: "#F59E0B", // Modern amber
      colorError: "#EF4444", // Modern red
      colorInfo: "#3B82F6",

      // Typography - Inter font system
      fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontSize: 14,
      fontSizeHeading1: 32,
      fontSizeHeading2: 24,
      fontSizeHeading3: 20,
      fontSizeHeading4: 18,
      fontSizeHeading5: 16,
      fontWeightStrong: 600,

      // Modern spacing system (16px base)
      padding: 16,
      paddingXS: 8,
      paddingSM: 12,
      paddingLG: 24,
      paddingXL: 32,
      margin: 16,
      marginXS: 8,
      marginSM: 12,
      marginLG: 24,
      marginXL: 32,

      // Modern border radius
      borderRadius: 12,
      borderRadiusLG: 16,
      borderRadiusSM: 8,
      borderRadiusXS: 6,

      // Modern shadows
      boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
      boxShadowSecondary: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
      boxShadowTertiary: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",

      // Modern color system
      colorBgContainer: mytheme === "dark" ? "#1F2937" : "#FFFFFF",
      colorBgElevated: mytheme === "dark" ? "#374151" : "#FFFFFF",
      colorBgLayout: mytheme === "dark" ? "#111827" : "#F9FAFB",
      colorBgBase: mytheme === "dark" ? "#111827" : "#FFFFFF",
      colorBgSpotlight: mytheme === "dark" ? "#1F2937" : "#F8FAFC",

      // Border colors
      colorBorder: mytheme === "dark" ? "#374151" : "#E5E7EB",
      colorBorderSecondary: mytheme === "dark" ? "#4B5563" : "#F3F4F6",

      // Text colors with proper contrast
      colorText: mytheme === "dark" ? "#F9FAFB" : "#111827",
      colorTextSecondary: mytheme === "dark" ? "#D1D5DB" : "#6B7280",
      colorTextTertiary: mytheme === "dark" ? "#9CA3AF" : "#9CA3AF",
      colorTextQuaternary: mytheme === "dark" ? "#6B7280" : "#D1D5DB",

      // Interactive colors
      colorFill: mytheme === "dark" ? "rgba(255, 255, 255, 0.08)" : "rgba(0, 0, 0, 0.04)",
      colorFillSecondary: mytheme === "dark" ? "rgba(255, 255, 255, 0.06)" : "rgba(0, 0, 0, 0.02)",
      colorFillTertiary: mytheme === "dark" ? "rgba(255, 255, 255, 0.04)" : "rgba(0, 0, 0, 0.01)",

      // Control heights
      controlHeight: 40,
      controlHeightSM: 32,
      controlHeightLG: 48,

      // Line heights for better readability
      lineHeight: 1.6,
      lineHeightHeading1: 1.2,
      lineHeightHeading2: 1.3,
      lineHeightHeading3: 1.4,
    },
    components: {
      Layout: {
        siderBg: mytheme === "dark" ? "#1F2937" : "#FFFFFF",
        headerBg: mytheme === "dark" ? "#1F2937" : "#FFFFFF",
        bodyBg: mytheme === "dark" ? "#111827" : "#F9FAFB",
        footerBg: mytheme === "dark" ? "#1F2937" : "#FFFFFF",
        headerHeight: isMobile ? 64 : 72,
        headerPadding: "0 24px",
        triggerBg: mytheme === "dark" ? "#374151" : "#F8FAFC",
        triggerColor: mytheme === "dark" ? "#F9FAFB" : "#111827",
      },
      Menu: {
        itemBg: "transparent",
        itemSelectedBg: mytheme === "dark" ? "rgba(59, 130, 246, 0.15)" : "rgba(59, 130, 246, 0.08)",
        itemHoverBg: mytheme === "dark" ? "rgba(255, 255, 255, 0.05)" : "rgba(0, 0, 0, 0.02)",
        itemActiveBg: mytheme === "dark" ? "rgba(59, 130, 246, 0.2)" : "rgba(59, 130, 246, 0.12)",
        itemSelectedColor: "#3B82F6",
        itemColor: mytheme === "dark" ? "#F9FAFB" : "#374151",
        itemHoverColor: mytheme === "dark" ? "#F9FAFB" : "#111827",
        subMenuItemBg: "transparent",
        groupTitleColor: mytheme === "dark" ? "#9CA3AF" : "#6B7280",
        iconSize: 20,
        itemHeight: 44,
        collapsedIconSize: 20,
        itemMarginBlock: 2,
        itemMarginInline: 8,
        itemPaddingInline: 16,
        itemBorderRadius: 8,
        subMenuItemBorderRadius: 6,
        fontSize: 14,
        fontWeight: 500,
      },
      Button: {
        borderRadius: 8,
        controlHeight: 40,
        controlHeightSM: 32,
        controlHeightLG: 48,
        paddingInline: 16,
        paddingInlineSM: 12,
        paddingInlineLG: 20,
        fontWeight: 500,
        primaryShadow: "0 2px 4px rgba(59, 130, 246, 0.2)",
        defaultShadow: "0 1px 2px rgba(0, 0, 0, 0.05)",
      },
      Input: {
        borderRadius: 8,
        controlHeight: 40,
        controlHeightSM: 32,
        controlHeightLG: 48,
        paddingInline: 12,
        fontSize: 14,
        colorBgContainer: mytheme === "dark" ? "#374151" : "#FFFFFF",
        colorBorder: mytheme === "dark" ? "#4B5563" : "#D1D5DB",
        colorBorderHover: mytheme === "dark" ? "#6B7280" : "#9CA3AF",
        activeShadow: "0 0 0 3px rgba(59, 130, 246, 0.1)",
      },
      Card: {
        borderRadius: 12,
        paddingLG: 24,
        headerBg: "transparent",
        colorBgContainer: mytheme === "dark" ? "#1F2937" : "#FFFFFF",
        colorBorderSecondary: mytheme === "dark" ? "#374151" : "#F3F4F6",
        boxShadowTertiary: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
      },
      Badge: {
        borderRadius: 12,
        fontWeight: 600,
        fontSize: 11,
        fontSizeSM: 10,
      },
      Avatar: {
        borderRadius: 8,
        fontSize: 14,
        fontWeight: 600,
      },
      Switch: {
        colorPrimary: "#3B82F6",
        colorPrimaryHover: "#2563EB",
        borderRadius: 12,
      },
      Dropdown: {
        colorBgElevated: mytheme === "dark" ? "#374151" : "#FFFFFF",
        borderRadius: 12,
        boxShadowSecondary: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
        paddingBlock: 8,
      },
      Tooltip: {
        borderRadius: 8,
        fontSize: 12,
        colorBgSpotlight: mytheme === "dark" ? "#374151" : "#1F2937",
      },
      FloatButton: {
        borderRadius: 16,
        boxShadow: "0 8px 25px rgba(0, 0, 0, 0.15)",
      },
      Breadcrumb: {
        fontSize: 14,
        itemColor: mytheme === "dark" ? "#9CA3AF" : "#6B7280",
        lastItemColor: mytheme === "dark" ? "#F9FAFB" : "#111827",
        linkColor: mytheme === "dark" ? "#D1D5DB" : "#6B7280",
        linkHoverColor: "#3B82F6",
        separatorColor: mytheme === "dark" ? "#6B7280" : "#9CA3AF",
      },
    },
  }

  return (
    <ConfigProvider theme={modernThemeConfig}>
      <NProgress isAnimating={mainLoading} key={location.key} />

      <Layout
        style={{
          minHeight: "100vh",
          background: modernThemeConfig.token.colorBgLayout,
          touchAction: "pan-y pinch-zoom",
        }}
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
      >
        {/* Mobile Overlay & Sidebar */}
        {isMobile && !collapsed && (
          <div
            style={{
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              zIndex: 1000,
              backdropFilter: "blur(4px)",
            }}
            onClick={() => setCollapsed(true)}
          >
            <div
              ref={sidenavRef}
              onClick={(e) => e.stopPropagation()}
              style={{
                transform: "translateX(0)",
                transition: "transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
              }}
            >
              <Sidebar
                collapsed={false}
                layoutState={layoutState}
                onMenuSelect={handleMenuSelect}
                onOpenChange={handleOpenChange}
                theme={mytheme}
                isMobile={true}
              />
            </div>
          </div>
        )}

        {/* Desktop Sidebar */}
        {!isMobile && (
          <Sidebar
            collapsed={collapsed}
            layoutState={layoutState}
            onMenuSelect={handleMenuSelect}
            onOpenChange={handleOpenChange}
            theme={mytheme}
            isMobile={false}
          />
        )}

        <Layout>
          <HeaderBar collapsed={collapsed} onToggle={handleToggle} />
          <AppContent collapsed={collapsed} />
        </Layout>
      </Layout>

      <ModalController />
    </ConfigProvider>
  )
}
