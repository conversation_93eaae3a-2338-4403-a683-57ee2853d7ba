"use client"

import type React from "react"
import { useState, useRef, useEffect, useCallback } from "react"
import { useLocation } from "react-router"
import { Layout, ConfigProvider, theme } from "antd"
import { useMediaQuery } from "react-responsive"
import { useThemeStore } from "@/store/themeStore"
import { NProgress } from "@/components"
import ModalController from "@/layouts/app/ModalController"
import { Sidebar } from "./components/Sidebar"
import { HeaderBar } from "./components/HeaderBar"
import { AppContent } from "./components/AppContent"
import type { LayoutState } from "./types"

const { defaultAlgorithm, darkAlgorithm } = theme

export const AppV3Layout: React.FC = () => {
  const location = useLocation()
  const isMobile = useMediaQuery({ maxWidth: 769 })
  const sidenavRef = useRef<HTMLDivElement>(null)

  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)

  const { mytheme, collapsed, setCollapsed, mainLoading } = useThemeStore()

  const [layoutState, setLayoutState] = useState<LayoutState>({
    collapsed: false,
    selectedKeys: ["dashboard-home"],
    openKeys: [],
  })

  // Minimum swipe distance (in px)
  const minSwipeDistance = 50

  const handleToggle = useCallback(() => {
    setCollapsed(!collapsed)
  }, [collapsed, setCollapsed])

  const handleMenuSelect = useCallback((selectedKeys: string[]) => {
    setLayoutState((prev) => ({ ...prev, selectedKeys }))
  }, [])

  const handleOpenChange = useCallback((openKeys: string[]) => {
    setLayoutState((prev) => ({ ...prev, openKeys }))
  }, [])

  // Touch handlers for mobile swipe
  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return

    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > minSwipeDistance
    const isRightSwipe = distance < -minSwipeDistance

    if (isRightSwipe && collapsed && isMobile) {
      setCollapsed(false)
    }
    if (isLeftSwipe && !collapsed && isMobile) {
      setCollapsed(true)
    }
  }

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobile && !collapsed && sidenavRef.current && !sidenavRef.current.contains(event.target as Node)) {
        setCollapsed(true)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [isMobile, collapsed, setCollapsed])

  // Auto-collapse sidebar on mobile orientation change
  useEffect(() => {
    const handleOrientationChange = () => {
      if (isMobile) {
        setCollapsed(true)
      }
    }

    window.addEventListener("orientationchange", handleOrientationChange)
    return () => window.removeEventListener("orientationchange", handleOrientationChange)
  }, [isMobile, setCollapsed])

  // Enhanced theme configuration for Ant Design
  const themeConfig = {
    algorithm: mytheme === "dark" ? darkAlgorithm : defaultAlgorithm,
    token: {
      colorPrimary: "#1890ff",
      colorSuccess: "#52c41a",
      colorWarning: "#faad14",
      colorError: "#ff4d4f",
      colorInfo: "#1890ff",
      borderRadius: 8,
      wireframe: false,
      // Typography
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
      fontSize: 14,
      fontSizeHeading1: 38,
      fontSizeHeading2: 30,
      fontSizeHeading3: 24,
      fontSizeHeading4: 20,
      fontSizeHeading5: 16,
      // Spacing
      padding: 16,
      paddingXS: 8,
      paddingSM: 12,
      paddingLG: 24,
      paddingXL: 32,
      // Colors based on theme
      colorBgContainer: mytheme === "dark" ? "#141414" : "#ffffff",
      colorBgElevated: mytheme === "dark" ? "#1f1f1f" : "#ffffff",
      colorBgLayout: mytheme === "dark" ? "#000000" : "#f5f5f5",
      colorBorder: mytheme === "dark" ? "#303030" : "#d9d9d9",
      colorBorderSecondary: mytheme === "dark" ? "#262626" : "#f0f0f0",
      colorText: mytheme === "dark" ? "rgba(255, 255, 255, 0.88)" : "rgba(0, 0, 0, 0.88)",
      colorTextSecondary: mytheme === "dark" ? "rgba(255, 255, 255, 0.65)" : "rgba(0, 0, 0, 0.65)",
      colorTextTertiary: mytheme === "dark" ? "rgba(255, 255, 255, 0.45)" : "rgba(0, 0, 0, 0.45)",
      // Box shadow
      boxShadow:
        mytheme === "dark"
          ? "0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)"
          : "0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)",
      boxShadowSecondary:
        mytheme === "dark"
          ? "0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)"
          : "0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)",
    },
    components: {
      Layout: {
        siderBg: mytheme === "dark" ? "#001529" : "#ffffff",
        headerBg: mytheme === "dark" ? "#001529" : "#ffffff",
        bodyBg: mytheme === "dark" ? "#000000" : "#f5f5f5",
        footerBg: mytheme === "dark" ? "#001529" : "#ffffff",
        headerHeight: isMobile ? 50 : 55,
        headerPadding: isMobile ? "0 8px" : "0 16px",
      },
      Menu: {
        itemBg: "transparent",
        itemSelectedBg: mytheme === "dark" ? "#1890ff20" : "#e6f7ff",
        itemHoverBg: mytheme === "dark" ? "#ffffff10" : "#f5f5f5",
        itemActiveBg: mytheme === "dark" ? "#1890ff30" : "#bae7ff",
        itemSelectedColor: mytheme === "dark" ? "#ffffff" : "#1890ff",
        itemColor: mytheme === "dark" ? "rgba(255, 255, 255, 0.88)" : "rgba(0, 0, 0, 0.88)",
        subMenuItemBg: "transparent",
        groupTitleColor: mytheme === "dark" ? "rgba(255, 255, 255, 0.67)" : "rgba(0, 0, 0, 0.45)",
        iconSize: 16,
        itemHeight: 40,
        collapsedIconSize: 16,
        itemMarginBlock: 4,
        itemMarginInline: 4,
        itemPaddingInline: 12,
      },
      Button: {
        borderRadius: 6,
        controlHeight: 32,
        paddingInline: 15,
        fontWeight: 400,
      },
      Input: {
        borderRadius: 6,
        controlHeight: 32,
        paddingInline: 11,
      },
      Card: {
        borderRadius: 8,
        paddingLG: 24,
        boxShadowTertiary:
          mytheme === "dark"
            ? "0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)"
            : "0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)",
      },
      Dropdown: {
        borderRadius: 8,
        boxShadowSecondary:
          mytheme === "dark"
            ? "0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)"
            : "0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)",
      },
      FloatButton: {
        borderRadius: 40,
        boxShadow: mytheme === "dark" ? "0 2px 8px rgba(0, 0, 0, 0.15)" : "0 2px 8px rgba(0, 0, 0, 0.15)",
      },
    },
  }

  return (
    <ConfigProvider theme={themeConfig}>
      <NProgress isAnimating={mainLoading} key={location.key} />

      <Layout
        className="min-vh-100"
        style={{
          touchAction: "pan-y pinch-zoom",
        }}
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
      >
        {/* Mobile Overlay & Sidebar */}
        {isMobile && !collapsed && (
          <div
            className="mobile-sidenav-overlay"
            onClick={() => setCollapsed(true)}
            style={{
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "rgba(0, 0, 0, 0.45)",
              zIndex: 150,
              animation: "fadeIn 0.2s ease-out",
            }}
          >
            <div
              ref={sidenavRef}
              onClick={(e) => e.stopPropagation()}
              style={{
                transform: "translateX(0)",
                transition: "transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                animation: "slideInLeft 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
              }}
            >
              <Sidebar
                collapsed={false}
                layoutState={layoutState}
                onMenuSelect={handleMenuSelect}
                onOpenChange={handleOpenChange}
                theme="light"
                isMobile={true}
              />
            </div>
          </div>
        )}

        {/* Desktop Sidebar */}
        {!isMobile && (
          <Sidebar
            collapsed={collapsed}
            layoutState={layoutState}
            onMenuSelect={handleMenuSelect}
            onOpenChange={handleOpenChange}
            theme={mytheme}
            isMobile={false}
          />
        )}

        <Layout>
          <HeaderBar collapsed={collapsed} onToggle={handleToggle} />
          <AppContent collapsed={collapsed} />
        </Layout>
      </Layout>

      <ModalController />
    </ConfigProvider>
  )
}
