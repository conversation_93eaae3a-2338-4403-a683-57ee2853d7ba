"use client"

import type React from "react"
import { useState, useRef, useEffect, useCallback } from "react"
import { useLocation } from "react-router"
import { ConfigProvider, theme } from "antd"
import { useMediaQuery } from "react-responsive"
import { useThemeStore } from "@/store/themeStore"
import { NProgress } from "@/components"
import ModalController from "@/layouts/app/ModalController"
import { Sidebar } from "./components/Sidebar"
import { HeaderBar } from "./components/HeaderBar"
import { AppContent } from "./components/AppContent"
import type { LayoutState } from "./types"

const { defaultAlgorithm, darkAlgorithm } = theme

export const AppV3Layout: React.FC = () => {
  const location = useLocation()
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const isTablet = useMediaQuery({ maxWidth: 1024 })
  const sidenavRef = useRef<HTMLDivElement>(null)

  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)

  const { mytheme, collapsed, setCollapsed, mainLoading } = useThemeStore()

  const [layoutState, setLayoutState] = useState<LayoutState>({
    collapsed: isTablet && !isMobile ? true : false,
    selectedKeys: ["dashboard"],
    openKeys: ["overview"],
  })

  const minSwipeDistance = 50

  const handleToggle = useCallback(() => {
    setCollapsed(!collapsed)
  }, [collapsed, setCollapsed])

  const handleMenuSelect = useCallback((selectedKeys: string[]) => {
    setLayoutState((prev) => ({ ...prev, selectedKeys }))
  }, [])

  const handleOpenChange = useCallback((openKeys: string[]) => {
    setLayoutState((prev) => ({ ...prev, openKeys }))
  }, [])

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return

    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > minSwipeDistance
    const isRightSwipe = distance < -minSwipeDistance

    if (isRightSwipe && collapsed && isMobile) {
      setCollapsed(false)
    }
    if (isLeftSwipe && !collapsed && isMobile) {
      setCollapsed(true)
    }
  }

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobile && !collapsed && sidenavRef.current && !sidenavRef.current.contains(event.target as Node)) {
        setCollapsed(true)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [isMobile, collapsed, setCollapsed])

  // Auto-collapse on tablet, expand on desktop
  useEffect(() => {
    if (isTablet && !isMobile) {
      setCollapsed(true)
    } else if (!isTablet && !isMobile) {
      setCollapsed(false)
    }
  }, [isTablet, isMobile, setCollapsed])

  // Premium Enterprise Theme Configuration
  const premiumEnterpriseTheme = {
    algorithm: mytheme === "dark" ? darkAlgorithm : defaultAlgorithm,
    token: {
      // Professional Color System
      colorPrimary: "#3B82F6",
      colorSuccess: "#10B981",
      colorWarning: "#F59E0B",
      colorError: "#EF4444",
      colorInfo: "#3B82F6",

      // Inter Font System with Typography Scale
      fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontSize: 14,
      fontSizeHeading1: 28,
      fontSizeHeading2: 24,
      fontSizeHeading3: 20,
      fontSizeHeading4: 16,
      fontSizeHeading5: 14,
      fontSizeSM: 12,
      fontSizeXS: 11,
      fontWeightStrong: 600,

      // Optimized Spacing System
      padding: 16,
      paddingXS: 8,
      paddingSM: 12,
      paddingLG: 20,
      paddingXL: 24,
      margin: 16,
      marginXS: 8,
      marginSM: 12,
      marginLG: 20,
      marginXL: 24,

      // Consistent 6px Border Radius
      borderRadius: 6,
      borderRadiusLG: 8,
      borderRadiusSM: 4,
      borderRadiusXS: 3,

      // Premium Shadows
      boxShadow: mytheme === "dark" ? "0 2px 8px rgba(0, 0, 0, 0.15)" : "0 2px 8px rgba(0, 0, 0, 0.06)",
      boxShadowSecondary: mytheme === "dark" ? "0 1px 3px rgba(0, 0, 0, 0.12)" : "0 1px 3px rgba(0, 0, 0, 0.04)",
      boxShadowTertiary: mytheme === "dark" ? "0 4px 12px rgba(0, 0, 0, 0.25)" : "0 4px 12px rgba(0, 0, 0, 0.08)",

      // High Contrast Colors for Accessibility
      colorBgContainer: mytheme === "dark" ? "#1a1a1a" : "#FFFFFF",
      colorBgElevated: mytheme === "dark" ? "#1e293b" : "#FFFFFF",
      colorBgLayout: mytheme === "dark" ? "#0f172a" : "#F8FAFC",
      colorBgBase: mytheme === "dark" ? "#0f172a" : "#FFFFFF",
      colorBgSpotlight: mytheme === "dark" ? "#1a1a1a" : "#FFFFFF",

      // Professional Border Colors
      colorBorder: mytheme === "dark" ? "#334155" : "#E2E8F0",
      colorBorderSecondary: mytheme === "dark" ? "#475569" : "#F1F5F9",

      // WCAG AA Compliant Text Colors
      colorText: mytheme === "dark" ? "#F8FAFC" : "#0F172A",
      colorTextSecondary: mytheme === "dark" ? "#CBD5E1" : "#475569",
      colorTextTertiary: mytheme === "dark" ? "#94A3B8" : "#64748B",
      colorTextQuaternary: mytheme === "dark" ? "#64748B" : "#94A3B8",

      // Subtle Interactive States
      colorFill: mytheme === "dark" ? "rgba(248, 250, 252, 0.06)" : "rgba(15, 23, 42, 0.02)",
      colorFillSecondary: mytheme === "dark" ? "rgba(248, 250, 252, 0.04)" : "rgba(15, 23, 42, 0.01)",
      colorFillTertiary: mytheme === "dark" ? "rgba(248, 250, 252, 0.02)" : "rgba(15, 23, 42, 0.005)",

      // Optimized Control Heights
      controlHeight: 36,
      controlHeightSM: 28,
      controlHeightLG: 44,

      // Professional Line Heights
      lineHeight: 1.5,
      lineHeightHeading1: 1.2,
      lineHeightHeading2: 1.3,
      lineHeightHeading3: 1.4,

      // Motion Design
      motionDurationSlow: "0.3s",
      motionDurationMid: "0.2s",
      motionDurationFast: "0.1s",
    },
    components: {
      Layout: {
        siderBg: mytheme === "dark" ? "#1a1a1a" : "#FFFFFF",
        headerBg: mytheme === "dark" ? "#1e293b" : "#FFFFFF",
        bodyBg: mytheme === "dark" ? "#0f172a" : "#F8FAFC",
        footerBg: mytheme === "dark" ? "#374151" : "#F8FAFC",
        headerHeight: 64,
        headerPadding: "0 24px",
        triggerBg: mytheme === "dark" ? "#475569" : "#F1F5F9",
        triggerColor: mytheme === "dark" ? "#F8FAFC" : "#0F172A",
      },
      Menu: {
        itemBg: "transparent",
        itemSelectedBg: mytheme === "dark" ? "rgba(59, 130, 246, 0.15)" : "rgba(59, 130, 246, 0.08)",
        itemHoverBg: mytheme === "dark" ? "#475569" : "rgba(15, 23, 42, 0.04)",
        itemActiveBg: mytheme === "dark" ? "rgba(59, 130, 246, 0.2)" : "rgba(59, 130, 246, 0.12)",
        itemSelectedColor: "#3B82F6",
        itemColor: mytheme === "dark" ? "#F8FAFC" : "#0F172A",
        itemHoverColor: mytheme === "dark" ? "#F8FAFC" : "#0F172A",
        subMenuItemBg: "transparent",
        groupTitleColor: mytheme === "dark" ? "#94A3B8" : "#64748B",
        iconSize: 16,
        itemHeight: 32,
        collapsedIconSize: 16,
        itemMarginBlock: 1,
        itemMarginInline: 6,
        itemPaddingInline: 10,
        itemBorderRadius: 6,
        subMenuItemBorderRadius: 4,
        fontSize: 13,
        fontWeight: 500,
      },
      Button: {
        borderRadius: 6,
        controlHeight: 36,
        controlHeightSM: 28,
        controlHeightLG: 44,
        paddingInline: 16,
        paddingInlineSM: 12,
        paddingInlineLG: 20,
        fontWeight: 500,
        primaryShadow: mytheme === "dark" ? "0 2px 4px rgba(59, 130, 246, 0.25)" : "0 2px 4px rgba(59, 130, 246, 0.15)",
        defaultShadow: mytheme === "dark" ? "0 1px 3px rgba(0, 0, 0, 0.12)" : "0 1px 3px rgba(0, 0, 0, 0.04)",
      },
      Input: {
        borderRadius: 6,
        controlHeight: 40,
        controlHeightSM: 32,
        controlHeightLG: 48,
        paddingInline: 16,
        fontSize: 14,
        colorBgContainer: mytheme === "dark" ? "#334155" : "#FFFFFF",
        colorBorder: mytheme === "dark" ? "#475569" : "#CBD5E1",
        colorBorderHover: mytheme === "dark" ? "#64748B" : "#94A3B8",
        activeShadow: "0 0 0 2px rgba(59, 130, 246, 0.2)",
        hoverBorderColor: mytheme === "dark" ? "#64748B" : "#94A3B8",
      },
      Card: {
        borderRadius: 6,
        boxShadow: mytheme === "dark" ? "0 2px 8px rgba(0, 0, 0, 0.15)" : "0 2px 8px rgba(0, 0, 0, 0.06)",
        headerBg: "transparent",
        bodyPadding: 20,
        paddingLG: 24,
      },
      Badge: {
        borderRadius: 6,
        fontWeight: 600,
        fontSize: 11,
        fontSizeSM: 10,
      },
      Avatar: {
        borderRadius: 6,
        fontSize: 13,
        fontWeight: 600,
      },
      Switch: {
        colorPrimary: "#3B82F6",
        colorPrimaryHover: "#2563EB",
        borderRadius: 12,
      },
      Dropdown: {
        colorBgElevated: mytheme === "dark" ? "#334155" : "#FFFFFF",
        borderRadius: 8,
        boxShadowSecondary: mytheme === "dark" ? "0 4px 12px rgba(0, 0, 0, 0.25)" : "0 4px 12px rgba(0, 0, 0, 0.08)",
        paddingBlock: 8,
      },
      Tooltip: {
        borderRadius: 6,
        fontSize: 12,
        colorBgSpotlight: mytheme === "dark" ? "#334155" : "#1F2937",
      },
    },
  }

  return (
    <ConfigProvider theme={premiumEnterpriseTheme}>
      <NProgress isAnimating={mainLoading} key={location.key} />

      {/* 1600px Max-width Container with Auto Centering */}
      <div
        className="admin-layout-wrapper"
        style={{
          maxWidth: 1600,
          margin: "0 auto",
          minHeight: "100vh",
          background: premiumEnterpriseTheme.token.colorBgLayout,
        }}
      >
        <div
          className="admin-layout d-grid"
          style={{
            minHeight: "100vh",
            gridTemplateRows: "64px 1fr auto",
            gridTemplateColumns: isMobile ? "1fr" : collapsed ? "64px 1fr" : "240px 1fr",
            transition: "all 200ms ease",
          }}
        >
          {/* Mobile Overlay */}
          {isMobile && !collapsed && (
            <div
              className="position-fixed top-0 start-0 w-100 h-100"
              style={{
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                zIndex: 1000,
                backdropFilter: "blur(4px)",
              }}
              onClick={() => setCollapsed(true)}
              onTouchStart={onTouchStart}
              onTouchMove={onTouchMove}
              onTouchEnd={onTouchEnd}
            >
              <div
                ref={sidenavRef}
                onClick={(e) => e.stopPropagation()}
                style={{
                  transform: "translateX(0)",
                  transition: "transform 200ms ease",
                }}
              >
                <Sidebar
                  collapsed={false}
                  layoutState={layoutState}
                  onMenuSelect={handleMenuSelect}
                  onOpenChange={handleOpenChange}
                  theme={mytheme}
                  isMobile={true}
                />
              </div>
            </div>
          )}

          {/* Sticky Header */}
          <HeaderBar collapsed={collapsed} onToggle={handleToggle} />

          {/* Desktop Sidebar */}
          {!isMobile && (
            <Sidebar
              collapsed={collapsed}
              layoutState={layoutState}
              onMenuSelect={handleMenuSelect}
              onOpenChange={handleOpenChange}
              theme={mytheme}
              isMobile={false}
            />
          )}

          {/* Main Content */}
          <AppContent collapsed={collapsed} />

          {/* Enhanced Footer */}
          <footer
            className="admin-footer text-center"
            style={{
              gridColumn: "1 / -1",
              background: mytheme === "dark" ? "#374151" : "#F8FAFC",
              borderTop: `1px solid ${mytheme === "dark" ? "#475569" : "#E2E8F0"}`,
              padding: "20px 24px",
              color: mytheme === "dark" ? "#94A3B8" : "#64748B",
              fontSize: "13px",
              fontWeight: 500,
              transition: "all 200ms ease",
            }}
          >
            © 2024 WorkFlow Pro Enterprise. All rights reserved. | Version 3.2.1
          </footer>
        </div>
      </div>

      <ModalController />
    </ConfigProvider>
  )
}
