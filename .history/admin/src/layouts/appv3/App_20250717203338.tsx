"use client"

import type React from "react"
import { useState, useRef, useEffect, useCallback } from "react"
import { useLocation } from "react-router"
import { ConfigProvider, theme } from "antd"
import { useMediaQuery } from "react-responsive"
import { useThemeStore } from "@/store/themeStore"
import { NProgress } from "@/components"
import ModalController from "@/layouts/app/ModalController"
import { Sidebar } from "./components/Sidebar"
import { HeaderBar } from "./components/HeaderBar"
import { AppContent } from "./components/AppContent"
import type { LayoutState } from "./types"

const { defaultAlgorithm, darkAlgorithm } = theme

export const AppV3Layout: React.FC = () => {
  const location = useLocation()
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const isTablet = useMediaQuery({ maxWidth: 1024 })
  const sidenavRef = useRef<HTMLDivElement>(null)

  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)

  const { mytheme, collapsed, setCollapsed, mainLoading } = useThemeStore()

  const [layoutState, setLayoutState] = useState<LayoutState>({
    collapsed: isTablet && !isMobile ? true : false,
    selectedKeys: ["dashboard"],
    openKeys: ["overview"],
  })

  const minSwipeDistance = 50

  const handleToggle = useCallback(() => {
    setCollapsed(!collapsed)
  }, [collapsed, setCollapsed])

  const handleMenuSelect = useCallback((selectedKeys: string[]) => {
    setLayoutState((prev) => ({ ...prev, selectedKeys }))
  }, [])

  const handleOpenChange = useCallback((openKeys: string[]) => {
    setLayoutState((prev) => ({ ...prev, openKeys }))
  }, [])

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return

    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > minSwipeDistance
    const isRightSwipe = distance < -minSwipeDistance

    if (isRightSwipe && collapsed && isMobile) {
      setCollapsed(false)
    }
    if (isLeftSwipe && !collapsed && isMobile) {
      setCollapsed(true)
    }
  }

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobile && !collapsed && sidenavRef.current && !sidenavRef.current.contains(event.target as Node)) {
        setCollapsed(true)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [isMobile, collapsed, setCollapsed])

  // Auto-collapse on tablet, expand on desktop
  useEffect(() => {
    if (isTablet && !isMobile) {
      setCollapsed(true)
    } else if (!isTablet && !isMobile) {
      setCollapsed(false)
    }
  }, [isTablet, isMobile, setCollapsed])

  // Premium 2024 Design System
  const premiumThemeConfig = {
    algorithm: mytheme === "dark" ? darkAlgorithm : defaultAlgorithm,
    token: {
      // Modern Color System
      colorPrimary: "#3B82F6",
      colorSuccess: "#10B981",
      colorWarning: "#F59E0B",
      colorError: "#EF4444",
      colorInfo: "#3B82F6",

      // Inter Font System
      fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontSize: 14,
      fontSizeHeading1: 28,
      fontSizeHeading2: 22,
      fontSizeHeading3: 18,
      fontSizeHeading4: 16,
      fontSizeHeading5: 14,
      fontWeightStrong: 600,

      // Consistent Spacing System
      padding: 16,
      paddingXS: 8,
      paddingSM: 12,
      paddingLG: 24,
      paddingXL: 32,
      margin: 16,
      marginXS: 8,
      marginSM: 12,
      marginLG: 24,
      marginXL: 32,

      // Modern Border Radius
      borderRadius: 8,
      borderRadiusLG: 12,
      borderRadiusSM: 6,
      borderRadiusXS: 4,

      // Subtle Shadows
      boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
      boxShadowSecondary: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
      boxShadowTertiary: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",

      // Premium Color Palette
      colorBgContainer: mytheme === "dark" ? "#1F2937" : "#FFFFFF",
      colorBgElevated: mytheme === "dark" ? "#374151" : "#FFFFFF",
      colorBgLayout: mytheme === "dark" ? "#111827" : "#F9FAFB",
      colorBgBase: mytheme === "dark" ? "#111827" : "#FFFFFF",
      colorBgSpotlight: mytheme === "dark" ? "#1F2937" : "#F9FAFB",

      // Border Colors
      colorBorder: mytheme === "dark" ? "#374151" : "#E5E7EB",
      colorBorderSecondary: mytheme === "dark" ? "#4B5563" : "#F3F4F6",

      // Text Colors with Proper Contrast
      colorText: mytheme === "dark" ? "#F9FAFB" : "#111827",
      colorTextSecondary: mytheme === "dark" ? "#D1D5DB" : "#6B7280",
      colorTextTertiary: mytheme === "dark" ? "#9CA3AF" : "#9CA3AF",
      colorTextQuaternary: mytheme === "dark" ? "#6B7280" : "#D1D5DB",

      // Interactive States
      colorFill: mytheme === "dark" ? "rgba(255, 255, 255, 0.06)" : "rgba(0, 0, 0, 0.02)",
      colorFillSecondary: mytheme === "dark" ? "rgba(255, 255, 255, 0.04)" : "rgba(0, 0, 0, 0.01)",
      colorFillTertiary: mytheme === "dark" ? "rgba(255, 255, 255, 0.02)" : "rgba(0, 0, 0, 0.005)",

      // Control Heights
      controlHeight: 40,
      controlHeightSM: 32,
      controlHeightLG: 48,

      // Line Heights
      lineHeight: 1.5,
      lineHeightHeading1: 1.2,
      lineHeightHeading2: 1.3,
      lineHeightHeading3: 1.4,
    },
    components: {
      Layout: {
        siderBg: mytheme === "dark" ? "#1F2937" : "#F9FAFB",
        headerBg: mytheme === "dark" ? "#1F2937" : "#FFFFFF",
        bodyBg: mytheme === "dark" ? "#111827" : "#F9FAFB",
        footerBg: mytheme === "dark" ? "#1F2937" : "#FFFFFF",
        headerHeight: 64,
        headerPadding: "0 24px",
        triggerBg: mytheme === "dark" ? "#374151" : "#F3F4F6",
        triggerColor: mytheme === "dark" ? "#F9FAFB" : "#111827",
      },
      Menu: {
        itemBg: "transparent",
        itemSelectedBg: mytheme === "dark" ? "rgba(59, 130, 246, 0.1)" : "#EFF6FF",
        itemHoverBg: mytheme === "dark" ? "rgba(255, 255, 255, 0.05)" : "#F3F4F6",
        itemActiveBg: mytheme === "dark" ? "rgba(59, 130, 246, 0.15)" : "#EFF6FF",
        itemSelectedColor: mytheme === "dark" ? "#60A5FA" : "#1D4ED8",
        itemColor: mytheme === "dark" ? "#F9FAFB" : "#111827",
        itemHoverColor: mytheme === "dark" ? "#F9FAFB" : "#111827",
        subMenuItemBg: "transparent",
        groupTitleColor: mytheme === "dark" ? "#9CA3AF" : "#6B7280",
        iconSize: 20,
        itemHeight: 40,
        collapsedIconSize: 20,
        itemMarginBlock: 2,
        itemMarginInline: 12,
        itemPaddingInline: 12,
        itemBorderRadius: 8,
        subMenuItemBorderRadius: 6,
        fontSize: 14,
        fontWeight: 500,
      },
      Button: {
        borderRadius: 8,
        controlHeight: 40,
        controlHeightSM: 32,
        controlHeightLG: 48,
        paddingInline: 16,
        paddingInlineSM: 12,
        paddingInlineLG: 20,
        fontWeight: 500,
        primaryShadow: "0 1px 2px rgba(59, 130, 246, 0.2)",
        defaultShadow: "0 1px 2px rgba(0, 0, 0, 0.05)",
      },
      Input: {
        borderRadius: 8,
        controlHeight: 40,
        controlHeightSM: 32,
        controlHeightLG: 48,
        paddingInline: 12,
        fontSize: 14,
        colorBgContainer: mytheme === "dark" ? "#374151" : "#FFFFFF",
        colorBorder: mytheme === "dark" ? "#4B5563" : "#D1D5DB",
        colorBorderHover: mytheme === "dark" ? "#6B7280" : "#9CA3AF",
        activeShadow: "0 0 0 3px rgba(59, 130, 246, 0.1)",
      },
      Badge: {
        borderRadius: 8,
        fontWeight: 600,
        fontSize: 11,
        fontSizeSM: 10,
      },
      Avatar: {
        borderRadius: 8,
        fontSize: 14,
        fontWeight: 600,
      },
      Switch: {
        colorPrimary: "#3B82F6",
        colorPrimaryHover: "#2563EB",
        borderRadius: 12,
      },
      Dropdown: {
        colorBgElevated: mytheme === "dark" ? "#374151" : "#FFFFFF",
        borderRadius: 12,
        boxShadowSecondary: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
        paddingBlock: 8,
      },
      Tooltip: {
        borderRadius: 6,
        fontSize: 12,
        colorBgSpotlight: mytheme === "dark" ? "#374151" : "#1F2937",
      },
    },
  }

  return (
    <ConfigProvider theme={premiumThemeConfig}>
      <NProgress isAnimating={mainLoading} key={location.key} />

      <div className="admin-layout" style={{ minHeight: "100vh", background: premiumThemeConfig.token.colorBgLayout }}>
        {/* Mobile Overlay */}
        {isMobile && !collapsed && (
          <div
            style={{
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              zIndex: 1000,
              backdropFilter: "blur(4px)",
            }}
            onClick={() => setCollapsed(true)}
            onTouchStart={onTouchStart}
            onTouchMove={onTouchMove}
            onTouchEnd={onTouchEnd}
          >
            <div
              ref={sidenavRef}
              onClick={(e) => e.stopPropagation()}
              style={{
                transform: "translateX(0)",
                transition: "transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
              }}
            >
              <Sidebar
                collapsed={false}
                layoutState={layoutState}
                onMenuSelect={handleMenuSelect}
                onOpenChange={handleOpenChange}
                theme={mytheme}
                isMobile={true}
              />
            </div>
          </div>
        )}

        {/* Header */}
        <HeaderBar collapsed={collapsed} onToggle={handleToggle} />

        {/* Body */}
        <div className="admin-body" style={{ display: "flex", paddingTop: 64 }}>
          {/* Desktop Sidebar */}
          {!isMobile && (
            <Sidebar
              collapsed={collapsed}
              layoutState={layoutState}
              onMenuSelect={handleMenuSelect}
              onOpenChange={handleOpenChange}
              theme={mytheme}
              isMobile={false}
            />
          )}

          {/* Main Content */}
          <AppContent collapsed={collapsed} />
        </div>
      </div>

      <ModalController />
    </ConfigProvider>
  )
}
