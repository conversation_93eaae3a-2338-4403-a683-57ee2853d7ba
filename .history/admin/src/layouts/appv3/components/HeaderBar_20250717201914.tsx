"use client"

import type React from "react"
import { Layout, But<PERSON>, theme, Badge, Avatar, Dropdown, Switch, Tooltip, Input, Breadcrumb } from "antd"
import { Menu, Search, Bell, Settings, LogOut, Moon, Sun, Globe, User, ChevronDown, Command, Plus } from "lucide-react"
import { useMediaQuery } from "react-responsive"
import { useNavigate, useLocation } from "react-router"
import { useThemeStore } from "@/store/themeStore"

const { Header } = Layout
const { Search: AntSearch } = Input

interface HeaderBarProps {
  collapsed: boolean
  onToggle: () => void
}

export const HeaderBar: React.FC<HeaderBarProps> = ({ collapsed, onToggle }) => {
  const {
    token: { colorBgContainer, colorText, colorBorder, colorTextSecondary },
  } = theme.useToken()
  const navigate = useNavigate()
  const location = useLocation()
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const { mytheme, setTheme } = useThemeStore()

  const handleThemeToggle = (checked: boolean) => {
    setTheme(checked ? "dark" : "light")
  }

  // Generate breadcrumbs from current path
  const getBreadcrumbs = () => {
    const pathSegments = location.pathname.split("/").filter(Boolean)
    const breadcrumbs = [
      {
        title: "Dashboard",
        href: "/dashboard",
      },
    ]

    pathSegments.forEach((segment, index) => {
      const path = "/" + pathSegments.slice(0, index + 1).join("/")
      const title = segment.charAt(0).toUpperCase() + segment.slice(1).replace("-", " ")
      breadcrumbs.push({
        title,
        href: path,
      })
    })

    return breadcrumbs
  }

  const userMenuItems = [
    {
      key: "profile",
      icon: <User size={16} />,
      label: "Profile Settings",
      onClick: () => navigate("/profile"),
    },
    {
      key: "settings",
      icon: <Settings size={16} />,
      label: "Account Settings",
      onClick: () => navigate("/settings"),
    },
    {
      type: "divider" as const,
    },
    {
      key: "logout",
      icon: <LogOut size={16} />,
      label: "Sign Out",
      onClick: () => {
        console.log("Logout")
      },
    },
  ]

  const quickActions = [
    {
      key: "new-project",
      label: "New Project",
      onClick: () => navigate("/projects/new"),
    },
    {
      key: "add-employee",
      label: "Add Employee",
      onClick: () => navigate("/employees/new"),
    },
    {
      key: "create-invoice",
      label: "Create Invoice",
      onClick: () => navigate("/billing/invoices/new"),
    },
  ]

  return (
    <Header
      style={{
        height: isMobile ? 64 : 72,
        padding: "0 24px",
        background: colorBgContainer,
        position: "fixed",
        top: 0,
        zIndex: 999,
        left: isMobile ? 0 : collapsed ? 80 : 280,
        width: isMobile ? "100%" : collapsed ? "calc(100% - 80px)" : "calc(100% - 280px)",
        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
        borderBottom: `1px solid ${colorBorder}`,
        boxShadow:
          mytheme === "light"
            ? "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"
            : "0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)",
        backdropFilter: "blur(12px)",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
      }}
    >
      {/* Top Row */}
      <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", height: "100%" }}>
        {/* Left Section */}
        <div style={{ display: "flex", alignItems: "center", flex: 1, minWidth: 0, gap: 16 }}>
          <Button
            type="text"
            icon={<Menu size={20} />}
            onClick={onToggle}
            style={{
              width: 48,
              height: 48,
              color: colorText,
              borderRadius: 12,
              transition: "all 0.2s ease",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              background: "transparent",
            }}
            className="hover:bg-gray-100 dark:hover:bg-gray-800"
          />

          {!isMobile && (
            <div style={{ flex: 1, maxWidth: 480 }}>
              <AntSearch
                placeholder="Search anything... (⌘K)"
                allowClear
                size="large"
                style={{ borderRadius: 12 }}
                prefix={<Search size={16} style={{ color: colorTextSecondary }} />}
                suffix={
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: 4,
                      color: colorTextSecondary,
                      fontSize: 12,
                      fontWeight: 500,
                    }}
                  >
                    <Command size={12} />
                    <span>K</span>
                  </div>
                }
              />
            </div>
          )}
        </div>

        {/* Right Section */}
        <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
          {isMobile && (
            <Button
              type="text"
              icon={<Search size={18} />}
              style={{
                width: 44,
                height: 44,
                color: colorText,
                borderRadius: 10,
              }}
            />
          )}

          {/* Quick Actions */}
          {!isMobile && (
            <Dropdown menu={{ items: quickActions }} placement="bottomRight">
              <Button
                type="primary"
                icon={<Plus size={16} />}
                style={{
                  borderRadius: 10,
                  height: 40,
                  fontWeight: 600,
                  boxShadow: "0 2px 4px rgba(59, 130, 246, 0.2)",
                }}
              >
                New
              </Button>
            </Dropdown>
          )}

          {/* Theme Toggle */}
          <Tooltip title={`Switch to ${mytheme === "light" ? "dark" : "light"} mode`}>
            <Switch
              checked={mytheme === "dark"}
              onChange={handleThemeToggle}
              checkedChildren={<Moon size={14} />}
              unCheckedChildren={<Sun size={14} />}
              style={{ margin: "0 4px" }}
            />
          </Tooltip>

          {/* Language */}
          <Tooltip title="Language">
            <Button
              type="text"
              icon={<Globe size={18} />}
              style={{
                width: 44,
                height: 44,
                color: colorText,
                borderRadius: 10,
              }}
            />
          </Tooltip>

          {/* Notifications */}
          <Tooltip title="Notifications">
            <Badge count={12} size="small" offset={[-2, 2]}>
              <Button
                type="text"
                icon={<Bell size={18} />}
                style={{
                  width: 44,
                  height: 44,
                  color: colorText,
                  borderRadius: 10,
                }}
              />
            </Badge>
          </Tooltip>

          {/* User Profile */}
          <Dropdown menu={{ items: userMenuItems }} placement="bottomRight" arrow>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                gap: 12,
                padding: "6px 12px",
                borderRadius: 12,
                cursor: "pointer",
                transition: "all 0.2s ease",
                background: "transparent",
                border: `1px solid ${colorBorder}`,
              }}
              className="hover:bg-gray-50 dark:hover:bg-gray-800"
            >
              <Avatar
                size={36}
                style={{
                  background: "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)",
                  fontSize: 14,
                  fontWeight: 600,
                  border: "2px solid rgba(59, 130, 246, 0.2)",
                }}
              >
                JD
              </Avatar>
              {!isMobile && (
                <div style={{ textAlign: "left", lineHeight: 1.3 }}>
                  <div
                    style={{
                      fontSize: 14,
                      fontWeight: 600,
                      color: colorText,
                    }}
                  >
                    John Doe
                  </div>
                  <div
                    style={{
                      fontSize: 12,
                      color: colorTextSecondary,
                      fontWeight: 500,
                    }}
                  >
                    Administrator
                  </div>
                </div>
              )}
              {!isMobile && <ChevronDown size={16} style={{ color: colorTextSecondary }} />}
            </div>
          </Dropdown>
        </div>
      </div>

      {/* Breadcrumbs Row */}
      {!isMobile && (
        <div style={{ marginTop: -8, paddingBottom: 8 }}>
          <Breadcrumb
            items={getBreadcrumbs()}
            style={{
              fontSize: 13,
              fontWeight: 500,
            }}
          />
        </div>
      )}
    </Header>
  )
}
