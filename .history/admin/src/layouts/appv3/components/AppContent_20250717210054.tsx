"use client"

import type React from "react"
import { Outlet } from "react-router"
import { useMediaQuery } from "react-responsive"
import { useThemeStore } from "@/store/themeStore"
import { theme } from "antd"

interface AppContentProps {
  collapsed: boolean
}

export const AppContent: React.FC<AppContentProps> = ({ collapsed }) => {
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const { mytheme } = useThemeStore()
  const {
    token: { colorBgLayout, colorBorder, colorBgContainer },
  } = theme.useToken()

  return (
    <main
      className="admin-content"
      style={{
        gridRow: isMobile ? "2 / -2" : "2 / -2",
        gridColumn: isMobile ? "1 / -1" : "2 / -1",
        background: mytheme === "dark" ? "#0a0a0a" : "#fafafa",
        paddingLeft: isMobile ? "12px" : "24px",
        paddingRight: isMobile ? "12px" : "16px",
        paddingTop: "16px",
        paddingBottom: "16px",
        overflow: "auto",
        minHeight: 0,
        transition: "all 200ms ease",
      }}
    >
      {/* Content Container */}
      <div
        className="w-100 position-relative"
        style={{
          minHeight: "calc(100vh - 140px)",
          background: mytheme === "dark" ? "#1a1a1a" : "#ffffff",
          borderRadius: 6,
          padding: isMobile ? "16px 12px" : "24px 20px",
          boxShadow: mytheme === "dark" ? "0 1px 3px rgba(0, 0, 0, 0.3)" : "0 1px 3px rgba(0, 0, 0, 0.1)",
          border: `1px solid ${mytheme === "dark" ? "#374151" : "#e5e7eb"}`,
          overflow: "hidden",
          transition: "all 200ms ease",
        }}
      >
        {/* Route Content */}
        <Outlet />
      </div>
    </main>
  )
}
