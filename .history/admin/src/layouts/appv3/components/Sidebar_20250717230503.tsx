"use client"

import React from "react"
import { useEffect, useMemo } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd"
import { useNavigate, useLocation } from "react-router"
import {
  LayoutDashboard,
  Users,
  Building2,
  CreditCard,
  Settings,
  Smartphone,
  FileText,
  ChevronLeft,
  ChevronRight,
} from "lucide-react"
import { getOriginalMenuItems } from "../utils/menuAdapter"
import type { MenuItem, LayoutState } from "../types"
import { useThemeStore } from "@/store/themeStore"

interface SidebarProps {
  collapsed: boolean
  layoutState: LayoutState
  onMenuSelect: (keys: string[]) => void
  onOpenChange: (keys: string[]) => void
  theme?: "light" | "dark"
  isMobile?: boolean
}

// Convert original menu items to the appv3 format with lucide icons
const convertOriginalToAppV3Format = (originalItems: any[]): MenuItem[] => {
  // Icon mapping from original menu keys to lucide icons
  const iconMap: Record<string, React.ReactNode> = {
    dashboard: <LayoutDashboard />,
    clients: <Building2 />,
    employees: <Users />,
    finance: <CreditCard />,
    reports: <FileText />,
    settings: <Settings />,
    devices: <Smartphone />,
  }

  const convertItems = (items: any[]): MenuItem[] => {
    return items.map((item) => {
      // Extract the actual label text from React Link components
      const getLabel = (label: any): string => {
        if (typeof label === 'string') return label
        if (label?.props?.children) return label.props.children
        return item.key
      }

      // Extract the path from React Link components
      const getPath = (label: any): string | undefined => {
        if (label?.props?.to) return label.props.to
        return undefined
      }

      const baseItem: MenuItem = {
        key: item.key,
        icon: iconMap[item.key] || item.icon,
        label: getLabel(item.label),
        path: getPath(item.label),
      }

      if (item.children && Array.isArray(item.children)) {
        baseItem.children = convertItems(item.children)
      }

      return baseItem
    })
  }

  return convertItems(originalItems)
}

// Get the original menu items and convert them to appv3 format
const getCompactMenuItems = (pathname: string): MenuItem[] => {
  const originalItems = getOriginalMenuItems(pathname)
  return convertOriginalToAppV3Format(originalItems)
}

export const Sidebar: React.FC<SidebarProps> = ({
  collapsed,
  layoutState,
  onMenuSelect,
  onOpenChange,
  theme = "light",
  isMobile = false,
}) => {
  const navigate = useNavigate()
  const location = useLocation()
  const { setCollapsed } = useThemeStore()

  const menuItems = useMemo(() => getCompactMenuItems(location.pathname), [location.pathname])

  const handleMenuClick = ({ key }: { key: string }) => {
    const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
      for (const item of items) {
        if (item.key === targetKey) return item
        if (item.children) {
          const found = findMenuItem(item.children, targetKey)
          if (found) return found
        }
      }
      return null
    }

    const menuItem = findMenuItem(menuItems, key)
    if (menuItem?.path) {
      navigate(menuItem.path)
      if (isMobile) {
        setCollapsed(true)
      }
    }
  }

  useEffect(() => {
    const normalize = (p: string) => p.replace(/^\/+|\/+$/g, '')
    const currentPath = normalize(location.pathname)

    const findMatch = (items: MenuItem[]): { selectedKey: string; openKeys: string[] } => {
      for (const item of items) {
        if (item.children) {
          for (const child of item.children) {
            if (child.path && normalize(child.path) === currentPath) {
              return { selectedKey: child.key, openKeys: [item.key] }
            }
          }
        } else if (item.path && normalize(item.path) === currentPath) {
          return { selectedKey: item.key, openKeys: [] }
        }
      }
      return { selectedKey: '', openKeys: [] }
    }

    const { selectedKey, openKeys } = findMatch(menuItems)
    if (selectedKey) {
      onMenuSelect([selectedKey])
      onOpenChange(openKeys)
    }
  }, [location.pathname, menuItems, onMenuSelect, onOpenChange])

  const convertToAntMenuItems = (items: MenuItem[]): any[] => {
    return items.map((item) => {
      if (item.type === "group") {
        return {
          type: "group",
          label: !collapsed ? (
            <div
              style={{
                fontSize: 11,
                fontWeight: 600,
                letterSpacing: "0.5px",
                color: theme === "dark" ? "#d1d5db" : "#6b7280",
                padding: "0",
                margin: "0 0 4px 0",
                textTransform: "uppercase",
              }}
            >
              {item.label}
            </div>
          ) : null,
          children: item.children ? convertToAntMenuItems(item.children) : [],
        }
      }

      if (item.children && item.children.length > 0) {
        return {
          key: item.key,
          icon: (
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                width: collapsed ? 20 : 18,
                height: collapsed ? 20 : 18,
                transition: "all 300ms ease-in-out",
                margin: collapsed ? "0 12px 0 0" : "0",
              }}
            >
              {item.icon && React.isValidElement(item.icon)
                ? React.cloneElement(item.icon as React.ReactElement, {
                  size: collapsed ? 20 : 18,
                })
                : item.icon}
            </div>
          ),
          label: (
            <div className="d-flex align-items-center justify-content-between w-100 position-relative">
              <span className="fw-medium" style={{ fontSize: 14 }}>
                {item.label}
              </span>
              {item.badge && !collapsed && (
                <div
                  className="position-absolute"
                  style={{
                    width: 6,
                    height: 6,
                    borderRadius: "50%",
                    backgroundColor: "#ef4444",
                    right: 8,
                    top: "50%",
                    transform: "translateY(-50%)",
                    boxShadow: "0 0 0 1px rgba(239, 68, 68, 0.2)",
                  }}
                />
              )}
            </div>
          ),
          children: convertToAntMenuItems(item.children),
          style: {
            border: "none",
          },
        }
      }

      return {
        key: item.key,
        icon: (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              width: collapsed ? 20 : 18,
              height: collapsed ? 20 : 18,
              transition: "all 300ms ease-in-out",
              margin: collapsed ? "0 12px 0 0" : "0",
            }}
          >
            {item.icon && React.isValidElement(item.icon)
              ? React.cloneElement(item.icon as React.ReactElement, {
                size: collapsed ? 20 : 18,
              })
              : item.icon}
          </div>
        ),
        label: (
          <div className="d-flex align-items-center justify-content-between w-100 position-relative">
            <span className="fw-medium" style={{ fontSize: 14 }}>
              {item.label}
            </span>
            {item.badge && !collapsed && (
              <div
                className="position-absolute"
                style={{
                  width: 6,
                  height: 6,
                  borderRadius: "50%",
                  backgroundColor: "#ef4444",
                  right: 8,
                  top: "50%",
                  transform: "translateY(-50%)",
                  boxShadow: "0 0 0 1px rgba(239, 68, 68, 0.2)",
                }}
              />
            )}
          </div>
        ),
        style: {
          border: "none",
        },
      }
    })
  }

  const antMenuItems = useMemo(() => convertToAntMenuItems(menuItems), [menuItems, collapsed, theme])

  const sidebarWidth = collapsed ? 64 : 280

  return (
    <aside
      className="admin-sidebar d-flex flex-column"
      style={{
        gridRow: isMobile ? "1 / -1" : "2 / -1",
        gridColumn: isMobile ? "1 / -1" : "1 / 2",
        width: isMobile ? 280 : sidebarWidth,
        minWidth: isMobile ? 280 : sidebarWidth,
        background: theme === "dark" ? "#1f2937" : "#ffffff",
        borderRight: `1px solid ${theme === "dark" ? "#374151" : "#e5e7eb"}`,
        transition: "all 300ms ease-in-out",
        position: isMobile ? "fixed" : "sticky",
        top: isMobile ? 0 : 64,
        left: 0,
        height: isMobile ? "100vh" : "calc(100vh - 64px)",
        zIndex: isMobile ? 1001 : 10,
        boxShadow: isMobile
          ? "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
          : "0 2px 4px rgba(0, 0, 0, 0.05)",
        overflow: "hidden",
      }}
    >
      {/* Compact Navigation Menu */}
      <div
        className="flex-grow-1"
        style={{
          overflowY: "auto",
          overflowX: "hidden",
          padding: "8px 0",
        }}
      >
        <Menu
          mode="inline"
          selectedKeys={layoutState.selectedKeys}
          openKeys={collapsed ? [] : layoutState.openKeys}
          onSelect={({ selectedKeys }) => onMenuSelect(selectedKeys)}
          onOpenChange={onOpenChange}
          onClick={handleMenuClick}
          items={antMenuItems}
          theme={theme}
          style={{
            border: "none",
            background: "transparent",
          }}
          inlineCollapsed={collapsed}
        />
      </div>

      {/* Smooth Collapse Toggle Button */}
      {!isMobile && (
        <div
          style={{
            padding: "12px",
            borderTop: `1px solid ${theme === "dark" ? "#374151" : "#e5e7eb"}`,
          }}
        >
          <Button
            type="text"
            icon={
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  transition: "transform 300ms ease-in-out",
                }}
              >
                {collapsed ? <ChevronRight size={14} /> : <ChevronLeft size={14} />}
              </div>
            }
            onClick={() => setCollapsed(!collapsed)}
            className="w-100 d-flex align-items-center justify-content-center fw-medium"
            aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
            style={{
              height: 32,
              color: theme === "dark" ? "#d1d5db" : "#6b7280",
              borderRadius: 6,
              transition: "all 300ms ease-in-out",
              fontSize: 12,
              border: "none",
              background: "transparent",
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = theme === "dark" ? "#374151" : "#f8fafc"
              e.currentTarget.style.color = theme === "dark" ? "#f9fafb" : "#111827"
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = "transparent"
              e.currentTarget.style.color = theme === "dark" ? "#9ca3af" : "#6b7280"
            }}
            onFocus={(e) => {
              e.currentTarget.style.outline = "2px solid #3B82F6"
              e.currentTarget.style.outlineOffset = "2px"
            }}
            onBlur={(e) => {
              e.currentTarget.style.outline = "none"
            }}
          >
            {!collapsed && <span style={{ marginLeft: 8 }}>Collapse</span>}
          </Button>
        </div>
      )}
    </aside>
  )
}
