"use client"

import type React from "react"
import { useEffect, useMemo } from "react"
import { <PERSON>u, Button } from "antd"
import { useNavigate, useLocation } from "react-router"
import {
  LayoutDashboard,
  BarChart3,
  Users,
  Building2,
  CreditCard,
  Settings,
  Mail,
  MessageSquare,
  Bell,
  Smartphone,
  FileText,
  Calendar,
  Shield,
  Zap,
  TrendingUp,
  UserCheck,
  Briefcase,
  ChevronLeft,
  ChevronRight,
} from "lucide-react"
import { getOriginalMenuItems } from "../utils/menuAdapter"
import type { MenuItem, LayoutState } from "../types"
import { useThemeStore } from "@/store/themeStore"

const { Sider } = Layout;

interface SidebarProps {
  collapsed: boolean;
  layoutState: any;
  onMenuSelect: (keys: string[]) => void;
  onOpenChange: (keys: string[]) => void;
  theme?: 'light' | 'dark';
  isMobile?: boolean;
}

export const Sidebar: React.FC<SidebarProps> = ({
  collapsed,
  layoutState,
  onMenuSelect,
  onOpenChange,
  theme = 'light',
  isMobile = false,
}) => {
  const nodeRef = useRef(null);
  const { pathname } = useLocation();
  const { mytheme } = useThemeStore();

  const items = useMemo(() => getOriginalMenuItems(pathname), [pathname]);

  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const [current, setCurrent] = useState<string>('');

  const rootSubmenuKeys = useMemo(
    () => items.filter(i => 'children' in i && Array.isArray((i as any).children)).map(i => i.key),
    [items]
  );

  const onOpenChangeInternal: MenuProps['onOpenChange'] = keys => {
    const latestOpenKey = keys.find(key => !openKeys.includes(key));
    if (latestOpenKey && rootSubmenuKeys.includes(latestOpenKey)) {
      setOpenKeys([latestOpenKey]);
    } else {
      setOpenKeys(keys);
    }
    // Also call the parent's onOpenChange
    onOpenChange(keys);
  };

  useEffect(() => {
    const normalize = (p: string) => p.replace(/^\/+|\/+$/g, '');
    const currentPath = normalize(pathname);

    const findMatch = (items: any[]): { selectedKey: string, groupKey: string } => {
      for (const item of items) {
        if (item.children) {
          for (const child of item.children) {
            const to = normalize((child.label as any)?.props?.to || '');
            if (currentPath === to) return { selectedKey: child.key, groupKey: item.key };
          }
        } else {
          const to = normalize((item.label as any)?.props?.to || '');
          if (currentPath === to) return { selectedKey: item.key, groupKey: '' };
        }
      }
      return { selectedKey: '', groupKey: '' };
    };

    const { selectedKey, groupKey } = findMatch(items);

    setCurrent(selectedKey);
    if (groupKey) setOpenKeys([groupKey]);

    // Update parent state
    onMenuSelect([selectedKey]);
  }, [pathname, items, onMenuSelect]);

  return (
    <Sider
      ref={nodeRef}
      width={isMobile ? 280 : 240}
      collapsed={collapsed}
      theme={mytheme}
      style={{
        overflow: 'auto',
        height: '100vh',
        position: 'fixed',
        left: 0,
        top: isMobile ? 0 : 64,
        bottom: 0,
        zIndex: isMobile ? 200 : 100,
      }}
    >
      <Menu
        mode="inline"
        openKeys={collapsed ? [] : openKeys}
        onOpenChange={onOpenChangeInternal}
        selectedKeys={[current]}
        items={items as MenuProps['items']}
        style={{ border: 'none' }}
        theme={mytheme}
        inlineCollapsed={collapsed}
      />
    </Sider>
  );
};
