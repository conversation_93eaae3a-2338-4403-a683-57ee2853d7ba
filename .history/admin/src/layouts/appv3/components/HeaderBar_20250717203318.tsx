"use client"

import type React from "react"
import { But<PERSON>, theme, Badge, Avatar, Dropdown, Switch, Tooltip, Input } from "antd"
import { Menu, Search, Bell, Settings, LogOut, Moon, Sun, Globe, User, ChevronDown, Command, Plus } from "lucide-react"
import { useMediaQuery } from "react-responsive"
import { useNavigate } from "react-router"
import { useThemeStore } from "@/store/themeStore"

const { Search: AntSearch } = Input

interface HeaderBarProps {
  collapsed: boolean
  onToggle: () => void
}

export const HeaderBar: React.FC<HeaderBarProps> = ({ collapsed, onToggle }) => {
  const {
    token: { colorBgContainer, colorText, colorBorder, colorTextSecondary },
  } = theme.useToken()
  const navigate = useNavigate()
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const { mytheme, setTheme } = useThemeStore()

  const handleThemeToggle = (checked: boolean) => {
    setTheme(checked ? "dark" : "light")
  }

  const userMenuItems = [
    {
      key: "profile",
      icon: <User size={16} />,
      label: "Profile Settings",
      onClick: () => navigate("/profile"),
    },
    {
      key: "settings",
      icon: <Settings size={16} />,
      label: "Account Settings",
      onClick: () => navigate("/settings"),
    },
    {
      type: "divider" as const,
    },
    {
      key: "logout",
      icon: <LogOut size={16} />,
      label: "Sign Out",
      onClick: () => {
        console.log("Logout")
      },
    },
  ]

  const quickActions = [
    {
      key: "new-project",
      label: "New Project",
      onClick: () => navigate("/projects/new"),
    },
    {
      key: "add-employee",
      label: "Add Employee",
      onClick: () => navigate("/employees/new"),
    },
    {
      key: "create-invoice",
      label: "Create Invoice",
      onClick: () => navigate("/billing/invoices/new"),
    },
  ]

  return (
    <header
      className="admin-header"
      style={{
        height: 64,
        padding: "0 24px",
        background: colorBgContainer,
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        zIndex: 999,
        borderBottom: `1px solid ${colorBorder}`,
        boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        gap: 24,
      }}
    >
      {/* Left Section - Logo & Menu Toggle */}
      <div style={{ display: "flex", alignItems: "center", gap: 16, minWidth: 0 }}>
        {/* Menu Toggle */}
        <Button
          type="text"
          icon={<Menu size={20} />}
          onClick={onToggle}
          style={{
            width: 48,
            height: 48,
            color: colorText,
            borderRadius: 8,
            transition: "all 0.15s ease",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            flexShrink: 0,
          }}
        />

        {/* Logo & Brand */}
        <div style={{ display: "flex", alignItems: "center", gap: 12, minWidth: 0 }}>
          <div
            style={{
              width: 48,
              height: 48,
              borderRadius: 12,
              background: "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              boxShadow: "0 4px 12px rgba(59, 130, 246, 0.25)",
              flexShrink: 0,
            }}
          >
            <div
              style={{
                width: 24,
                height: 24,
                background: "#FFFFFF",
                borderRadius: 4,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontWeight: 700,
                fontSize: 12,
                color: "#3B82F6",
              }}
            >
              W
            </div>
          </div>
          {!isMobile && (
            <div style={{ minWidth: 0 }}>
              <div
                style={{
                  fontSize: 18,
                  fontWeight: 600,
                  color: colorText,
                  lineHeight: 1.2,
                  letterSpacing: "-0.025em",
                }}
              >
                WorkFlow Pro
              </div>
              <div
                style={{
                  fontSize: 12,
                  fontWeight: 500,
                  color: colorTextSecondary,
                  lineHeight: 1,
                }}
              >
                Business Dashboard
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Center Section - Search */}
      {!isMobile && (
        <div style={{ flex: 1, maxWidth: 480, minWidth: 200 }}>
          <AntSearch
            placeholder="Search anything..."
            allowClear
            size="large"
            style={{
              borderRadius: 8,
              width: "100%",
            }}
            prefix={<Search size={16} style={{ color: colorTextSecondary }} />}
            suffix={
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: 2,
                  color: colorTextSecondary,
                  fontSize: 11,
                  fontWeight: 500,
                  padding: "2px 6px",
                  background: mytheme === "dark" ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.05)",
                  borderRadius: 4,
                }}
              >
                <Command size={10} />
                <span>K</span>
              </div>
            }
          />
        </div>
      )}

      {/* Right Section - Actions */}
      <div style={{ display: "flex", alignItems: "center", gap: 16 }}>
        {/* Mobile Search */}
        {isMobile && (
          <Button
            type="text"
            icon={<Search size={18} />}
            style={{
              width: 40,
              height: 40,
              color: colorText,
              borderRadius: 8,
              flexShrink: 0,
            }}
          />
        )}

        {/* Quick Actions */}
        {!isMobile && (
          <Dropdown menu={{ items: quickActions }} placement="bottomRight">
            <Button
              type="primary"
              icon={<Plus size={16} />}
              style={{
                borderRadius: 8,
                height: 40,
                fontWeight: 600,
                boxShadow: "0 2px 4px rgba(59, 130, 246, 0.2)",
                flexShrink: 0,
              }}
            >
              New
            </Button>
          </Dropdown>
        )}

        {/* Theme Toggle */}
        <Tooltip title={`Switch to ${mytheme === "light" ? "dark" : "light"} mode`}>
          <Switch
            checked={mytheme === "dark"}
            onChange={handleThemeToggle}
            checkedChildren={<Moon size={12} />}
            unCheckedChildren={<Sun size={12} />}
            style={{ flexShrink: 0 }}
          />
        </Tooltip>

        {/* Language */}
        <Tooltip title="Language">
          <Button
            type="text"
            icon={<Globe size={18} />}
            style={{
              width: 40,
              height: 40,
              color: colorText,
              borderRadius: 8,
              flexShrink: 0,
            }}
          />
        </Tooltip>

        {/* Notifications */}
        <Tooltip title="Notifications">
          <Badge count={12} size="small" offset={[-2, 2]}>
            <Button
              type="text"
              icon={<Bell size={18} />}
              style={{
                width: 40,
                height: 40,
                color: colorText,
                borderRadius: 8,
                flexShrink: 0,
              }}
            />
          </Badge>
        </Tooltip>

        {/* User Profile */}
        <Dropdown menu={{ items: userMenuItems }} placement="bottomRight" arrow>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: 12,
              padding: "6px 12px",
              borderRadius: 8,
              cursor: "pointer",
              transition: "all 0.15s ease",
              background: "transparent",
              border: `1px solid ${colorBorder}`,
              flexShrink: 0,
            }}
          >
            <Avatar
              size={32}
              style={{
                background: "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)",
                fontSize: 13,
                fontWeight: 600,
                border: "2px solid rgba(59, 130, 246, 0.2)",
              }}
            >
              JD
            </Avatar>
            {!isMobile && (
              <div style={{ textAlign: "left", lineHeight: 1.3, minWidth: 0 }}>
                <div
                  style={{
                    fontSize: 14,
                    fontWeight: 600,
                    color: colorText,
                    whiteSpace: "nowrap",
                  }}
                >
                  John Doe
                </div>
                <div
                  style={{
                    fontSize: 12,
                    color: colorTextSecondary,
                    fontWeight: 500,
                    whiteSpace: "nowrap",
                  }}
                >
                  Administrator
                </div>
              </div>
            )}
            {!isMobile && <ChevronDown size={14} style={{ color: colorTextSecondary, flexShrink: 0 }} />}
          </div>
        </Dropdown>
      </div>
    </header>
  )
}
