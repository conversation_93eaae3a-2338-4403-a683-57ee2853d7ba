"use client"

import type React from "react"
import { Layout, But<PERSON>, theme, Badge, Avatar, Dropdown, Switch, Tooltip, Input } from "antd"
import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  BellOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  <PERSON>Outlined,
  <PERSON>Outlined,
  SearchOutlined,
  GlobalOutlined,
} from "@ant-design/icons"
import { useMediaQuery } from "react-responsive"
import { useNavigate } from "react-router"
import { useThemeStore } from "@/store/themeStore"

const { Header } = Layout
const { Search } = Input

interface HeaderBarProps {
  collapsed: boolean
  onToggle: () => void
}

export const HeaderBar: React.FC<HeaderBarProps> = ({ collapsed, onToggle }) => {
  const {
    token: { borderRadius, colorBgContainer, colorText, colorBorder },
  } = theme.useToken()
  const navigate = useNavigate()
  const isMobile = useMediaQuery({ maxWidth: 769 })
  const { mytheme, setTheme } = useThemeStore()

  const handleThemeToggle = (checked: boolean) => {
    setTheme(checked ? "dark" : "light")
  }

  const userMenuItems = [
    {
      key: "profile",
      icon: <UserOutlined />,
      label: "Profile Settings",
      onClick: () => navigate("/profile"),
    },
    {
      key: "settings",
      icon: <SettingOutlined />,
      label: "Account Settings",
      onClick: () => navigate("/settings"),
    },
    {
      type: "divider" as const,
    },
    {
      key: "logout",
      icon: <LogoutOutlined />,
      label: "Sign Out",
      onClick: () => {
        console.log("Logout")
      },
    },
  ]

  return (
    <Header
      style={{
        height: isMobile ? 56 : 64,
        padding: isMobile ? "0 16px" : "0 24px",
        background: colorBgContainer,
        position: "fixed",
        top: 0,
        zIndex: 1000,
        left: isMobile ? 0 : collapsed ? 80 : 256,
        width: isMobile ? "100%" : collapsed ? "calc(100% - 80px)" : "calc(100% - 256px)",
        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
        borderBottom: `1px solid ${colorBorder}`,
        boxShadow: mytheme === "light" ? "0 2px 8px rgba(0,0,0,0.06)" : "0 2px 8px rgba(0,0,0,0.25)",
        backdropFilter: "blur(8px)",
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
      }}
    >
      <div style={{ display: "flex", alignItems: "center", flex: 1, minWidth: 0, gap: 16 }}>
        <Button
          type="text"
          icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          onClick={onToggle}
          style={{
            fontSize: "16px",
            width: 48,
            height: 48,
            color: colorText,
            borderRadius: 8,
            transition: "all 0.2s ease",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        />

        {!isMobile && (
          <Search
            placeholder="Search employees, clients, projects..."
            allowClear
            style={{ maxWidth: 400, flex: 1 }}
            size="middle"
          />
        )}
      </div>

      <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
        {isMobile && (
          <Button
            type="text"
            icon={<SearchOutlined />}
            style={{
              fontSize: "16px",
              width: 40,
              height: 40,
              color: colorText,
              borderRadius: 6,
            }}
          />
        )}

        <Tooltip title={`Switch to ${mytheme === "light" ? "dark" : "light"} mode`}>
          <Switch
            checked={mytheme === "dark"}
            onChange={handleThemeToggle}
            checkedChildren={<MoonOutlined />}
            unCheckedChildren={<SunOutlined />}
            style={{ margin: "0 8px" }}
          />
        </Tooltip>

        <Tooltip title="Language">
          <Button
            type="text"
            icon={<GlobalOutlined />}
            style={{
              fontSize: "16px",
              width: 40,
              height: 40,
              color: colorText,
              borderRadius: 6,
            }}
          />
        </Tooltip>

        <Tooltip title="Notifications">
          <Badge count={5} size="small">
            <Button
              type="text"
              icon={<BellOutlined />}
              style={{
                fontSize: "16px",
                width: 40,
                height: 40,
                color: colorText,
                borderRadius: 6,
              }}
            />
          </Badge>
        </Tooltip>

        <Dropdown menu={{ items: userMenuItems }} placement="bottomRight" arrow>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: 8,
              padding: "4px 8px",
              borderRadius: 8,
              cursor: "pointer",
              transition: "all 0.2s ease",
              background: "transparent",
            }}
          >
            <Avatar
              size={32}
              style={{
                backgroundColor: "#1677ff",
                fontSize: 14,
                fontWeight: 500,
              }}
            >
              B
            </Avatar>
            {!isMobile && (
              <div style={{ textAlign: "left", lineHeight: 1.2 }}>
                <div
                  style={{
                    fontSize: 14,
                    fontWeight: 500,
                    color: colorText,
                  }}
                >
                  John Doe
                </div>
                <div
                  style={{
                    fontSize: 12,
                    color: mytheme === "light" ? "rgba(0, 0, 0, 0.45)" : "rgba(255, 255, 255, 0.45)",
                  }}
                >
                  Administrator
                </div>
              </div>
            )}
          </div>
        </Dropdown>
      </div>
    </Header>
  )
}
