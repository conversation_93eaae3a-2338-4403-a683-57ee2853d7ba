"use client"

import type React from "react"
import { useEffect, useMemo } from "react"
import { Layout, Menu, Typography, Space } from "antd"
import { useNavigate, useLocation } from "react-router"
import {
  LayoutDashboard,
  BarChart3,
  Users,
  Building2,
  CreditCard,
  Settings,
  Mail,
  MessageSquare,
  Bell,
  Smartphone,
  FileText,
  Calendar,
  Shield,
  Zap,
  TrendingUp,
  UserCheck,
  Briefcase,
} from "lucide-react"
import { findSelectedMenuItem } from "../utils/menuAdapter"
import type { MenuItem, LayoutState } from "../types"

const { Sider } = Layout
const { Text } = Typography

interface SidebarProps {
  collapsed: boolean
  layoutState: LayoutState
  onMenuSelect: (keys: string[]) => void
  onOpenChange: (keys: string[]) => void
  theme?: "light" | "dark"
  isMobile?: boolean
}

// Modern, well-organized menu structure
const getModernMenuItems = (): MenuItem[] => [
  {
    key: "overview",
    label: "Overview",
    type: "group",
    children: [
      {
        key: "dashboard",
        icon: <LayoutDashboard size={20} />,
        label: "Dashboard",
        path: "/dashboard",
      },
      {
        key: "analytics",
        icon: <BarChart3 size={20} />,
        label: "Analytics",
        path: "/analytics",
      },
    ],
  },
  {
    key: "workspace",
    label: "Workspace",
    type: "group",
    children: [
      {
        key: "inbox",
        icon: <Mail size={20} />,
        label: "Inbox",
        path: "/inbox",
        badge: 12,
      },
      {
        key: "messages",
        icon: <MessageSquare size={20} />,
        label: "Messages",
        path: "/messages",
        badge: 3,
      },
      {
        key: "notifications",
        icon: <Bell size={20} />,
        label: "Notifications",
        path: "/notifications",
        badge: 7,
      },
      {
        key: "calendar",
        icon: <Calendar size={20} />,
        label: "Calendar",
        path: "/calendar",
      },
    ],
  },
  {
    key: "management",
    label: "Management",
    type: "group",
    children: [
      {
        key: "employees",
        icon: <Users size={20} />,
        label: "Team Members",
        path: "/employees",
        children: [
          {
            key: "employees-directory",
            label: "Directory",
            path: "/employees/directory",
          },
          {
            key: "employees-attendance",
            label: "Attendance",
            path: "/employees/attendance",
          },
          {
            key: "employees-performance",
            label: "Performance",
            path: "/employees/performance",
          },
        ],
      },
      {
        key: "clients",
        icon: <Building2 size={20} />,
        label: "Clients",
        path: "/clients",
        children: [
          {
            key: "clients-active",
            label: "Active Clients",
            path: "/clients/active",
          },
          {
            key: "clients-prospects",
            label: "Prospects",
            path: "/clients/prospects",
          },
          {
            key: "clients-contracts",
            label: "Contracts",
            path: "/clients/contracts",
          },
        ],
      },
      {
        key: "projects",
        icon: <Briefcase size={20} />,
        label: "Projects",
        path: "/projects",
        children: [
          {
            key: "projects-active",
            label: "Active Projects",
            path: "/projects/active",
          },
          {
            key: "projects-completed",
            label: "Completed",
            path: "/projects/completed",
          },
          {
            key: "projects-planning",
            label: "Planning",
            path: "/projects/planning",
          },
        ],
      },
    ],
  },
  {
    key: "finance",
    label: "Finance",
    type: "group",
    children: [
      {
        key: "billing",
        icon: <CreditCard size={20} />,
        label: "Billing & Invoices",
        path: "/billing",
        children: [
          {
            key: "billing-invoices",
            label: "Invoices",
            path: "/billing/invoices",
          },
          {
            key: "billing-payments",
            label: "Payments",
            path: "/billing/payments",
          },
          {
            key: "billing-subscriptions",
            label: "Subscriptions",
            path: "/billing/subscriptions",
          },
        ],
      },
      {
        key: "reports",
        icon: <TrendingUp size={20} />,
        label: "Financial Reports",
        path: "/reports",
        children: [
          {
            key: "reports-revenue",
            label: "Revenue",
            path: "/reports/revenue",
          },
          {
            key: "reports-expenses",
            label: "Expenses",
            path: "/reports/expenses",
          },
          {
            key: "reports-profit",
            label: "Profit & Loss",
            path: "/reports/profit",
          },
        ],
      },
    ],
  },
  {
    key: "tools",
    label: "Tools & Apps",
    type: "group",
    children: [
      {
        key: "integrations",
        icon: <Zap size={20} />,
        label: "Integrations",
        path: "/integrations",
      },
      {
        key: "mobile",
        icon: <Smartphone size={20} />,
        label: "Mobile App",
        path: "/mobile",
      },
      {
        key: "documents",
        icon: <FileText size={20} />,
        label: "Documents",
        path: "/documents",
      },
    ],
  },
  {
    key: "admin",
    label: "Administration",
    type: "group",
    children: [
      {
        key: "settings",
        icon: <Settings size={20} />,
        label: "Settings",
        path: "/settings",
        children: [
          {
            key: "settings-general",
            label: "General",
            path: "/settings/general",
          },
          {
            key: "settings-security",
            label: "Security",
            path: "/settings/security",
          },
          {
            key: "settings-permissions",
            label: "Permissions",
            path: "/settings/permissions",
          },
        ],
      },
      {
        key: "security",
        icon: <Shield size={20} />,
        label: "Security Center",
        path: "/security",
      },
      {
        key: "audit",
        icon: <UserCheck size={20} />,
        label: "Audit Logs",
        path: "/audit",
      },
    ],
  },
]

export const Sidebar: React.FC<SidebarProps> = ({
  collapsed,
  layoutState,
  onMenuSelect,
  onOpenChange,
  theme = "light",
  isMobile = false,
}) => {
  const navigate = useNavigate()
  const location = useLocation()

  const menuItems = useMemo(() => getModernMenuItems(), [])

  const handleMenuClick = ({ key }: { key: string }) => {
    const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
      for (const item of items) {
        if (item.key === targetKey) return item
        if (item.children) {
          const found = findMenuItem(item.children, targetKey)
          if (found) return found
        }
      }
      return null
    }

    const menuItem = findMenuItem(menuItems, key)
    if (menuItem?.path) {
      navigate(menuItem.path)
    }
  }

  useEffect(() => {
    const { selectedKey, openKeys } = findSelectedMenuItem(menuItems, location.pathname)
    if (selectedKey) {
      onMenuSelect([selectedKey])
      onOpenChange(openKeys)
    }
  }, [location.pathname, menuItems, onMenuSelect, onOpenChange])

  const convertToAntMenuItems = (items: MenuItem[]): any[] => {
    return items.map((item) => {
      if (item.type === "group") {
        return {
          type: "group",
          label: !collapsed ? (
            <Text
              style={{
                fontSize: 11,
                fontWeight: 600,
                textTransform: "uppercase",
                letterSpacing: "0.8px",
                color: theme === "dark" ? "#9CA3AF" : "#6B7280",
                padding: "16px 0 8px 0",
                display: "block",
              }}
            >
              {item.label}
            </Text>
          ) : null,
          children: item.children ? convertToAntMenuItems(item.children) : [],
        }
      }

      if (item.children && item.children.length > 0) {
        return {
          key: item.key,
          icon: item.icon,
          label: (
            <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%" }}>
              <span style={{ fontWeight: 500 }}>{item.label}</span>
              {item.badge && !collapsed && (
                <span
                  style={{
                    backgroundColor: "#EF4444",
                    color: "#FFFFFF",
                    borderRadius: "12px",
                    padding: "2px 8px",
                    fontSize: "11px",
                    fontWeight: 600,
                    minWidth: "20px",
                    height: "20px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    boxShadow: "0 2px 4px rgba(239, 68, 68, 0.3)",
                  }}
                >
                  {item.badge}
                </span>
              )}
            </div>
          ),
          children: convertToAntMenuItems(item.children),
        }
      }

      return {
        key: item.key,
        icon: item.icon,
        label: (
          <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%" }}>
            <span style={{ fontWeight: 500 }}>{item.label}</span>
            {item.badge && !collapsed && (
              <span
                style={{
                  backgroundColor: "#EF4444",
                  color: "#FFFFFF",
                  borderRadius: "12px",
                  padding: "2px 8px",
                  fontSize: "11px",
                  fontWeight: 600,
                  minWidth: "20px",
                  height: "20px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  boxShadow: "0 2px 4px rgba(239, 68, 68, 0.3)",
                }}
              >
                {item.badge}
              </span>
            )}
          </div>
        ),
      }
    })
  }

  const antMenuItems = useMemo(() => convertToAntMenuItems(menuItems), [menuItems, collapsed, theme])

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={collapsed}
      width={isMobile ? 320 : 280}
      collapsedWidth={80}
      theme={theme}
      style={{
        overflow: "auto",
        height: "100vh",
        position: "fixed",
        left: 0,
        top: isMobile ? 0 : 72,
        bottom: 0,
        zIndex: isMobile ? 1001 : 100,
        background: theme === "light" ? "#FFFFFF" : "#1F2937",
        border: "none",
        boxShadow: isMobile
          ? "0 25px 50px -12px rgba(0, 0, 0, 0.25)"
          : theme === "light"
            ? "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
            : "0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2)",
        paddingTop: isMobile ? 72 : 0,
        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
      }}
    >
      {/* Modern Logo Section */}
      <div
        style={{
          height: 72,
          padding: collapsed ? "0 16px" : "0 24px",
          background: theme === "light" ? "#F8FAFC" : "#374151",
          borderBottom: `1px solid ${theme === "light" ? "#E5E7EB" : "#4B5563"}`,
          transition: "all 0.3s ease",
          display: "flex",
          alignItems: "center",
          justifyContent: collapsed ? "center" : "flex-start",
        }}
      >
        {!collapsed ? (
          <Space align="center" size={12}>
            <div
              style={{
                width: 40,
                height: 40,
                borderRadius: 12,
                background: "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                boxShadow: "0 4px 12px rgba(59, 130, 246, 0.4)",
              }}
            >
              <Zap size={20} color="#FFFFFF" />
            </div>
            <div>
              <Text
                strong
                style={{
                  color: theme === "light" ? "#111827" : "#F9FAFB",
                  fontSize: 18,
                  fontWeight: 700,
                  lineHeight: 1,
                  letterSpacing: "-0.025em",
                }}
              >
                WorkFlow Pro
              </Text>
              <br />
              <Text
                style={{
                  color: theme === "light" ? "#6B7280" : "#9CA3AF",
                  fontSize: 12,
                  fontWeight: 500,
                  lineHeight: 1,
                }}
              >
                Business Dashboard
              </Text>
            </div>
          </Space>
        ) : (
          <div
            style={{
              width: 40,
              height: 40,
              borderRadius: 12,
              background: "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              boxShadow: "0 4px 12px rgba(59, 130, 246, 0.4)",
            }}
          >
            <Zap size={20} color="#FFFFFF" />
          </div>
        )}
      </div>

      {/* Modern Menu */}
      <Menu
        mode="inline"
        selectedKeys={layoutState.selectedKeys}
        openKeys={collapsed ? [] : layoutState.openKeys}
        onSelect={({ selectedKeys }) => onMenuSelect(selectedKeys)}
        onOpenChange={onOpenChange}
        onClick={handleMenuClick}
        items={antMenuItems}
        theme={theme}
        style={{
          border: "none",
          height: `calc(100% - 72px)`,
          background: "transparent",
          padding: "24px 0",
        }}
        inlineCollapsed={collapsed}
      />
    </Sider>
  )
}
