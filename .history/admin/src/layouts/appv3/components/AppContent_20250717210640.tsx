"use client"

import type React from "react"
import { Outlet } from "react-router"
import { useMediaQuery } from "react-responsive"
import { useThemeStore } from "@/store/themeStore"
import { theme } from "antd"

interface AppContentProps {
  collapsed: boolean
}

export const AppContent: React.FC<AppContentProps> = ({ collapsed }) => {
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const isTablet = useMediaQuery({ maxWidth: 1024 })
  const { mytheme } = useThemeStore()
  const {
    token: { colorBgLayout, colorBorder, colorBgContainer },
  } = theme.useToken()

  return (
    <main
      className="admin-content"
      style={{
        gridRow: isMobile ? "2 / -2" : "2 / -2",
        gridColumn: isMobile ? "1 / -1" : "2 / -1",
        background: mytheme === "dark" ? "#0f172a" : "#F8FAFC",
        paddingLeft: isMobile ? "16px" : "32px",
        paddingRight: isMobile ? "16px" : "24px",
        paddingTop: "24px",
        paddingBottom: "24px",
        overflow: "auto",
        minHeight: 0,
        transition: "all 200ms ease",
      }}
    >
      {/* Optimized Content Container */}
      <div
        className="w-100 position-relative"
        style={{
          minHeight: "calc(100vh - 160px)",
          background: mytheme === "dark" ? "#1a1a1a" : "#ffffff",
          borderRadius: 6,
          padding: isMobile ? "20px 16px" : "32px 28px",
          boxShadow: mytheme === "dark" ? "0 2px 8px rgba(0, 0, 0, 0.15)" : "0 2px 8px rgba(0, 0, 0, 0.06)",
          border: `1px solid ${mytheme === "dark" ? "#334155" : "#E2E8F0"}`,
          overflow: "hidden",
          transition: "all 200ms ease",
        }}
      >
        {/* Route Content */}
        <div
          style={{
            fontSize: "14px",
            lineHeight: 1.5,
            color: mytheme === "dark" ? "#F8FAFC" : "#0F172A",
          }}
        >
          <Outlet />
        </div>
      </div>
    </main>
  )
}
