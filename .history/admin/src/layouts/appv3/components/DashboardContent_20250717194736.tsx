"use client"

import type React from "react"
import { useState } from "react"
import { Row, Col, Card, Statistic, List, Avatar, Badge, Button, Typography, Progress, Tag, Space } from "antd"
import {
  UserOutlined,
  TeamOutlined,
  DollarOutlined,
  RiseOutlined,
  MailOutlined,
  Check<PERSON>ircleOutlined,
  ExclamationCircleOutlined,
  GoogleOutlined,
  SlackOutlined,
  CalendarOutlined,
  FileTextOutlined,
} from "@ant-design/icons"
import { useMediaQuery } from "react-responsive"
import { useThemeStore } from "@/store/themeStore"

const { Title, Text, Paragraph } = Typography

// Mock data
const inboxMessages = [
  {
    id: 1,
    sender: "<PERSON>",
    subject: "Q4 Budget Review Meeting",
    preview: "Hi team, I'd like to schedule a meeting to review our Q4 budget allocations...",
    time: "2 min ago",
    unread: true,
    priority: "high",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Sarah",
  },
  {
    id: 2,
    sender: "<PERSON>",
    subject: "Project Timeline Update",
    preview: "The development phase is ahead of schedule. We should be able to deliver...",
    time: "15 min ago",
    unread: true,
    priority: "medium",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Mike",
  },
  {
    id: 3,
    sender: "HR Department",
    subject: "New Employee Onboarding",
    preview: "Welcome package for new hires has been updated with the latest policies...",
    time: "1 hour ago",
    unread: false,
    priority: "low",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=HR",
  },
  {
    id: 4,
    sender: "Finance Team",
    subject: "Monthly Expense Report",
    preview: "Please find attached the monthly expense report for your review...",
    time: "3 hours ago",
    unread: false,
    priority: "medium",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Finance",
  },
]

const recentActivities = [
  {
    id: 1,
    action: "New employee registered",
    user: "Alice Cooper",
    time: "5 min ago",
    type: "user",
  },
  {
    id: 2,
    action: "Invoice #INV-2024-001 paid",
    user: "Acme Corp",
    time: "12 min ago",
    type: "payment",
  },
  {
    id: 3,
    action: "Project milestone completed",
    user: "Development Team",
    time: "1 hour ago",
    type: "project",
  },
  {
    id: 4,
    action: "System backup completed",
    user: "System",
    time: "2 hours ago",
    type: "system",
  },
]

export const DashboardContent: React.FC = () => {
  const isMobile = useMediaQuery({ maxWidth: 769 })
  const isTablet = useMediaQuery({ maxWidth: 1024 })
  const { mytheme } = useThemeStore()
  const [loading, setLoading] = useState(false)

  const handleConnectGoogle = () => {
    setLoading(true)
    // Simulate API call
    setTimeout(() => {
      setLoading(false)
    }, 2000)
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "#ff4d4f"
      case "medium":
        return "#faad14"
      case "low":
        return "#52c41a"
      default:
        return "#d9d9d9"
    }
  }

  return (
    <div style={{ width: "100%" }}>
      {/* Welcome Section */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0, fontSize: isMobile ? 24 : 32 }}>
          Welcome back, John! 👋
        </Title>
        <Paragraph style={{ fontSize: 16, margin: "8px 0 0 0", opacity: 0.8 }}>
          Here's what's happening with your business today.
        </Paragraph>
      </div>

      {/* Stats Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Employees"
              value={156}
              prefix={<UserOutlined />}
              valueStyle={{ color: "#1677ff" }}
              suffix={
                <Tag color="green" style={{ marginLeft: 8 }}>
                  +12%
                </Tag>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Active Clients"
              value={89}
              prefix={<TeamOutlined />}
              valueStyle={{ color: "#52c41a" }}
              suffix={
                <Tag color="blue" style={{ marginLeft: 8 }}>
                  +5%
                </Tag>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Monthly Revenue"
              value={284750}
              prefix={<DollarOutlined />}
              precision={0}
              valueStyle={{ color: "#faad14" }}
              suffix={
                <Tag color="green" style={{ marginLeft: 8 }}>
                  +18%
                </Tag>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Growth Rate"
              value={23.5}
              prefix={<RiseOutlined />}
              suffix="%"
              valueStyle={{ color: "#ff4d4f" }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* Inbox Section */}
        <Col xs={24} lg={14}>
          <Card
            title={
              <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                <Space>
                  <MailOutlined />
                  <span>Inbox</span>
                  <Badge count={12} size="small" />
                </Space>
                <Button type="link" size="small">
                  View All
                </Button>
              </div>
            }
            style={{ height: "100%" }}
          >
            <List
              itemLayout="horizontal"
              dataSource={inboxMessages}
              renderItem={(item) => (
                <List.Item
                  style={{
                    padding: "12px 0",
                    background: item.unread ? (mytheme === "light" ? "#f6ffed" : "#162312") : "transparent",
                    borderRadius: 8,
                    marginBottom: 8,
                    paddingLeft: item.unread ? 12 : 0,
                    paddingRight: item.unread ? 12 : 0,
                    border: item.unread ? `1px solid ${getPriorityColor(item.priority)}20` : "none",
                  }}
                >
                  <List.Item.Meta
                    avatar={
                      <Badge dot={item.unread} color={getPriorityColor(item.priority)}>
                        <Avatar src={item.avatar} />
                      </Badge>
                    }
                    title={
                      <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                        <Text strong={item.unread}>{item.subject}</Text>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {item.time}
                        </Text>
                      </div>
                    }
                    description={
                      <div>
                        <Text type="secondary" style={{ fontSize: 13 }}>
                          From: {item.sender}
                        </Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {item.preview}
                        </Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* Right Column */}
        <Col xs={24} lg={10}>
          <Space direction="vertical" size={16} style={{ width: "100%" }}>
            {/* Google Integration CTA */}
            <Card>
              <div style={{ textAlign: "center", padding: "16px 0" }}>
                <GoogleOutlined style={{ fontSize: 48, color: "#4285f4", marginBottom: 16 }} />
                <Title level={4} style={{ margin: "0 0 8px 0" }}>
                  Connect Google Workspace
                </Title>
                <Paragraph style={{ margin: "0 0 16px 0", color: "rgba(0,0,0,0.65)" }}>
                  Sync your emails, calendar, and drive to streamline your workflow and boost productivity.
                </Paragraph>
                <Button
                  type="primary"
                  size="large"
                  icon={<GoogleOutlined />}
                  loading={loading}
                  onClick={handleConnectGoogle}
                  style={{ borderRadius: 8 }}
                >
                  Connect Now
                </Button>
                <div style={{ marginTop: 12 }}>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    Secure OAuth 2.0 authentication • No data stored
                  </Text>
                </div>
              </div>
            </Card>

            {/* Quick Actions */}
            <Card title="Quick Actions" size="small">
              <Space direction="vertical" style={{ width: "100%" }}>
                <Button block icon={<UserOutlined />} style={{ textAlign: "left", height: 40 }}>
                  Add New Employee
                </Button>
                <Button block icon={<FileTextOutlined />} style={{ textAlign: "left", height: 40 }}>
                  Generate Report
                </Button>
                <Button block icon={<CalendarOutlined />} style={{ textAlign: "left", height: 40 }}>
                  Schedule Meeting
                </Button>
                <Button block icon={<SlackOutlined />} style={{ textAlign: "left", height: 40 }}>
                  Send Slack Update
                </Button>
              </Space>
            </Card>

            {/* Recent Activity */}
            <Card title="Recent Activity" size="small">
              <List
                size="small"
                dataSource={recentActivities}
                renderItem={(item) => (
                  <List.Item style={{ padding: "8px 0" }}>
                    <List.Item.Meta
                      avatar={
                        <Avatar
                          size="small"
                          style={{
                            backgroundColor:
                              item.type === "user"
                                ? "#1677ff"
                                : item.type === "payment"
                                  ? "#52c41a"
                                  : item.type === "project"
                                    ? "#faad14"
                                    : "#666",
                          }}
                        >
                          {item.type === "user" ? (
                            <UserOutlined />
                          ) : item.type === "payment" ? (
                            <DollarOutlined />
                          ) : item.type === "project" ? (
                            <CheckCircleOutlined />
                          ) : (
                            <ExclamationCircleOutlined />
                          )}
                        </Avatar>
                      }
                      title={<Text style={{ fontSize: 13 }}>{item.action}</Text>}
                      description={
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {item.user} • {item.time}
                        </Text>
                      }
                    />
                  </List.Item>
                )}
              />
            </Card>
          </Space>
        </Col>
      </Row>

      {/* System Status */}
      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card title="System Status" size="small">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={8}>
                <div style={{ textAlign: "center" }}>
                  <Progress type="circle" percent={98} size={80} status="normal" />
                  <div style={{ marginTop: 8 }}>
                    <Text strong>Server Uptime</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      99.8% this month
                    </Text>
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={8}>
                <div style={{ textAlign: "center" }}>
                  <Progress type="circle" percent={76} size={80} strokeColor="#faad14" />
                  <div style={{ marginTop: 8 }}>
                    <Text strong>Storage Used</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      76GB of 100GB
                    </Text>
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={8}>
                <div style={{ textAlign: "center" }}>
                  <Progress type="circle" percent={45} size={80} strokeColor="#52c41a" />
                  <div style={{ marginTop: 8 }}>
                    <Text strong>CPU Usage</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      Average load
                    </Text>
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  )
}
