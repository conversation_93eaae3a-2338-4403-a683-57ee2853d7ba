"use client"

import type React from "react"
import { useThemeStore } from "@/store/themeStore"

export const Footer: React.FC = () => {
  const { mytheme } = useThemeStore()

  return (
    <footer
      style={{
        position: "fixed",
        bottom: 0,
        left: 0,
        right: 0,
        height: 48,
        background: mytheme === "dark" ? "#1f2937" : "#ffffff",
        borderTop: `1px solid ${mytheme === "dark" ? "#374151" : "#e5e7eb"}`,
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        padding: "0 24px",
        zIndex: 50,
        boxShadow: "0 -2px 4px rgba(0, 0, 0, 0.05)",
        transition: "all 300ms ease-in-out",
      }}
    >
      {/* Left Section - Copyright */}
      <div
        style={{
          fontSize: 12,
          color: mytheme === "dark" ? "#9ca3af" : "#6b7280",
          fontWeight: 500,
        }}
      >
        © 2024 WorkFlow Pro Enterprise. All rights reserved.
      </div>

      {/* Right Section - Links & Status */}
      <div className="d-flex align-items-center" style={{ gap: 24 }}>
        <a
          href="#"
          style={{
            fontSize: 12,
            color: mytheme === "dark" ? "#9ca3af" : "#6b7280",
            textDecoration: "none",
            fontWeight: 500,
            transition: "color 300ms ease-in-out",
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.color = mytheme === "dark" ? "#d1d5db" : "#374151"
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.color = mytheme === "dark" ? "#9ca3af" : "#6b7280"
          }}
        >
          Privacy Policy
        </a>
        <a
          href="#"
          style={{
            fontSize: 12,
            color: mytheme === "dark" ? "#9ca3af" : "#6b7280",
            textDecoration: "none",
            fontWeight: 500,
            transition: "color 300ms ease-in-out",
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.color = mytheme === "dark" ? "#d1d5db" : "#374151"
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.color = mytheme === "dark" ? "#9ca3af" : "#6b7280"
          }}
        >
          Support
        </a>
        <div
          className="d-flex align-items-center"
          style={{
            gap: 8,
            fontSize: 12,
            color: mytheme === "dark" ? "#9ca3af" : "#6b7280",
            fontWeight: 500,
          }}
        >
          <div
            style={{
              width: 8,
              height: 8,
              borderRadius: "50%",
              backgroundColor: "#10b981",
              boxShadow: "0 0 0 2px rgba(16, 185, 129, 0.2)",
            }}
          />
          <span>System Online</span>
        </div>
      </div>
    </footer>
  )
}
