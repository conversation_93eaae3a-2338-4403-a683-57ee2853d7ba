"use client"

import type React from "react"
import { <PERSON>ton, Avatar, Dropdown, Switch, Tooltip, Input } from "antd"
import { Menu, Search, Clock, Settings, LogOut, Moon, Sun, User, ChevronDown, Command } from "lucide-react"
import { useMediaQuery } from "react-responsive"
import { useNavigate } from "react-router"
import { useThemeStore } from "@/store/themeStore"

const { Search: AntSearch } = Input

interface HeaderBarProps {
  collapsed: boolean
  onToggle: () => void
  isScrolled?: boolean
}

export const HeaderBar: React.FC<HeaderBarProps> = ({ collapsed, onToggle, isScrolled = false }) => {
  const navigate = useNavigate()
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const { mytheme, setTheme } = useThemeStore()

  const handleThemeToggle = (checked: boolean) => {
    setTheme(checked ? "dark" : "light")
  }

  const userMenuItems = [
    {
      key: "profile",
      icon: <User size={14} />,
      label: "Profile Settings",
      onClick: () => navigate("/profile"),
    },
    {
      key: "settings",
      icon: <Settings size={14} />,
      label: "Account Settings",
      onClick: () => navigate("/settings"),
    },
    {
      type: "divider" as const,
    },
    {
      key: "logout",
      icon: <LogOut size={14} />,
      label: "Sign Out",
      onClick: () => {
        console.log("Logout")
      },
    },
  ]

  return (
    <header
      className="admin-header d-flex align-items-center justify-content-between"
      style={{
        gridColumn: "1 / -1",
        height: 64,
        background: mytheme === "dark" ? "#1e293b" : "#ffffff",
        borderBottom: `1px solid ${mytheme === "dark" ? "#374151" : "#e5e7eb"}`,
        boxShadow: isScrolled
          ? "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
          : "0 2px 4px rgba(0, 0, 0, 0.05)",
        position: "sticky",
        top: 0,
        zIndex: 100,
        transition: "box-shadow 300ms ease-in-out",
        padding: "0 24px",
      }}
    >
      {/* Left Section - Menu Toggle & Logo */}
      <div className="d-flex align-items-center" style={{ gap: 16, minWidth: 0 }}>
        {/* Menu Toggle */}
        <Button
          type="text"
          icon={<Menu size={18} />}
          onClick={onToggle}
          className="d-flex align-items-center justify-content-center"
          aria-label="Toggle sidebar navigation"
          style={{
            width: 40,
            height: 40,
            color: mytheme === "dark" ? "#f9fafb" : "#374151",
            borderRadius: 8,
            transition: "all 300ms ease-in-out",
            flexShrink: 0,
            border: "1px solid transparent",
          }}
          onFocus={(e) => {
            e.currentTarget.style.outline = "2px solid #3B82F6"
            e.currentTarget.style.outlineOffset = "2px"
          }}
          onBlur={(e) => {
            e.currentTarget.style.outline = "none"
          }}
        />

        {/* Logo & Brand - No Hover Effects */}
        <div className="d-flex align-items-center" style={{ gap: 12, minWidth: 0 }}>
          <div
            className="d-flex align-items-center justify-content-center"
            style={{
              width: 36,
              height: 36,
              borderRadius: 8,
              background: "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)",
              boxShadow: "0 2px 4px rgba(59, 130, 246, 0.15)",
              flexShrink: 0,
            }}
          >
            <div
              className="d-flex align-items-center justify-content-center fw-bold"
              style={{
                width: 20,
                height: 20,
                background: "#ffffff",
                borderRadius: 4,
                fontSize: 11,
                color: "#3B82F6",
              }}
            >
              W
            </div>
          </div>
          {!isMobile && (
            <div style={{ minWidth: 0 }}>
              <div
                className="fw-semibold"
                style={{
                  fontSize: 16,
                  color: mytheme === "dark" ? "#f9fafb" : "#111827",
                  lineHeight: 1.2,
                  letterSpacing: "-0.01em",
                }}
              >
                WorkFlow Pro
              </div>
              <div
                style={{
                  fontSize: 12,
                  fontWeight: 500,
                  color: mytheme === "dark" ? "#d1d5db" : "#6b7280",
                  lineHeight: 1,
                }}
              >
                Enterprise Dashboard
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Center Section - Reduced Search Bar (20-30% smaller) */}
      {!isMobile && (
        <div className="flex-grow-1" style={{ maxWidth: 360, minWidth: 200 }}>
          <AntSearch
            placeholder="Search projects, tasks, team members..."
            allowClear
            size="middle"
            style={{
              borderRadius: 8,
              width: "100%",
              height: 36,
            }}
            prefix={
              <Search
                size={14}
                style={{
                  color: mytheme === "dark" ? "#9ca3af" : "#6b7280",
                  marginRight: 8,
                }}
              />
            }
            suffix={
              <div
                className="d-flex align-items-center fw-medium"
                style={{
                  gap: 4,
                  color: mytheme === "dark" ? "#9ca3af" : "#6b7280",
                  fontSize: 10,
                  padding: "2px 6px",
                  background: mytheme === "dark" ? "rgba(255, 255, 255, 0.08)" : "rgba(0, 0, 0, 0.04)",
                  borderRadius: 4,
                  border: `1px solid ${mytheme === "dark" ? "#4b5563" : "#e5e7eb"}`,
                }}
              >
                <Command size={8} />
                <span>K</span>
              </div>
            }
          />
        </div>
      )}

      {/* Right Section - No Hover Effects */}
      <div className="d-flex align-items-center" style={{ gap: 16 }}>
        {/* Mobile Search */}
        {isMobile && (
          <Button
            type="text"
            icon={<Search size={18} />}
            className="d-flex align-items-center justify-content-center"
            aria-label="Open search"
            style={{
              width: 40,
              height: 40,
              color: mytheme === "dark" ? "#f9fafb" : "#374151",
              borderRadius: 8,
              flexShrink: 0,
            }}
          />
        )}

        {/* Theme Toggle */}
        <Tooltip title={`Switch to ${mytheme === "light" ? "dark" : "light"} mode`}>
          <Switch
            checked={mytheme === "dark"}
            onChange={handleThemeToggle}
            checkedChildren={<Moon size={12} />}
            unCheckedChildren={<Sun size={12} />}
            size="default"
            style={{ flexShrink: 0 }}
          />
        </Tooltip>

        {/* Time Clock with Badge - No Hover Effects */}
        <Tooltip title="Time Tracking">
          <div style={{ position: "relative", display: "inline-block" }}>
            <Button
              type="text"
              icon={<Clock size={18} />}
              className="d-flex align-items-center justify-content-center"
              aria-label="Time tracking"
              style={{
                width: 40,
                height: 40,
                color: mytheme === "dark" ? "#f9fafb" : "#374151",
                borderRadius: 8,
                flexShrink: 0,
              }}
              onFocus={(e) => {
                e.currentTarget.style.outline = "2px solid #3B82F6"
                e.currentTarget.style.outlineOffset = "2px"
              }}
              onBlur={(e) => {
                e.currentTarget.style.outline = "none"
              }}
            />
            <div
              style={{
                position: "absolute",
                top: "-2px",
                right: "-2px",
                width: "20px",
                height: "20px",
                backgroundColor: "#3b82f6",
                color: "#ffffff",
                fontSize: "11px",
                fontWeight: 600,
                borderRadius: "10px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                boxShadow: "0 2px 4px rgba(59, 130, 246, 0.3)",
                border: `2px solid ${mytheme === "dark" ? "#1e293b" : "#ffffff"}`,
                lineHeight: 1,
              }}
            >
              8
            </div>
          </div>
        </Tooltip>

        {/* User Profile - No Hover Effects */}
        <Dropdown menu={{ items: userMenuItems }} placement="bottomRight" arrow>
          <div
            className="d-flex align-items-center"
            style={{
              gap: 12,
              padding: "6px 12px",
              borderRadius: 8,
              cursor: "pointer",
              background: "transparent",
              border: `1px solid ${mytheme === "dark" ? "#4b5563" : "#d1d5db"}`,
              flexShrink: 0,
            }}
          >
            <Avatar
              size={32}
              style={{
                background: "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)",
                fontSize: 13,
                fontWeight: 600,
                border: "2px solid rgba(59, 130, 246, 0.2)",
              }}
            >
              JD
            </Avatar>
            {!isMobile && (
              <div style={{ textAlign: "left", lineHeight: 1.2, minWidth: 0 }}>
                <div
                  className="fw-semibold"
                  style={{
                    fontSize: 13,
                    color: mytheme === "dark" ? "#f9fafb" : "#111827",
                    whiteSpace: "nowrap",
                  }}
                >
                  John Doe
                </div>
                <div
                  style={{
                    fontSize: 11,
                    color: mytheme === "dark" ? "#d1d5db" : "#6b7280",
                    fontWeight: 500,
                    whiteSpace: "nowrap",
                  }}
                >
                  Administrator
                </div>
              </div>
            )}
            {!isMobile && (
              <ChevronDown
                size={14}
                style={{
                  color: mytheme === "dark" ? "#9ca3af" : "#6b7280",
                  flexShrink: 0,
                }}
              />
            )}
          </div>
        </Dropdown>
      </div>
    </header>
  )
}
