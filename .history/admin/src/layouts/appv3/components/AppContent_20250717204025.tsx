"use client"

import type React from "react"
import { Outlet } from "react-router"
import { useMediaQuery } from "react-responsive"
import { useThemeStore } from "@/store/themeStore"
import { theme } from "antd"
import FooterNav from "@/layouts/app/FooterNav"

interface AppContentProps {
  collapsed: boolean
}

export const AppContent: React.FC<AppContentProps> = ({ collapsed }) => {
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const { mytheme } = useThemeStore()
  const {
    token: { colorBgLayout, colorBorder, colorBgContainer },
  } = theme.useToken()

  return (
    <main
      className="admin-content"
      style={{
        gridRow: isMobile ? "2 / -1" : "2 / -1",
        gridColumn: isMobile ? "1 / -1" : "2 / -1",
        background: colorBgLayout,
        padding: isMobile ? "16px 12px" : "24px 16px",
        overflow: "auto",
        minHeight: 0,
      }}
    >
      {/* Content Container */}
      <div
        style={{
          width: "100%",
          minHeight: "calc(100vh - 140px)",
          background: colorBgContainer,
          borderRadius: 8,
          padding: isMobile ? "16px 12px" : "24px 16px",
          boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
          border: `1px solid ${colorBorder}`,
          position: "relative",
          overflow: "hidden",
        }}
      >
        {/* Route Content */}
        <Outlet />
      </div>

      {/* Footer */}
      <FooterNav
        style={{
          textAlign: "center",
          background: "transparent",
          padding: isMobile ? "16px 12px" : "16px 16px",
          color: mytheme === "light" ? "rgba(107, 114, 128, 0.6)" : "rgba(156, 163, 175, 0.6)",
          fontSize: "11px",
          fontWeight: 500,
          marginTop: "16px",
        }}
      />
    </main>
  )
}
