"use client"

import type React from "react"
import { useEffect, useMemo } from "react"
import { <PERSON><PERSON>, But<PERSON> } from "antd"
import { useNavigate, useLocation } from "react-router"
import {
  LayoutDashboard,
  Users,
  Building2,
  CreditCard,
  Settings,
  FileText,
  Smartphone,
  ChevronLeft,
  ChevronRight,
} from "lucide-react"
import { getOriginalMenuItems, findSelectedMenuItem } from "../utils/menuAdapter"
import { useThemeStore } from "@/store/themeStore"

interface SidebarProps {
  collapsed: boolean
  layoutState: any
  onMenuSelect: (keys: string[]) => void
  onOpenChange: (keys: string[]) => void
  theme?: "light" | "dark"
  isMobile?: boolean
}

// Create a mapping from original menu keys to lucide icons
const iconMap: Record<string, React.ReactNode> = {
  dashboard: <LayoutDashboard />,
  clients: <Building2 />,
  employees: <Users />,
  finance: <CreditCard />,
  reports: <FileText />,
  settings: <Settings />,
  devices: <Smartphone />,
}

export const Sidebar: React.FC<SidebarProps> = ({
  collapsed,
  layoutState,
  onMenuSelect,
  onOpenChange,
  theme = "light",
  isMobile = false,
}) => {
  const navigate = useNavigate()
  const location = useLocation()
  const { setCollapsed } = useThemeStore()

  // Get original menu items and convert them for the custom UI
  const originalItems = useMemo(() => getOriginalMenuItems(location.pathname), [location.pathname])

  const handleMenuClick = ({ key }: { key: string }) => {
    // Find the menu item and navigate to its path
    const findMenuItem = (items: any[], targetKey: string): any => {
      for (const item of items) {
        if (item.key === targetKey) return item
        if (item.children) {
          const found = findMenuItem(item.children, targetKey)
          if (found) return found
        }
      }
      return null
    }

    const menuItem = findMenuItem(originalItems, key)
    if (menuItem?.label?.props?.to) {
      navigate(menuItem.label.props.to)
      if (isMobile) {
        setCollapsed(true)
      }
    }
  }

  return (
    <Sider
      ref={nodeRef}
      trigger={null}
      collapsible
      collapsed={collapsed}
      width={isMobile ? 280 : 240}
      theme={mytheme}
      style={{
        overflow: 'auto',
        height: '100vh',
        position: 'fixed',
        left: 0,
        top: isMobile ? 0 : 64,
        bottom: 0,
        zIndex: isMobile ? 200 : 100,
        background: mytheme === 'light' ? '#ffffff' : '#001529',
        border: 'none',
        boxShadow: isMobile ? '4px 0 12px rgba(0, 0, 0, 0.15)' : '2px 0 8px rgba(0,0,0,0.1)',
        paddingTop: isMobile ? 50 : 0,
        transition: 'all 0.2s ease',
      }}
    >
      {/* Logo Section */}
      <div
        className="d-flex align-items-center justify-content-center border-bottom"
        style={{
          height: isMobile ? 50 : 64,
          padding: '0 16px',
          background: mytheme === 'light' ? '#fafafa' : '#002140'
        }}
      >
        {!collapsed ? (
          <Logo
            color={mytheme === 'light' ? 'dark' : 'white'}
            asLink
            href="/"
            imgSize={{ w: 28, h: 28 }}
          />
        ) : (
          <Logo
            color={mytheme === 'light' ? 'dark' : 'white'}
            asLink
            href="/"
            imgSize={{ w: 24, h: 24 }}
          />
        )}
      </div>

      {/* Menu */}
      <Menu
        mode="inline"
        openKeys={collapsed ? [] : openKeys}
        onOpenChange={onOpenChange}
        selectedKeys={[current]}
        items={items as MenuProps['items']}
        theme={mytheme}
        style={{
          border: 'none',
          height: `calc(100% - ${isMobile ? 50 : 64}px)`,
          background: 'transparent'
        }}
        inlineCollapsed={collapsed}
      />
    </Sider>
  );
};
