"use client"

import type React from "react"
import { useState } from "react"
import { Button, Avatar, Dropdown, Switch, Tooltip, Input, AutoComplete, message } from "antd"
import { Menu, Search, Clock, LogOut, Moon, Sun, User, ChevronDown, Command } from "lucide-react"
import { useMediaQuery } from "react-responsive"
import { useNavigate, Link } from "react-router"
import { useThemeStore } from "@/store/themeStore"
import { useAuthStore } from "@/store/authStore"

import EmployeeAvatar from "@/components/EmployeeAvatar"
import { api } from "@/utils/api"
import { PRIMARY_COLOR, getRoleById } from "@/utils/consts"
import Cookies from "js-cookie"



interface HeaderBarProps {
  collapsed: boolean
  onToggle: () => void
  isScrolled?: boolean
}

export const HeaderBar: React.FC<HeaderBarProps> = ({ collapsed, onToggle, isScrolled = false }) => {
  const navigate = useNavigate()
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const { mytheme, setTheme } = useThemeStore()
  const { user, logout } = useAuthStore()
  const [settingsOpen, setSettingsOpen] = useState(false)

  // Search functionality state
  const [searchValue, setSearchValue] = useState('')
  const [isSearchLoading, setIsSearchLoading] = useState(false)
  const [searchOptions, setSearchOptions] = useState<any[]>([])

  const handleThemeToggle = (checked: boolean) => {
    setTheme(checked ? "dark" : "light")
  }

  // Search functionality
  const fetchSearchData = async (keyword: string) => {
    try {
      setIsSearchLoading(true)
      const res = await api('NavSearch', {
        account_id: user?.account?.id,
        keyword,
      })

      const { status, data } = res || {}
      if (status === 'success') {
        const newOptions = data?.map((item: any) => {
          const { id, avatar, photo, first_name, last_name, client, department, location } = item

          return {
            value: item.path,
            label: (
              <Link onClick={() => onSearchSelect()} to={`/employees/${id}/timecards`} key={id} className="text-decoration-none">
                <div className="d-flex align-items-center" style={{ padding: isMobile ? '8px 4px' : '4px 0' }}>
                  <EmployeeAvatar
                    size={isMobile ? 40 : 50}
                    id={id}
                    photo={photo}
                    avatar={avatar}
                  />
                  <div className="ms-2" style={{ minWidth: 0, flex: 1 }}>
                    <div className="fw-bold" style={{
                      fontSize: isMobile ? '14px' : '16px',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis'
                    }}>
                      {first_name} {last_name}
                    </div>
                    <div
                      className="text-muted small"
                      style={{
                        marginTop: -2,
                        fontSize: isMobile ? '12px' : '13px',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis'
                      }}
                    >
                      {client ? (
                        <>
                          {client} - {department} {location && <i>({location})</i>}
                        </>
                      ) : (
                        "No active assignment"
                      )}
                    </div>
                  </div>
                </div>
              </Link>
            ),
            search: `${first_name} ${last_name} ${client} ${department} ${location}`,
          }
        })
        setSearchOptions(newOptions)
      }
    } catch (error) {
      console.error('[NavSearch] fetch error:', error)
      setSearchOptions([])
    } finally {
      setIsSearchLoading(false)
    }
  }

  const onSearch = async (searchText: string) => {
    setSearchValue(searchText)
    if (searchText.length >= 3) {
      await fetchSearchData(searchText)
    } else {
      setSearchOptions([])
    }
  }

  const onSearchSelect = () => {
    setSearchValue('')
    setSearchOptions([])
  }

  const userMenuItems = [
    {
      key: "user-profile-link",
      icon: <User size={14} />,
      label: "profile",
      onClick: () => navigate("/profile"),
    },
    {
      key: "user-settings-link",
      icon: <Settings size={14} />,
      label: "settings",
      onClick: () => setSettingsOpen(true),
    },
    {
      type: "divider" as const,
    },
    {
      key: "user-logout-link",
      icon: <LogOut size={14} />,
      label: "logout",
      danger: true,
      onClick: () => {
        message.open({
          type: 'loading',
          content: 'signing you out',
        })

        setTimeout(() => {
          Cookies.remove('jwt')
          Cookies.remove('google_access_token')
          logout()

          message.destroy()
          message.success('signed out successfully')
          navigate('/auth/signin')
        }, 1000)
      },
    },
  ]

  return (
    <>
      <header
        className="admin-header d-flex align-items-center justify-content-between"
        style={{
          gridColumn: "1 / -1",
          height: 64,
          background: "#1e293b", // Keep header dark themed
          borderBottom: "1px solid #334155",
          boxShadow: isScrolled
            ? "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
            : "0 2px 4px rgba(0, 0, 0, 0.05)",
          position: "sticky",
          top: 0,
          zIndex: 100,
          transition: "box-shadow 300ms ease-in-out", // Smooth transitions
          padding: "0 24px",
        }}
      >
        {/* Left Section - Menu Toggle & Logo */}
        <div className="d-flex align-items-center" style={{ gap: 16, minWidth: 0 }}>
          {/* Menu Toggle */}
          <Button
            type="text"
            icon={<Menu size={18} />}
            onClick={onToggle}
            className="d-flex align-items-center justify-content-center"
            aria-label="Toggle sidebar navigation"
            style={{
              width: 40,
              height: 40,
              color: "#ffffff",
              borderRadius: 8,
              transition: "all 300ms ease-in-out", // Smooth transitions
              flexShrink: 0,
              border: "none", // Remove border
              background: "transparent",
            }}
            onFocus={(e) => {
              e.currentTarget.style.outline = "2px solid #3B82F6"
              e.currentTarget.style.outlineOffset = "2px"
            }}
            onBlur={(e) => {
              e.currentTarget.style.outline = "none"
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = "rgba(255, 255, 255, 0.1)"
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = "transparent"
            }}
          />

          {/* Logo & Brand */}
          <div className="d-flex align-items-center" style={{ gap: 12, minWidth: 0 }}>
            <div
              className="d-flex align-items-center justify-content-center"
              style={{
                width: 36,
                height: 36,
                borderRadius: 8,
                background: "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)", // Header blue accent
                boxShadow: "0 2px 4px rgba(59, 130, 246, 0.15)",
                flexShrink: 0,
              }}
            >
              <div
                className="d-flex align-items-center justify-content-center fw-bold"
                style={{
                  width: 20,
                  height: 20,
                  background: "#ffffff",
                  borderRadius: 4,
                  fontSize: 11,
                  color: "#3B82F6",
                }}
              >
                W
              </div>
            </div>
            {!isMobile && (
              <div style={{ minWidth: 0 }}>
                <div
                  className="fw-semibold"
                  style={{
                    fontSize: 16,
                    color: "#ffffff",
                    lineHeight: 1.2,
                    letterSpacing: "-0.01em",
                  }}
                >
                  WorkFlow Pro
                </div>
                <div
                  style={{
                    fontSize: 12,
                    fontWeight: 500,
                    color: "#cbd5e1",
                    lineHeight: 1,
                  }}
                >
                  Enterprise Dashboard
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Center Section - Functional Search Bar */}
        {!isMobile && (
          <div className="flex-grow-1" style={{ maxWidth: 420, minWidth: 280 }}>
            <AutoComplete
              options={searchOptions}
              onSelect={onSearchSelect}
              onSearch={onSearch}
              value={searchValue}
              filterOption={false}
              style={{
                width: '100%',
              }}
              styles={{
                popup: {
                  root: {
                    maxHeight: '400px',
                    overflowY: 'auto',
                  }
                }
              }}
              popupMatchSelectWidth={true}
              placement="bottomLeft"
            >
              <Input
                placeholder="Search employees"
                allowClear={false}
                prefix={
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      width: 18,
                      height: 18,
                      borderRadius: 4,
                      background: "rgba(59, 130, 246, 0.15)",
                      marginRight: 12,
                    }}
                  >
                    <Search
                      size={12}
                      style={{
                        color: "#3B82F6",
                      }}
                    />
                  </div>
                }
                suffix={
                  <div
                    className="d-flex align-items-center fw-semibold"
                    style={{
                      gap: 4,
                      color: "#94a3b8",
                      fontSize: 10,
                      padding: "3px 6px",
                      background: "rgba(148, 163, 184, 0.15)",
                      borderRadius: 4,
                      backdropFilter: "blur(4px)",
                    }}
                  >
                    <Command size={8} />
                    <span>K</span>
                  </div>
                }
                style={{
                  borderRadius: 8,
                  width: "100%",
                  height: 34,
                  background: "#334155",
                  border: "none",
                  color: "#f7fafc",
                  fontSize: 14,
                  fontWeight: 500,
                  transition: "all 300ms ease-in-out",
                  boxShadow: "inset 0 1px 3px rgba(0, 0, 0, 0.2)",
                }}
                onFocus={(e) => {
                  e.currentTarget.style.boxShadow =
                    "inset 0 1px 3px rgba(0, 0, 0, 0.2), 0 0 0 2px rgba(59, 130, 246, 0.15)"
                }}
                onBlur={(e) => {
                  e.currentTarget.style.boxShadow = "inset 0 1px 3px rgba(0, 0, 0, 0.2)"
                }}
              />
            </AutoComplete>
          </div>
        )}

        {/* Right Section */}
        <div className="d-flex align-items-center" style={{ gap: 16 }}>
          {/* Mobile Search */}
          {isMobile && (
            <Button
              type="text"
              icon={<Search size={18} />}
              className="d-flex align-items-center justify-content-center"
              aria-label="Open search"
              style={{
                width: 40,
                height: 40,
                color: "#ffffff",
                borderRadius: 8,
                flexShrink: 0,
                transition: "all 300ms ease-in-out",
                border: "none",
                background: "transparent",
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = "rgba(255, 255, 255, 0.1)"
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = "transparent"
              }}
            />
          )}



          {/* Theme Toggle with Header-Matching Colors */}
          <Tooltip title={`Switch to ${mytheme === "light" ? "dark" : "light"} mode`}>
            <Switch
              checked={mytheme === "dark"}
              onChange={handleThemeToggle}
              checkedChildren={<Moon size={12} />}
              unCheckedChildren={<Sun size={12} />}
              size="default"
              style={{
                flexShrink: 0,
                transition: "all 300ms ease-in-out",
                backgroundColor: mytheme === "dark" ? "#3B82F6" : "#475569", // Header-matching colors
              }}
            />
          </Tooltip>

          {/* Time Clock with Badge */}
          <Tooltip title="Time Tracking">
            <div style={{ position: "relative", display: "inline-block" }}>
              <Button
                type="text"
                icon={<Clock size={18} />}
                className="d-flex align-items-center justify-content-center"
                aria-label="Time tracking"
                style={{
                  width: 40,
                  height: 40,
                  color: "#ffffff",
                  borderRadius: 8,
                  flexShrink: 0,
                  transition: "all 300ms ease-in-out",
                  border: "none",
                  background: "transparent",
                }}
                onFocus={(e) => {
                  e.currentTarget.style.outline = "2px solid #3B82F6"
                  e.currentTarget.style.outlineOffset = "2px"
                }}
                onBlur={(e) => {
                  e.currentTarget.style.outline = "none"
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = "rgba(255, 255, 255, 0.1)"
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = "transparent"
                }}
              />
              <div
                style={{
                  position: "absolute",
                  top: "-2px",
                  right: "-2px",
                  width: "20px",
                  height: "20px",
                  backgroundColor: "#3b82f6",
                  color: "#ffffff",
                  fontSize: "11px",
                  fontWeight: 600,
                  borderRadius: "10px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  boxShadow: "0 2px 4px rgba(59, 130, 246, 0.3)",
                  border: "2px solid #1e293b",
                  lineHeight: 1,
                }}
              >
                8
              </div>
            </div>
          </Tooltip>

          {/* User Profile - No Border, Seamless with Header */}
          <Dropdown menu={{ items: userMenuItems }} placement="bottomRight" arrow>
            <div
              className="d-flex align-items-center"
              style={{
                gap: 12,
                padding: "8px 16px",
                borderRadius: 10,
                cursor: "pointer",
                background: "rgba(255, 255, 255, 0.05)", // Subtle background
                border: "none", // No border - seamless with header
                flexShrink: 0,
                transition: "all 300ms ease-in-out",
                backdropFilter: "blur(8px)",
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = "rgba(255, 255, 255, 0.08)"
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = "rgba(255, 255, 255, 0.05)"
              }}
            >
              {user?.avatar ? (
                <img
                  src="https://avatars.githubusercontent.com/u/33683226?v=4"
                  alt="user profile photo"
                  height={32}
                  width={32}
                  style={{
                    borderRadius: 16,
                    objectFit: 'cover',
                    border: "2px solid rgba(59, 130, 246, 0.2)",
                    boxShadow: "0 2px 4px rgba(59, 130, 246, 0.15)",
                  }}
                />
              ) : (
                <Avatar
                  size={32}
                  style={{
                    backgroundColor: PRIMARY_COLOR,
                    fontSize: 13,
                    fontWeight: 600,
                    border: "2px solid rgba(59, 130, 246, 0.2)",
                    boxShadow: "0 2px 4px rgba(59, 130, 246, 0.15)",
                  }}
                >
                  {user?.name?.charAt(0).toUpperCase()}
                </Avatar>
              )}
              {!isMobile && (
                <div style={{ textAlign: "left", lineHeight: 1.2, minWidth: 0 }}>
                  <div
                    className="fw-semibold"
                    style={{
                      fontSize: 13,
                      color: "#ffffff",
                      whiteSpace: "nowrap",
                    }}
                  >
                    {user?.name} {user?.last_name}
                  </div>
                  <div
                    style={{
                      fontSize: 11,
                      color: "#cbd5e1",
                      fontWeight: 500,
                      whiteSpace: "nowrap",
                    }}
                  >
                    {user?.role ? getRoleById(user.role)?.short_name || 'Administrator' : 'Administrator'}
                  </div>
                </div>
              )}
              {!isMobile && (
                <ChevronDown
                  size={14}
                  style={{
                    color: "#94a3b8",
                    flexShrink: 0,
                    transition: "transform 300ms ease-in-out",
                  }}
                />
              )}
            </div>
          </Dropdown>
        </div>
      </header>


    </>
  )
}
