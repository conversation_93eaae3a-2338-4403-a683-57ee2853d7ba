import React, { useRef } from 'react';
import { Layout, FloatButton } from 'antd';
import { Outlet } from 'react-router';
import { useMediaQuery } from 'react-responsive';
import { useThemeStore } from '@/store/themeStore';
import FooterNav from '@/layouts/app/FooterNav';

const { Content } = Layout;

interface AppContentProps {
  collapsed: boolean;
}

export const AppContent: React.FC<AppContentProps> = ({ collapsed }) => {
  const floatBtnRef = useRef<HTMLDivElement>(null);
  const isMobile = useMediaQuery({ maxWidth: 769 });
  const isTablet = useMediaQuery({ maxWidth: 1024 });
  const { mytheme } = useThemeStore();

  return (
    <Layout 
      style={{
        marginLeft: isMobile ? 0 : (collapsed ? 80 : 240),
        marginTop: isMobile ? 50 : 55,
        minHeight: `calc(100vh - ${isMobile ? 50 : 55}px)`,
        transition: 'all 0.2s ease',
        background: mytheme === 'light' ? '#f5f5f5' : '#141414',
      }}
    >
      <Content
        style={{
          padding: isMobile ? '12px' : (isTablet ? '20px 24px' : '24px 32px'),
          minHeight: 360,
          maxWidth: '100%',
          overflowX: 'hidden',
          background: 'transparent',
        }}
      >
        <div style={{ width: '100%', minWidth: 0 }}>
          <Outlet />
        </div>
        
        <div ref={floatBtnRef}>
          <FloatButton.BackTop
            style={{
              right: isMobile ? 16 : 24,
              bottom: isMobile ? 16 : 24,
              width: isMobile ? 40 : 48,
              height: isMobile ? 40 : 48
            }}
          />
        </div>
      </Content>

      <FooterNav
        style={{
          textAlign: 'center',
          background: 'transparent',
          padding: isMobile ? '8px' : '16px',
          color: mytheme === 'light' ? '#666' : '#999'
        }}
      />
    </Layout>
  );
};
