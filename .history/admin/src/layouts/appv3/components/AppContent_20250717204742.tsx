"use client"

import type React from "react"
import { Outlet } from "react-router"
import { useMediaQuery } from "react-responsive"
import { useThemeStore } from "@/store/themeStore"
import { theme } from "antd"
import FooterNav from "@/layouts/app/FooterNav"

interface AppContentProps {
  collapsed: boolean
}

export const AppContent: React.FC<AppContentProps> = ({ collapsed }) => {
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const { mytheme } = useThemeStore()
  const {
    token: { colorBgLayout, colorBorder, colorBgContainer },
  } = theme.useToken()

  return (
    <main
      className="admin-content"
      style={{
        gridRow: isMobile ? "2 / -1" : "2 / -1",
        gridColumn: isMobile ? "1 / -1" : "2 / -1",
        background: mytheme === "dark" ? "#0a0a0a" : "#fafafa",
        padding: isMobile ? "12px 8px" : "16px 12px",
        overflow: "auto",
        minHeight: 0,
        transition: "all 300ms ease-in-out",
      }}
    >
      {/* Content Container */}
      <div
        className="w-100 position-relative"
        style={{
          minHeight: "calc(100vh - 120px)",
          background: mytheme === "dark" ? "#1a1a1a" : "#ffffff",
          borderRadius: 8,
          padding: isMobile ? "12px 8px" : "16px 12px",
          boxShadow: mytheme === "dark" ? "0 1px 3px rgba(0, 0, 0, 0.3)" : "0 1px 3px rgba(0, 0, 0, 0.1)",
          border: `1px solid ${mytheme === "dark" ? "#374151" : "#e5e7eb"}`,
          overflow: "hidden",
          transition: "all 300ms ease-in-out",
        }}
      >
        {/* Route Content */}
        <Outlet />
      </div>

      {/* Footer */}
      <FooterNav
        className="text-center"
        style={{
          background: "transparent",
          padding: isMobile ? "12px 8px" : "12px 12px",
          color: mytheme === "light" ? "rgba(107, 114, 128, 0.6)" : "rgba(156, 163, 175, 0.6)",
          fontSize: "11px",
          fontWeight: 500,
          marginTop: "12px",
          transition: "all 300ms ease-in-out",
        }}
      />
    </main>
  )
}
