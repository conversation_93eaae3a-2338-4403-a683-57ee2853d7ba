"use client"

import type React from "react"
import { useEffect, useMemo } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Button } from "antd"
import { useNavigate, useLocation } from "react-router"
import {
  LayoutDashboard,
  BarChart3,
  Users,
  Building2,
  CreditCard,
  Settings,
  Mail,
  MessageSquare,
  Bell,
  Smartphone,
  FileText,
  Calendar,
  Shield,
  Zap,
  TrendingUp,
  UserCheck,
  Briefcase,
  ChevronLeft,
  ChevronRight,
} from "lucide-react"
import { findSelectedMenuItem } from "../utils/menuAdapter"
import type { MenuItem, LayoutState } from "../types"
import { useThemeStore } from "@/store/themeStore"

const { Text } = Typography

interface SidebarProps {
  collapsed: boolean
  layoutState: LayoutState
  onMenuSelect: (keys: string[]) => void
  onOpenChange: (keys: string[]) => void
  theme?: "light" | "dark"
  isMobile?: boolean
}

// Ultra-compact menu structure
const getUltraCompactMenuItems = (): MenuItem[] => [
  {
    key: "overview",
    label: "Overview",
    type: "group",
    children: [
      {
        key: "dashboard",
        icon: <LayoutDashboard size={16} />,
        label: "Dashboard",
        path: "/dashboard",
      },
      {
        key: "analytics",
        icon: <BarChart3 size={16} />,
        label: "Analytics",
        path: "/analytics",
      },
    ],
  },
  {
    key: "workspace",
    label: "Workspace",
    type: "group",
    children: [
      {
        key: "inbox",
        icon: <Mail size={16} />,
        label: "Inbox",
        path: "/inbox",
        badge: 8,
      },
      {
        key: "messages",
        icon: <MessageSquare size={16} />,
        label: "Messages",
        path: "/messages",
        badge: 3,
      },
      {
        key: "notifications",
        icon: <Bell size={16} />,
        label: "Notifications",
        path: "/notifications",
        badge: 5,
      },
      {
        key: "calendar",
        icon: <Calendar size={16} />,
        label: "Calendar",
        path: "/calendar",
      },
    ],
  },
  {
    key: "management",
    label: "Management",
    type: "group",
    children: [
      {
        key: "employees",
        icon: <Users size={16} />,
        label: "Team Members",
        path: "/employees",
        children: [
          {
            key: "employees-directory",
            label: "Directory",
            path: "/employees/directory",
          },
          {
            key: "employees-attendance",
            label: "Attendance",
            path: "/employees/attendance",
          },
        ],
      },
      {
        key: "clients",
        icon: <Building2 size={16} />,
        label: "Clients",
        path: "/clients",
        children: [
          {
            key: "clients-active",
            label: "Active Clients",
            path: "/clients/active",
          },
          {
            key: "clients-prospects",
            label: "Prospects",
            path: "/clients/prospects",
          },
        ],
      },
      {
        key: "projects",
        icon: <Briefcase size={16} />,
        label: "Projects",
        path: "/projects",
      },
    ],
  },
  {
    key: "finance",
    label: "Finance",
    type: "group",
    children: [
      {
        key: "billing",
        icon: <CreditCard size={16} />,
        label: "Billing",
        path: "/billing",
        children: [
          {
            key: "billing-invoices",
            label: "Invoices",
            path: "/billing/invoices",
          },
          {
            key: "billing-payments",
            label: "Payments",
            path: "/billing/payments",
          },
        ],
      },
      {
        key: "reports",
        icon: <TrendingUp size={16} />,
        label: "Reports",
        path: "/reports",
      },
    ],
  },
  {
    key: "tools",
    label: "Tools",
    type: "group",
    children: [
      {
        key: "integrations",
        icon: <Zap size={16} />,
        label: "Integrations",
        path: "/integrations",
      },
      {
        key: "mobile",
        icon: <Smartphone size={16} />,
        label: "Mobile App",
        path: "/mobile",
      },
      {
        key: "documents",
        icon: <FileText size={16} />,
        label: "Documents",
        path: "/documents",
      },
    ],
  },
  {
    key: "admin",
    label: "Administration",
    type: "group",
    children: [
      {
        key: "settings",
        icon: <Settings size={16} />,
        label: "Settings",
        path: "/settings",
        children: [
          {
            key: "settings-general",
            label: "General",
            path: "/settings/general",
          },
          {
            key: "settings-security",
            label: "Security",
            path: "/settings/security",
          },
        ],
      },
      {
        key: "security",
        icon: <Shield size={16} />,
        label: "Security",
        path: "/security",
      },
      {
        key: "audit",
        icon: <UserCheck size={16} />,
        label: "Audit Logs",
        path: "/audit",
      },
    ],
  },
]

export const Sidebar: React.FC<SidebarProps> = ({
  collapsed,
  layoutState,
  onMenuSelect,
  onOpenChange,
  theme = "light",
  isMobile = false,
}) => {
  const navigate = useNavigate()
  const location = useLocation()
  const { setCollapsed } = useThemeStore()

  const menuItems = useMemo(() => getUltraCompactMenuItems(), [])

  const handleMenuClick = ({ key }: { key: string }) => {
    const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
      for (const item of items) {
        if (item.key === targetKey) return item
        if (item.children) {
          const found = findMenuItem(item.children, targetKey)
          if (found) return found
        }
      }
      return null
    }

    const menuItem = findMenuItem(menuItems, key)
    if (menuItem?.path) {
      navigate(menuItem.path)
    }
  }

  useEffect(() => {
    const { selectedKey, openKeys } = findSelectedMenuItem(menuItems, location.pathname)
    if (selectedKey) {
      onMenuSelect([selectedKey])
      onOpenChange(openKeys)
    }
  }, [location.pathname, menuItems, onMenuSelect, onOpenChange])

  const convertToAntMenuItems = (items: MenuItem[]): any[] => {
    return items.map((item) => {
      if (item.type === "group") {
        return {
          type: "group",
          label: !collapsed ? (
            <>
              <div
                style={{
                  height: 1,
                  background: theme === "dark" ? "#334155" : "#E2E8F0",
                  margin: "8px 0 6px 0",
                }}
              />
              <Text
                className="text-uppercase fw-semibold"
                style={{
                  fontSize: 11,
                  letterSpacing: "0.5px",
                  color: theme === "dark" ? "#94A3B8" : "#64748B",
                  padding: "0 0 4px 0",
                  display: "block",
                }}
              >
                {item.label}
              </Text>
            </>
          ) : (
            <div
              style={{
                height: 1,
                background: theme === "dark" ? "#334155" : "#E2E8F0",
                margin: "8px 4px",
              }}
            />
          ),
          children: item.children ? convertToAntMenuItems(item.children) : [],
        }
      }

      if (item.children && item.children.length > 0) {
        return {
          key: item.key,
          icon: item.icon,
          label: (
            <div className="d-flex align-items-center justify-content-between w-100 position-relative">
              <span className="fw-medium">{item.label}</span>
              {item.badge && !collapsed && (
                <div
                  className="position-absolute"
                  style={{
                    width: 6,
                    height: 6,
                    borderRadius: "50%",
                    backgroundColor: "#ef4444",
                    right: 4,
                    top: "50%",
                    transform: "translateY(-50%)",
                    boxShadow: "0 0 0 1px rgba(239, 68, 68, 0.2)",
                  }}
                />
              )}
            </div>
          ),
          children: convertToAntMenuItems(item.children),
        }
      }

      return {
        key: item.key,
        icon: item.icon,
        label: (
          <div className="d-flex align-items-center justify-content-between w-100 position-relative">
            <span className="fw-medium">{item.label}</span>
            {item.badge && !collapsed && (
              <div
                className="position-absolute"
                style={{
                  width: 6,
                  height: 6,
                  borderRadius: "50%",
                  backgroundColor: "#ef4444",
                  right: 4,
                  top: "50%",
                  transform: "translateY(-50%)",
                  boxShadow: "0 0 0 1px rgba(239, 68, 68, 0.2)",
                }}
              />
            )}
          </div>
        ),
      }
    })
  }

  const antMenuItems = useMemo(() => convertToAntMenuItems(menuItems), [menuItems, collapsed, theme])

  const sidebarWidth = collapsed ? 64 : 240

  return (
    <aside
      className="admin-sidebar d-flex flex-column"
      style={{
        gridRow: isMobile ? "1 / -1" : "2 / -2",
        gridColumn: isMobile ? "1 / -1" : "1 / 2",
        width: isMobile ? 240 : sidebarWidth,
        minWidth: isMobile ? 240 : sidebarWidth,
        background: theme === "dark" ? "#1a1a1a" : "#ffffff",
        borderRight: `1px solid ${theme === "dark" ? "#334155" : "#E2E8F0"}`,
        transition: "all 200ms ease",
        position: isMobile ? "fixed" : "sticky",
        top: isMobile ? 0 : 0,
        left: 0,
        height: isMobile ? "100vh" : "calc(100vh - 64px)",
        zIndex: isMobile ? 1001 : 10,
        boxShadow: isMobile
          ? theme === "dark"
            ? "0 4px 12px rgba(0, 0, 0, 0.25)"
            : "0 4px 12px rgba(0, 0, 0, 0.08)"
          : theme === "dark"
            ? "0 2px 8px rgba(0, 0, 0, 0.15)"
            : "0 2px 8px rgba(0, 0, 0, 0.06)",
        overflow: "hidden",
      }}
    >
      {/* Ultra-Compact Navigation Menu */}
      <div
        className="flex-grow-1"
        style={{
          overflowY: "auto",
          overflowX: "hidden",
          padding: "8px 0",
        }}
      >
        <Menu
          mode="inline"
          selectedKeys={layoutState.selectedKeys}
          openKeys={collapsed ? [] : layoutState.openKeys}
          onSelect={({ selectedKeys }) => onMenuSelect(selectedKeys)}
          onOpenChange={onOpenChange}
          onClick={handleMenuClick}
          items={antMenuItems}
          theme={theme}
          style={{
            border: "none",
            background: "transparent",
          }}
          inlineCollapsed={collapsed}
        />
      </div>

      {/* Ultra-Compact Collapse Toggle Button */}
      {!isMobile && (
        <div
          style={{
            padding: "8px",
            borderTop: `1px solid ${theme === "dark" ? "#334155" : "#E2E8F0"}`,
          }}
        >
          <Button
            type="text"
            icon={collapsed ? <ChevronRight size={14} /> : <ChevronLeft size={14} />}
            onClick={() => setCollapsed(!collapsed)}
            className="w-100 d-flex align-items-center justify-content-center fw-medium"
            style={{
              height: 32,
              color: theme === "dark" ? "#94A3B8" : "#64748B",
              borderRadius: 6,
              transition: "all 200ms ease",
              fontSize: 11,
            }}
            onFocus={(e) => {
              e.currentTarget.style.outline = "2px solid #3B82F6"
              e.currentTarget.style.outlineOffset = "2px"
            }}
            onBlur={(e) => {
              e.currentTarget.style.outline = "none"
            }}
          >
            {!collapsed && <span style={{ marginLeft: 4 }}>Collapse</span>}
          </Button>
        </div>
      )}
    </aside>
  )
}
