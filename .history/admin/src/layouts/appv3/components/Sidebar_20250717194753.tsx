"use client"

import type React from "react"
import { useEffect, useMemo } from "react"
import { Layout, Menu, Typography } from "antd"
import { useNavigate, useLocation } from "react-router"
import {
  DashboardOutlined,
  HomeOutlined,
  InboxOutlined,
  MessageOutlined,
  TeamOutlined,
  UserOutlined,
  <PERSON>Outlined,
  <PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  SettingOutlined,
  BellOutlined,
  FolderOutlined,
} from "@ant-design/icons"
import { Logo } from "@/components"
import { findSelectedMenuItem } from "../utils/menuAdapter"
import type { MenuItem, LayoutState } from "../types"

const { Sider } = Layout
const { Text } = Typography

interface SidebarProps {
  collapsed: boolean
  layoutState: LayoutState
  onMenuSelect: (keys: string[]) => void
  onOpenChange: (keys: string[]) => void
  theme?: "light" | "dark"
  isMobile?: boolean
}

// Enhanced menu items with proper icons and grouping
const getEnhancedMenuItems = (): MenuItem[] => [
  {
    key: "main",
    label: "Main",
    type: "group",
    children: [
      {
        key: "dashboard-home",
        icon: <DashboardOutlined />,
        label: "Dashboard",
        path: "/dashboard",
      },
      {
        key: "home",
        icon: <HomeOutlined />,
        label: "Home",
        path: "/home",
      },
    ],
  },
  {
    key: "communication",
    label: "Communication",
    type: "group",
    children: [
      {
        key: "inbox",
        icon: <InboxOutlined />,
        label: "Inbox",
        path: "/inbox",
        badge: 12,
      },
      {
        key: "slack",
        icon: <MessageOutlined />,
        label: "#Slack",
        path: "/slack",
        badge: 3,
      },
      {
        key: "notifications",
        icon: <BellOutlined />,
        label: "Notifications",
        path: "/notifications",
      },
    ],
  },
  {
    key: "management",
    label: "Management",
    type: "group",
    children: [
      {
        key: "clients",
        icon: <TeamOutlined />,
        label: "Clients",
        path: "/clients",
      },
      {
        key: "employees",
        icon: <UserOutlined />,
        label: "Employees",
        path: "/employees",
        children: [
          {
            key: "employees-list",
            label: "Employee List",
            path: "/employees/list",
          },
          {
            key: "employees-attendance",
            label: "Attendance",
            path: "/employees/attendance",
          },
          {
            key: "employees-payroll",
            label: "Payroll",
            path: "/employees/payroll",
          },
        ],
      },
      {
        key: "finance",
        icon: <DollarOutlined />,
        label: "Finance & Taxes",
        path: "/finance",
        children: [
          {
            key: "finance-overview",
            label: "Overview",
            path: "/finance/overview",
          },
          {
            key: "finance-invoices",
            label: "Invoices",
            path: "/finance/invoices",
          },
          {
            key: "finance-taxes",
            label: "Tax Reports",
            path: "/finance/taxes",
          },
        ],
      },
    ],
  },
  {
    key: "tools",
    label: "Tools & Reports",
    type: "group",
    children: [
      {
        key: "apps",
        icon: <MobileOutlined />,
        label: "App & Devices",
        path: "/apps",
      },
      {
        key: "reports",
        icon: <BarChartOutlined />,
        label: "Reports",
        path: "/reports",
        children: [
          {
            key: "reports-analytics",
            label: "Analytics",
            path: "/reports/analytics",
          },
          {
            key: "reports-performance",
            label: "Performance",
            path: "/reports/performance",
          },
        ],
      },
      {
        key: "projects",
        icon: <FolderOutlined />,
        label: "Projects",
        path: "/projects",
      },
    ],
  },
  {
    key: "system",
    label: "System",
    type: "group",
    children: [
      {
        key: "settings",
        icon: <SettingOutlined />,
        label: "Settings",
        path: "/settings",
        children: [
          {
            key: "settings-general",
            label: "General",
            path: "/settings/general",
          },
          {
            key: "settings-security",
            label: "Security",
            path: "/settings/security",
          },
          {
            key: "settings-integrations",
            label: "Integrations",
            path: "/settings/integrations",
          },
        ],
      },
    ],
  },
]

export const Sidebar: React.FC<SidebarProps> = ({
  collapsed,
  layoutState,
  onMenuSelect,
  onOpenChange,
  theme = "light",
  isMobile = false,
}) => {
  const navigate = useNavigate()
  const location = useLocation()

  // Get enhanced menu items
  const menuItems = useMemo(() => getEnhancedMenuItems(), [])

  const handleMenuClick = ({ key }: { key: string }) => {
    const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
      for (const item of items) {
        if (item.key === targetKey) return item
        if (item.children) {
          const found = findMenuItem(item.children, targetKey)
          if (found) return found
        }
      }
      return null
    }

    const menuItem = findMenuItem(menuItems, key)
    if (menuItem?.path) {
      navigate(menuItem.path)
    }
  }

  // Update selected keys based on location
  useEffect(() => {
    const { selectedKey, openKeys } = findSelectedMenuItem(menuItems, location.pathname)
    if (selectedKey) {
      onMenuSelect([selectedKey])
      onOpenChange(openKeys)
    }
  }, [location.pathname, menuItems, onMenuSelect, onOpenChange])

  // Convert MenuItem[] to Ant Design menu items format
  const convertToAntMenuItems = (items: MenuItem[]): any[] => {
    return items.map((item) => {
      if (item.type === "group") {
        return {
          type: "group",
          label: !collapsed ? item.label : null,
          children: item.children ? convertToAntMenuItems(item.children) : [],
        }
      }

      if (item.children && item.children.length > 0) {
        return {
          key: item.key,
          icon: item.icon,
          label: item.label,
          children: convertToAntMenuItems(item.children),
        }
      }

      return {
        key: item.key,
        icon: item.icon,
        label: (
          <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%" }}>
            <span>{item.label}</span>
            {item.badge && !collapsed && (
              <span
                style={{
                  backgroundColor: "#ff4d4f",
                  color: "#fff",
                  borderRadius: "10px",
                  padding: "0 6px",
                  fontSize: "12px",
                  fontWeight: 500,
                  minWidth: "18px",
                  height: "18px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                {item.badge}
              </span>
            )}
          </div>
        ),
      }
    })
  }

  const antMenuItems = useMemo(() => convertToAntMenuItems(menuItems), [menuItems, collapsed])

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={collapsed}
      width={isMobile ? 280 : 256}
      theme={theme}
      style={{
        overflow: "auto",
        height: "100vh",
        position: "fixed",
        left: 0,
        top: isMobile ? 0 : 64,
        bottom: 0,
        zIndex: isMobile ? 200 : 100,
        background: theme === "light" ? "#ffffff" : "#001529",
        border: "none",
        boxShadow: isMobile
          ? "4px 0 12px rgba(0, 0, 0, 0.15)"
          : theme === "light"
            ? "2px 0 8px rgba(0,0,0,0.06)"
            : "2px 0 8px rgba(0,0,0,0.25)",
        paddingTop: isMobile ? 64 : 0,
        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
      }}
    >
      {/* Logo Section */}
      <div
        className="d-flex align-items-center justify-content-center"
        style={{
          height: isMobile ? 64 : 64,
          padding: collapsed ? "0 8px" : "0 24px",
          background: theme === "light" ? "#fafafa" : "#002140",
          borderBottom: `1px solid ${theme === "light" ? "#f0f0f0" : "#303030"}`,
          transition: "all 0.3s ease",
        }}
      >
        {!collapsed ? (
          <div style={{ display: "flex", alignItems: "center", gap: 12 }}>
            <Logo color={theme === "light" ? "dark" : "white"} asLink href="/" imgSize={{ w: 32, h: 32 }} />
            <div>
              <Text
                strong
                style={{
                  color: theme === "light" ? "#000" : "#fff",
                  fontSize: 16,
                  lineHeight: 1,
                }}
              >
                VEA Admin
              </Text>
              <br />
              <Text
                style={{
                  color: theme === "light" ? "#666" : "#999",
                  fontSize: 12,
                  lineHeight: 1,
                }}
              >
                Dashboard
              </Text>
            </div>
          </div>
        ) : (
          <Logo color={theme === "light" ? "dark" : "white"} asLink href="/" imgSize={{ w: 28, h: 28 }} />
        )}
      </div>

      {/* Menu */}
      <Menu
        mode="inline"
        selectedKeys={layoutState.selectedKeys}
        openKeys={collapsed ? [] : layoutState.openKeys}
        onSelect={({ selectedKeys }) => onMenuSelect(selectedKeys)}
        onOpenChange={onOpenChange}
        onClick={handleMenuClick}
        items={antMenuItems}
        theme={theme}
        style={{
          border: "none",
          height: `calc(100% - ${isMobile ? 64 : 64}px)`,
          background: "transparent",
          padding: "16px 0",
        }}
        inlineCollapsed={collapsed}
      />
    </Sider>
  )
}
