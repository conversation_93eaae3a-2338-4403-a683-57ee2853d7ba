"use client"

import type React from "react"
import { useEffect, useMemo } from "react"
import { <PERSON>u, But<PERSON> } from "antd"
import { useNavigate, useLocation } from "react-router"
import {
  LayoutDashboard,
  Users,
  Building2,
  CreditCard,
  Settings,
  FileText,
  Smartphone,
  ChevronLeft,
  ChevronRight,
} from "lucide-react"
import { getOriginalMenuItems, findSelectedMenuItem } from "../utils/menuAdapter"
import { useThemeStore } from "@/store/themeStore"

interface SidebarProps {
  collapsed: boolean
  layoutState: any
  onMenuSelect: (keys: string[]) => void
  onOpenChange: (keys: string[]) => void
  theme?: "light" | "dark"
  isMobile?: boolean
}

export const Sidebar: React.FC<SidebarProps> = ({
  collapsed,
  onToggle,
  theme = 'light',
  isMobile = false
}) => {
  const nodeRef = useRef(null);
  const { pathname } = useLocation();
  const { mytheme } = useThemeStore();

  const items = useMemo(() => getOriginalMenuItems(pathname), [pathname]);

  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const [current, setCurrent] = useState<string>('');

  const rootSubmenuKeys = useMemo(
    () => items.filter(i => 'children' in i && Array.isArray((i as any).children)).map(i => i.key),
    [items]
  );

  const onOpenChange: MenuProps['onOpenChange'] = keys => {
    const latestOpenKey = keys.find(key => !openKeys.includes(key));
    if (latestOpenKey && rootSubmenuKeys.includes(latestOpenKey)) {
      setOpenKeys([latestOpenKey]);
    } else {
      setOpenKeys(keys);
    }
  };

  useEffect(() => {
    const { selectedKey, openKeys: foundOpenKeys } = findSelectedMenuItem(items, pathname);
    setCurrent(selectedKey);
    if (foundOpenKeys.length > 0) setOpenKeys(foundOpenKeys);
  }, [pathname, items]);

  return (
    <Sider
      ref={nodeRef}
      trigger={null}
      collapsible
      collapsed={collapsed}
      width={isMobile ? 280 : 240}
      theme={mytheme}
      style={{
        overflow: 'auto',
        height: '100vh',
        position: 'fixed',
        left: 0,
        top: isMobile ? 0 : 64,
        bottom: 0,
        zIndex: isMobile ? 200 : 100,
        background: mytheme === 'light' ? '#ffffff' : '#001529',
        border: 'none',
        boxShadow: isMobile ? '4px 0 12px rgba(0, 0, 0, 0.15)' : '2px 0 8px rgba(0,0,0,0.1)',
        paddingTop: isMobile ? 50 : 0,
        transition: 'all 0.2s ease',
      }}
    >
      {/* Logo Section */}
      <div
        className="d-flex align-items-center justify-content-center border-bottom"
        style={{
          height: isMobile ? 50 : 64,
          padding: '0 16px',
          background: mytheme === 'light' ? '#fafafa' : '#002140'
        }}
      >
        {!collapsed ? (
          <Logo
            color={mytheme === 'light' ? 'dark' : 'white'}
            asLink
            href="/"
            imgSize={{ w: 28, h: 28 }}
          />
        ) : (
          <Logo
            color={mytheme === 'light' ? 'dark' : 'white'}
            asLink
            href="/"
            imgSize={{ w: 24, h: 24 }}
          />
        )}
      </div>

      {/* Menu */}
      <Menu
        mode="inline"
        openKeys={collapsed ? [] : openKeys}
        onOpenChange={onOpenChange}
        selectedKeys={[current]}
        items={items as MenuProps['items']}
        theme={mytheme}
        style={{
          border: 'none',
          height: `calc(100% - ${isMobile ? 50 : 64}px)`,
          background: 'transparent'
        }}
        inlineCollapsed={collapsed}
      />
    </Sider>
  );
};
