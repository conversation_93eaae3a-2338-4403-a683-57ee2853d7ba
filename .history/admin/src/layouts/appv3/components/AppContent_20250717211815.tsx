"use client"

import type React from "react"
import { Outlet } from "react-router"
import { useMediaQuery } from "react-responsive"
import { useThemeStore } from "@/store/themeStore"
import { Card, Skeleton } from "antd"

interface AppContentProps {
  collapsed: boolean
}

export const AppContent: React.FC<AppContentProps> = ({ collapsed }) => {
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const { mytheme, mainLoading } = useThemeStore()

  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div style={{ padding: "24px 0" }}>
      <Skeleton active paragraph={{ rows: 8 }} />
      <div style={{ marginTop: 32 }}>
        <div className="row g-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="col-md-6 col-lg-3">
              <Card
                style={{
                  borderRadius: 8,
                  boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
                }}
              >
                <Skeleton active paragraph={{ rows: 2 }} />
              </Card>
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  return (
    <main
      className="admin-content"
      style={{
        gridRow: isMobile ? "2 / -2" : "2 / -2",
        gridColumn: isMobile ? "1 / -1" : "2 / -1",
        background: "#ffffff",
        padding: isMobile ? "24px 16px" : "32px 24px 32px 32px",
        overflow: "auto",
        minHeight: 0,
        transition: "all 200ms ease-in-out",
      }}
    >
      {/* Content Container with Subtle Card Shadow */}
      <div
        className="w-100 position-relative"
        style={{
          minHeight: "calc(100vh - 160px)",
          background: "#ffffff",
          borderRadius: 8,
          padding: isMobile ? "24px 20px" : "32px 28px",
          boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
          border: "1px solid #f3f4f6",
          overflow: "hidden",
          transition: "all 200ms ease-in-out",
        }}
      >
        {/* Content Headers with Proper Typography Scale */}
        <div
          style={{
            fontSize: "16px",
            fontWeight: 600,
            lineHeight: 1.4,
            color: "#111827",
            marginBottom: "24px",
          }}
        >
          {/* Route Content with Loading States */}
          {mainLoading ? <LoadingSkeleton /> : <Outlet />}
        </div>
      </div>
    </main>
  )
}
