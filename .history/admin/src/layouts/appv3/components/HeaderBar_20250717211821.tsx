"use client"

import type React from "react"
import { <PERSON>ton, Avatar, Dropdown, Switch, Tooltip, Input } from "antd"
import { Menu, Search, Clock, Settings, LogOut, Moon, Sun, User, ChevronDown, Command } from "lucide-react"
import { useMediaQuery } from "react-responsive"
import { useNavigate } from "react-router"
import { useThemeStore } from "@/store/themeStore"

const { Search: AntSearch } = Input

interface HeaderBarProps {
  collapsed: boolean
  onToggle: () => void
  isScrolled?: boolean
}

export const HeaderBar: React.FC<HeaderBarProps> = ({ collapsed, onToggle, isScrolled = false }) => {
  const navigate = useNavigate()
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const { mytheme, setTheme } = useThemeStore()

  const handleThemeToggle = (checked: boolean) => {
    setTheme(checked ? "dark" : "light")
  }

  const userMenuItems = [
    {
      key: "profile",
      icon: <User size={14} />,
      label: "Profile Settings",
      onClick: () => navigate("/profile"),
    },
    {
      key: "settings",
      icon: <Settings size={14} />,
      label: "Account Settings",
      onClick: () => navigate("/settings"),
    },
    {
      type: "divider" as const,
    },
    {
      key: "logout",
      icon: <LogOut size={14} />,
      label: "Sign Out",
      onClick: () => {
        console.log("Logout")
      },
    },
  ]

  return (
    <header
      className="admin-header d-flex align-items-center justify-content-between"
      style={{
        gridColumn: "1 / -1",
        height: 64,
        background: "#1e293b",
        borderBottom: "1px solid #334155",
        boxShadow: isScrolled
          ? "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
          : "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
        position: "sticky",
        top: 0,
        zIndex: 100,
        transition: "box-shadow 200ms ease-in-out",
        padding: "0 24px",
      }}
    >
      {/* Left Section - Menu Toggle & Logo */}
      <div className="d-flex align-items-center" style={{ gap: 16, minWidth: 0 }}>
        {/* Menu Toggle with Micro-interaction */}
        <Button
          type="text"
          icon={<Menu size={18} />}
          onClick={onToggle}
          className="d-flex align-items-center justify-content-center"
          aria-label="Toggle sidebar navigation"
          style={{
            width: 40,
            height: 40,
            color: "#ffffff",
            borderRadius: 8,
            transition: "all 200ms ease-in-out",
            flexShrink: 0,
            border: "1px solid transparent",
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = "#334155"
            e.currentTarget.style.borderColor = "#475569"
            e.currentTarget.style.transform = "scale(1.05)"
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = "transparent"
            e.currentTarget.style.borderColor = "transparent"
            e.currentTarget.style.transform = "scale(1)"
          }}
          onFocus={(e) => {
            e.currentTarget.style.outline = "2px solid #3B82F6"
            e.currentTarget.style.outlineOffset = "2px"
          }}
          onBlur={(e) => {
            e.currentTarget.style.outline = "none"
          }}
        />

        {/* Logo & Brand */}
        <div className="d-flex align-items-center" style={{ gap: 12, minWidth: 0 }}>
          <div
            className="d-flex align-items-center justify-content-center"
            style={{
              width: 36,
              height: 36,
              borderRadius: 8,
              background: "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)",
              boxShadow: "0 2px 4px rgba(59, 130, 246, 0.25)",
              flexShrink: 0,
            }}
          >
            <div
              className="d-flex align-items-center justify-content-center fw-bold"
              style={{
                width: 20,
                height: 20,
                background: "#ffffff",
                borderRadius: 4,
                fontSize: 11,
                color: "#3B82F6",
              }}
            >
              W
            </div>
          </div>
          {!isMobile && (
            <div style={{ minWidth: 0 }}>
              <div
                className="fw-semibold"
                style={{
                  fontSize: 16,
                  color: "#ffffff",
                  lineHeight: 1.2,
                  letterSpacing: "-0.01em",
                }}
              >
                WorkFlow Pro
              </div>
              <div
                style={{
                  fontSize: 12,
                  fontWeight: 500,
                  color: "#cbd5e1",
                  lineHeight: 1,
                }}
              >
                Enterprise Dashboard
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Center Section - Enhanced Search Bar */}
      {!isMobile && (
        <div className="flex-grow-1" style={{ maxWidth: 520, minWidth: 240 }}>
          <AntSearch
            placeholder="Search projects, tasks, team members..."
            allowClear
            size="large"
            style={{
              borderRadius: 8,
              width: "100%",
              boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
            }}
            prefix={
              <Search
                size={16}
                style={{
                  color: "#9ca3af",
                  marginRight: 8,
                }}
              />
            }
            suffix={
              <div
                className="d-flex align-items-center fw-medium"
                style={{
                  gap: 4,
                  color: "#6b7280",
                  fontSize: 11,
                  padding: "4px 8px",
                  background: "#f3f4f6",
                  borderRadius: 6,
                  border: "1px solid #e5e7eb",
                }}
              >
                <Command size={10} />
                <span>K</span>
              </div>
            }
          />
        </div>
      )}

      {/* Right Section - Actions */}
      <div className="d-flex align-items-center" style={{ gap: 16 }}>
        {/* Mobile Search */}
        {isMobile && (
          <Button
            type="text"
            icon={<Search size={18} />}
            className="d-flex align-items-center justify-content-center"
            aria-label="Open search"
            style={{
              width: 40,
              height: 40,
              color: "#ffffff",
              borderRadius: 8,
              flexShrink: 0,
              transition: "all 200ms ease-in-out",
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = "#334155"
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = "transparent"
            }}
          />
        )}

        {/* Theme Toggle */}
        <Tooltip title={`Switch to ${mytheme === "light" ? "dark" : "light"} mode`}>
          <Switch
            checked={mytheme === "dark"}
            onChange={handleThemeToggle}
            checkedChildren={<Moon size={12} />}
            unCheckedChildren={<Sun size={12} />}
            size="default"
            style={{ flexShrink: 0 }}
          />
        </Tooltip>

        {/* Time Clock with Pixel-Perfect Badge */}
        <Tooltip title="Time Tracking">
          <div style={{ position: "relative", display: "inline-block" }}>
            <Button
              type="text"
              icon={<Clock size={18} />}
              className="d-flex align-items-center justify-content-center"
              aria-label="Time tracking"
              style={{
                width: 40,
                height: 40,
                color: "#ffffff",
                borderRadius: 8,
                flexShrink: 0,
                transition: "all 200ms ease-in-out",
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = "#334155"
                e.currentTarget.style.transform = "scale(1.05)"
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = "transparent"
                e.currentTarget.style.transform = "scale(1)"
              }}
              onFocus={(e) => {
                e.currentTarget.style.outline = "2px solid #3B82F6"
                e.currentTarget.style.outlineOffset = "2px"
              }}
              onBlur={(e) => {
                e.currentTarget.style.outline = "none"
              }}
            />
            {/* Pixel-Perfect Badge Positioning */}
            <div
              style={{
                position: "absolute",
                top: "-2px",
                right: "-2px",
                width: "20px",
                height: "20px",
                backgroundColor: "#3b82f6",
                color: "#ffffff",
                fontSize: "11px",
                fontWeight: 600,
                borderRadius: "10px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                boxShadow: "0 2px 4px rgba(59, 130, 246, 0.3)",
                border: "2px solid #1e293b",
                lineHeight: 1,
              }}
            >
              8
            </div>
          </div>
        </Tooltip>

        {/* User Profile */}
        <Dropdown menu={{ items: userMenuItems }} placement="bottomRight" arrow>
          <div
            className="d-flex align-items-center"
            style={{
              gap: 12,
              padding: "6px 12px",
              borderRadius: 8,
              cursor: "pointer",
              transition: "all 200ms ease-in-out",
              background: "transparent",
              border: "1px solid #475569",
              flexShrink: 0,
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = "#334155"
              e.currentTarget.style.borderColor = "#64748b"
              e.currentTarget.style.transform = "translateY(-1px)"
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = "transparent"
              e.currentTarget.style.borderColor = "#475569"
              e.currentTarget.style.transform = "translateY(0)"
            }}
          >
            <Avatar
              size={32}
              style={{
                background: "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)",
                fontSize: 13,
                fontWeight: 600,
                border: "2px solid rgba(59, 130, 246, 0.2)",
              }}
            >
              JD
            </Avatar>
            {!isMobile && (
              <div style={{ textAlign: "left", lineHeight: 1.2, minWidth: 0 }}>
                <div
                  className="fw-semibold"
                  style={{
                    fontSize: 13,
                    color: "#ffffff",
                    whiteSpace: "nowrap",
                  }}
                >
                  John Doe
                </div>
                <div
                  style={{
                    fontSize: 11,
                    color: "#cbd5e1",
                    fontWeight: 500,
                    whiteSpace: "nowrap",
                  }}
                >
                  Administrator
                </div>
              </div>
            )}
            {!isMobile && (
              <ChevronDown
                size={14}
                style={{
                  color: "#94a3b8",
                  flexShrink: 0,
                }}
              />
            )}
          </div>
        </Dropdown>
      </div>
    </header>
  )
}
