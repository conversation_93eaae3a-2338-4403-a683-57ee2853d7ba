"use client"

import type React from "react"
import { But<PERSON>, theme, Badge, Avatar, Dropdown, Switch, Tooltip, Input } from "antd"
import { Menu, Search, Bell, Settings, LogOut, Moon, Sun, Globe, User, ChevronDown, Command } from "lucide-react"
import { useMediaQuery } from "react-responsive"
import { useNavigate } from "react-router"
import { useThemeStore } from "@/store/themeStore"

const { Search: AntSearch } = Input

interface HeaderBarProps {
  collapsed: boolean
  onToggle: () => void
}

export const HeaderBar: React.FC<HeaderBarProps> = ({ collapsed, onToggle }) => {
  const {
    token: { colorBgContainer, colorText, colorBorder, colorTextSecondary },
  } = theme.useToken()
  const navigate = useNavigate()
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const { mytheme, setTheme } = useThemeStore()

  const handleThemeToggle = (checked: boolean) => {
    setTheme(checked ? "dark" : "light")
  }

  const userMenuItems = [
    {
      key: "profile",
      icon: <User size={14} />,
      label: "Profile Settings",
      onClick: () => navigate("/profile"),
    },
    {
      key: "settings",
      icon: <Settings size={14} />,
      label: "Account Settings",
      onClick: () => navigate("/settings"),
    },
    {
      type: "divider" as const,
    },
    {
      key: "logout",
      icon: <LogOut size={14} />,
      label: "Sign Out",
      onClick: () => {
        console.log("Logout")
      },
    },
  ]

  return (
    <header
      className="admin-header d-flex align-items-center justify-content-between px-3"
      style={{
        gridColumn: "1 / -1",
        height: 56,
        background: mytheme === "dark" ? "linear-gradient(135deg, #1f2937 0%, #374151 100%)" : "#ffffff",
        borderBottom: `1px solid ${mytheme === "dark" ? "#374151" : "#e5e7eb"}`,
        boxShadow: mytheme === "dark" ? "0 1px 3px rgba(0, 0, 0, 0.3)" : "0 1px 3px rgba(0, 0, 0, 0.1)",
        position: "relative",
        zIndex: 100,
        transition: "all 300ms ease-in-out",
      }}
    >
      {/* Header Accent Overlay */}
      <div
        className="position-absolute top-0 start-0 w-100 h-100"
        style={{
          background: mytheme === "dark" ? "rgba(30, 64, 175, 0.1)" : "rgba(59, 130, 246, 0.05)",
          pointerEvents: "none",
        }}
      />

      {/* Left Section - Menu Toggle & Logo */}
      <div className="d-flex align-items-center" style={{ gap: 8, minWidth: 0, position: "relative", zIndex: 1 }}>
        {/* Menu Toggle */}
        <Button
          type="text"
          icon={<Menu size={16} />}
          onClick={onToggle}
          className="d-flex align-items-center justify-content-center"
          style={{
            width: 36,
            height: 36,
            color: mytheme === "dark" ? "#ffffff" : "#1f2937",
            borderRadius: 6,
            transition: "all 300ms ease-in-out",
            flexShrink: 0,
          }}
        />

        {/* Logo & Brand */}
        <div className="d-flex align-items-center" style={{ gap: 8, minWidth: 0 }}>
          <div
            className="d-flex align-items-center justify-content-center"
            style={{
              width: 32,
              height: 32,
              borderRadius: 6,
              background: "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)",
              boxShadow: mytheme === "dark" ? "0 1px 3px rgba(59, 130, 246, 0.4)" : "0 1px 3px rgba(59, 130, 246, 0.3)",
              flexShrink: 0,
            }}
          >
            <div
              className="d-flex align-items-center justify-content-center fw-bold"
              style={{
                width: 18,
                height: 18,
                background: "#ffffff",
                borderRadius: 3,
                fontSize: 10,
                color: "#3B82F6",
              }}
            >
              W
            </div>
          </div>
          {!isMobile && (
            <div style={{ minWidth: 0 }}>
              <div
                className="fw-semibold"
                style={{
                  fontSize: 14,
                  color: mytheme === "dark" ? "#ffffff" : "#1f2937",
                  lineHeight: 1.2,
                  letterSpacing: "-0.01em",
                }}
              >
                WorkFlow Pro
              </div>
              <div
                style={{
                  fontSize: 11,
                  fontWeight: 500,
                  color: mytheme === "dark" ? "#d1d5db" : "#6b7280",
                  lineHeight: 1,
                }}
              >
                Enterprise Dashboard
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Center Section - Search */}
      {!isMobile && (
        <div className="flex-grow-1" style={{ maxWidth: 400, minWidth: 200, position: "relative", zIndex: 1 }}>
          <AntSearch
            placeholder="Search..."
            allowClear
            size="middle"
            style={{
              borderRadius: 6,
              width: "100%",
            }}
            prefix={<Search size={14} style={{ color: mytheme === "dark" ? "#9ca3af" : "#6b7280" }} />}
            suffix={
              <div
                className="d-flex align-items-center fw-medium"
                style={{
                  gap: 2,
                  color: mytheme === "dark" ? "#9ca3af" : "#6b7280",
                  fontSize: 10,
                  padding: "2px 4px",
                  background: mytheme === "dark" ? "rgba(255, 255, 255, 0.08)" : "rgba(0, 0, 0, 0.04)",
                  borderRadius: 3,
                }}
              >
                <Command size={8} />
                <span>K</span>
              </div>
            }
          />
        </div>
      )}

      {/* Right Section - Actions */}
      <div className="d-flex align-items-center" style={{ gap: 8, position: "relative", zIndex: 1 }}>
        {/* Mobile Search */}
        {isMobile && (
          <Button
            type="text"
            icon={<Search size={16} />}
            className="d-flex align-items-center justify-content-center"
            style={{
              width: 36,
              height: 36,
              color: mytheme === "dark" ? "#ffffff" : "#1f2937",
              borderRadius: 6,
              flexShrink: 0,
            }}
          />
        )}

        {/* Theme Toggle */}
        <Tooltip title={`Switch to ${mytheme === "light" ? "dark" : "light"} mode`}>
          <Switch
            checked={mytheme === "dark"}
            onChange={handleThemeToggle}
            checkedChildren={<Moon size={10} />}
            unCheckedChildren={<Sun size={10} />}
            size="small"
            style={{ flexShrink: 0 }}
          />
        </Tooltip>

        {/* Language */}
        <Tooltip title="Language">
          <Button
            type="text"
            icon={<Globe size={16} />}
            className="d-flex align-items-center justify-content-center"
            style={{
              width: 36,
              height: 36,
              color: mytheme === "dark" ? "#ffffff" : "#1f2937",
              borderRadius: 6,
              flexShrink: 0,
            }}
          />
        </Tooltip>

        {/* Notifications */}
        <Tooltip title="Notifications">
          <Badge count={5} size="small" offset={[-2, 2]} color="#ef4444">
            <Button
              type="text"
              icon={<Bell size={16} />}
              className="d-flex align-items-center justify-content-center"
              style={{
                width: 36,
                height: 36,
                color: mytheme === "dark" ? "#ffffff" : "#1f2937",
                borderRadius: 6,
                flexShrink: 0,
              }}
            />
          </Badge>
        </Tooltip>

        {/* User Profile */}
        <Dropdown menu={{ items: userMenuItems }} placement="bottomRight" arrow>
          <div
            className="d-flex align-items-center"
            style={{
              gap: 8,
              padding: "4px 8px",
              borderRadius: 6,
              cursor: "pointer",
              transition: "all 300ms ease-in-out",
              background: "transparent",
              border: `1px solid ${mytheme === "dark" ? "#374151" : "#e5e7eb"}`,
              flexShrink: 0,
            }}
          >
            <Avatar
              size={28}
              style={{
                background: "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)",
                fontSize: 11,
                fontWeight: 600,
                border: "1px solid rgba(59, 130, 246, 0.2)",
              }}
            >
              JD
            </Avatar>
            {!isMobile && (
              <div style={{ textAlign: "left", lineHeight: 1.2, minWidth: 0 }}>
                <div
                  className="fw-semibold"
                  style={{
                    fontSize: 12,
                    color: mytheme === "dark" ? "#ffffff" : "#1f2937",
                    whiteSpace: "nowrap",
                  }}
                >
                  John Doe
                </div>
                <div
                  style={{
                    fontSize: 10,
                    color: mytheme === "dark" ? "#d1d5db" : "#6b7280",
                    fontWeight: 500,
                    whiteSpace: "nowrap",
                  }}
                >
                  Administrator
                </div>
              </div>
            )}
            {!isMobile && (
              <ChevronDown
                size={12}
                style={{
                  color: mytheme === "dark" ? "#9ca3af" : "#6b7280",
                  flexShrink: 0,
                }}
              />
            )}
          </div>
        </Dropdown>
      </div>
    </header>
  )
}
