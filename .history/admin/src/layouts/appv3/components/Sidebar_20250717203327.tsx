"use client"

import type React from "react"
import { useEffect, useMemo } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Button } from "antd"
import { useNavigate, useLocation } from "react-router"
import {
  LayoutDashboard,
  BarChart3,
  Users,
  Building2,
  CreditCard,
  Settings,
  Mail,
  MessageSquare,
  Bell,
  Smartphone,
  FileText,
  Calendar,
  Shield,
  Zap,
  TrendingUp,
  UserCheck,
  Briefcase,
  ChevronLeft,
  ChevronRight,
} from "lucide-react"
import { findSelectedMenuItem } from "../utils/menuAdapter"
import type { MenuItem, LayoutState } from "../types"
import { useThemeStore } from "@/store/themeStore"

const { Text } = Typography

interface SidebarProps {
  collapsed: boolean
  layoutState: LayoutState
  onMenuSelect: (keys: string[]) => void
  onOpenChange: (keys: string[]) => void
  theme?: "light" | "dark"
  isMobile?: boolean
}

// Premium organized menu structure
const getPremiumMenuItems = (): MenuItem[] => [
  {
    key: "overview",
    label: "Overview",
    type: "group",
    children: [
      {
        key: "dashboard",
        icon: <LayoutDashboard size={20} />,
        label: "Dashboard",
        path: "/dashboard",
      },
      {
        key: "analytics",
        icon: <BarChart3 size={20} />,
        label: "Analytics",
        path: "/analytics",
      },
    ],
  },
  {
    key: "workspace",
    label: "Workspace",
    type: "group",
    children: [
      {
        key: "inbox",
        icon: <Mail size={20} />,
        label: "Inbox",
        path: "/inbox",
        badge: 12,
      },
      {
        key: "messages",
        icon: <MessageSquare size={20} />,
        label: "Messages",
        path: "/messages",
        badge: 3,
      },
      {
        key: "notifications",
        icon: <Bell size={20} />,
        label: "Notifications",
        path: "/notifications",
        badge: 7,
      },
      {
        key: "calendar",
        icon: <Calendar size={20} />,
        label: "Calendar",
        path: "/calendar",
      },
    ],
  },
  {
    key: "management",
    label: "Management",
    type: "group",
    children: [
      {
        key: "employees",
        icon: <Users size={20} />,
        label: "Team Members",
        path: "/employees",
        children: [
          {
            key: "employees-directory",
            label: "Directory",
            path: "/employees/directory",
          },
          {
            key: "employees-attendance",
            label: "Attendance",
            path: "/employees/attendance",
          },
          {
            key: "employees-performance",
            label: "Performance",
            path: "/employees/performance",
          },
        ],
      },
      {
        key: "clients",
        icon: <Building2 size={20} />,
        label: "Clients",
        path: "/clients",
        children: [
          {
            key: "clients-active",
            label: "Active Clients",
            path: "/clients/active",
          },
          {
            key: "clients-prospects",
            label: "Prospects",
            path: "/clients/prospects",
          },
        ],
      },
      {
        key: "projects",
        icon: <Briefcase size={20} />,
        label: "Projects",
        path: "/projects",
      },
    ],
  },
  {
    key: "finance",
    label: "Finance",
    type: "group",
    children: [
      {
        key: "billing",
        icon: <CreditCard size={20} />,
        label: "Billing & Invoices",
        path: "/billing",
        children: [
          {
            key: "billing-invoices",
            label: "Invoices",
            path: "/billing/invoices",
          },
          {
            key: "billing-payments",
            label: "Payments",
            path: "/billing/payments",
          },
        ],
      },
      {
        key: "reports",
        icon: <TrendingUp size={20} />,
        label: "Financial Reports",
        path: "/reports",
      },
    ],
  },
  {
    key: "tools",
    label: "Tools & Apps",
    type: "group",
    children: [
      {
        key: "integrations",
        icon: <Zap size={20} />,
        label: "Integrations",
        path: "/integrations",
      },
      {
        key: "mobile",
        icon: <Smartphone size={20} />,
        label: "Mobile App",
        path: "/mobile",
      },
      {
        key: "documents",
        icon: <FileText size={20} />,
        label: "Documents",
        path: "/documents",
      },
    ],
  },
  {
    key: "admin",
    label: "Administration",
    type: "group",
    children: [
      {
        key: "settings",
        icon: <Settings size={20} />,
        label: "Settings",
        path: "/settings",
        children: [
          {
            key: "settings-general",
            label: "General",
            path: "/settings/general",
          },
          {
            key: "settings-security",
            label: "Security",
            path: "/settings/security",
          },
        ],
      },
      {
        key: "security",
        icon: <Shield size={20} />,
        label: "Security Center",
        path: "/security",
      },
      {
        key: "audit",
        icon: <UserCheck size={20} />,
        label: "Audit Logs",
        path: "/audit",
      },
    ],
  },
]

export const Sidebar: React.FC<SidebarProps> = ({
  collapsed,
  layoutState,
  onMenuSelect,
  onOpenChange,
  theme = "light",
  isMobile = false,
}) => {
  const navigate = useNavigate()
  const location = useLocation()
  const { setCollapsed } = useThemeStore()

  const menuItems = useMemo(() => getPremiumMenuItems(), [])

  const handleMenuClick = ({ key }: { key: string }) => {
    const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
      for (const item of items) {
        if (item.key === targetKey) return item
        if (item.children) {
          const found = findMenuItem(item.children, targetKey)
          if (found) return found
        }
      }
      return null
    }

    const menuItem = findMenuItem(menuItems, key)
    if (menuItem?.path) {
      navigate(menuItem.path)
    }
  }

  useEffect(() => {
    const { selectedKey, openKeys } = findSelectedMenuItem(menuItems, location.pathname)
    if (selectedKey) {
      onMenuSelect([selectedKey])
      onOpenChange(openKeys)
    }
  }, [location.pathname, menuItems, onMenuSelect, onOpenChange])

  const convertToAntMenuItems = (items: MenuItem[]): any[] => {
    return items.map((item) => {
      if (item.type === "group") {
        return {
          type: "group",
          label: !collapsed ? (
            <Text
              style={{
                fontSize: 12,
                fontWeight: 600,
                textTransform: "uppercase",
                letterSpacing: "0.5px",
                color: theme === "dark" ? "#9CA3AF" : "#6B7280",
                padding: "24px 0 8px 0",
                display: "block",
              }}
            >
              {item.label}
            </Text>
          ) : null,
          children: item.children ? convertToAntMenuItems(item.children) : [],
        }
      }

      if (item.children && item.children.length > 0) {
        return {
          key: item.key,
          icon: item.icon,
          label: (
            <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%" }}>
              <span style={{ fontWeight: 500 }}>{item.label}</span>
              {item.badge && !collapsed && (
                <div
                  style={{
                    width: 8,
                    height: 8,
                    borderRadius: "50%",
                    backgroundColor: "#EF4444",
                    position: "absolute",
                    right: 8,
                    top: "50%",
                    transform: "translateY(-50%)",
                    boxShadow: "0 0 0 2px rgba(239, 68, 68, 0.2)",
                  }}
                />
              )}
            </div>
          ),
          children: convertToAntMenuItems(item.children),
        }
      }

      return {
        key: item.key,
        icon: item.icon,
        label: (
          <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", width: "100%" }}>
            <span style={{ fontWeight: 500 }}>{item.label}</span>
            {item.badge && !collapsed && (
              <div
                style={{
                  width: 8,
                  height: 8,
                  borderRadius: "50%",
                  backgroundColor: "#EF4444",
                  position: "absolute",
                  right: 8,
                  top: "50%",
                  transform: "translateY(-50%)",
                  boxShadow: "0 0 0 2px rgba(239, 68, 68, 0.2)",
                }}
              />
            )}
          </div>
        ),
      }
    })
  }

  const antMenuItems = useMemo(() => convertToAntMenuItems(menuItems), [menuItems, collapsed, theme])

  const sidebarBg = theme === "dark" ? "#1F2937" : "#F9FAFB"
  const sidebarWidth = collapsed ? 80 : 280

  return (
    <aside
      className="admin-sidebar"
      style={{
        width: sidebarWidth,
        minWidth: sidebarWidth,
        height: "calc(100vh - 64px)",
        background: sidebarBg,
        borderRight: `1px solid ${theme === "dark" ? "#374151" : "#E5E7EB"}`,
        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
        position: isMobile ? "fixed" : "sticky",
        top: isMobile ? 64 : 0,
        left: 0,
        zIndex: isMobile ? 1001 : 100,
        boxShadow: isMobile
          ? "0 25px 50px -12px rgba(0, 0, 0, 0.25)"
          : theme === "light"
            ? "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"
            : "0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)",
        display: "flex",
        flexDirection: "column",
        overflow: "hidden",
      }}
    >
      {/* Navigation Menu */}
      <div
        style={{
          flex: 1,
          overflowY: "auto",
          overflowX: "hidden",
          padding: "24px 0",
        }}
      >
        <Menu
          mode="inline"
          selectedKeys={layoutState.selectedKeys}
          openKeys={collapsed ? [] : layoutState.openKeys}
          onSelect={({ selectedKeys }) => onMenuSelect(selectedKeys)}
          onOpenChange={onOpenChange}
          onClick={handleMenuClick}
          items={antMenuItems}
          theme={theme}
          style={{
            border: "none",
            background: "transparent",
          }}
          inlineCollapsed={collapsed}
        />
      </div>

      {/* Collapse Toggle Button */}
      {!isMobile && (
        <div
          style={{
            padding: "16px 12px",
            borderTop: `1px solid ${theme === "dark" ? "#374151" : "#E5E7EB"}`,
          }}
        >
          <Button
            type="text"
            icon={collapsed ? <ChevronRight size={16} /> : <ChevronLeft size={16} />}
            onClick={() => setCollapsed(!collapsed)}
            style={{
              width: "100%",
              height: 40,
              color: theme === "dark" ? "#9CA3AF" : "#6B7280",
              borderRadius: 8,
              transition: "all 0.15s ease",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          />
        </div>
      )}
    </aside>
  )
}
