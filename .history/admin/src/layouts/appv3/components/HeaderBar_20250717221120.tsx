"use client"

import type React from "react"
import { <PERSON>ton, Avatar, Dropdown, Switch, Tooltip, Input } from "antd"
import { Menu, Search, Clock, Settings, LogOut, Moon, Sun, User, ChevronDown, Command } from "lucide-react"
import { useMediaQuery } from "react-responsive"
import { useNavigate } from "react-router"
import { useThemeStore } from "@/store/themeStore"

const { Search: AntSearch } = Input

interface HeaderBarProps {
  collapsed: boolean
  onToggle: () => void
  isScrolled?: boolean
}

export const HeaderBar: React.FC<HeaderBarProps> = ({ collapsed, onToggle, isScrolled = false }) => {
  const navigate = useNavigate()
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const { mytheme, setTheme } = useThemeStore()

  const handleThemeToggle = (checked: boolean) => {
    setTheme(checked ? "dark" : "light")
  }

  const userMenuItems = [
    {
      key: "profile",
      icon: <User size={14} />,
      label: "Profile Settings",
      onClick: () => navigate("/profile"),
    },
    {
      key: "settings",
      icon: <Settings size={14} />,
      label: "Account Settings",
      onClick: () => navigate("/settings"),
    },
    {
      type: "divider" as const,
    },
    {
      key: "logout",
      icon: <LogOut size={14} />,
      label: "Sign Out",
      onClick: () => {
        console.log("Logout")
      },
    },
  ]

  return (
    <header
      className="admin-header d-flex align-items-center justify-content-between"
      style={{
        gridColumn: "1 / -1",
        height: 64,
        background: "#1e293b", // Keep header dark themed
        borderBottom: "1px solid #334155",
        boxShadow: isScrolled
          ? "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
          : "0 2px 4px rgba(0, 0, 0, 0.05)",
        position: "sticky",
        top: 0,
        zIndex: 100,
        transition: "box-shadow 300ms ease-in-out", // Smooth transitions
        padding: "0 24px",
      }}
    >
      {/* Left Section - Menu Toggle & Logo */}
      <div className="d-flex align-items-center" style={{ gap: 16, minWidth: 0 }}>
        {/* Menu Toggle */}
        <Button
          type="text"
          icon={<Menu size={18} />}
          onClick={onToggle}
          className="d-flex align-items-center justify-content-center"
          aria-label="Toggle sidebar navigation"
          style={{
            width: 40,
            height: 40,
            color: "#ffffff",
            borderRadius: 8,
            transition: "all 300ms ease-in-out", // Smooth transitions
            flexShrink: 0,
            border: "1px solid transparent",
          }}
          onFocus={(e) => {
            e.currentTarget.style.outline = "2px solid #3B82F6"
            e.currentTarget.style.outlineOffset = "2px"
          }}
          onBlur={(e) => {
            e.currentTarget.style.outline = "none"
          }}
        />

        {/* Logo & Brand */}
        <div className="d-flex align-items-center" style={{ gap: 12, minWidth: 0 }}>
          <div
            className="d-flex align-items-center justify-content-center"
            style={{
              width: 36,
              height: 36,
              borderRadius: 8,
              background: "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)", // Header blue accent
              boxShadow: "0 2px 4px rgba(59, 130, 246, 0.15)",
              flexShrink: 0,
            }}
          >
            <div
              className="d-flex align-items-center justify-content-center fw-bold"
              style={{
                width: 20,
                height: 20,
                background: "#ffffff",
                borderRadius: 4,
                fontSize: 11,
                color: "#3B82F6",
              }}
            >
              W
            </div>
          </div>
          {!isMobile && (
            <div style={{ minWidth: 0 }}>
              <div
                className="fw-semibold"
                style={{
                  fontSize: 16,
                  color: "#ffffff",
                  lineHeight: 1.2,
                  letterSpacing: "-0.01em",
                }}
              >
                WorkFlow Pro
              </div>
              <div
                style={{
                  fontSize: 12,
                  fontWeight: 500,
                  color: "#cbd5e1",
                  lineHeight: 1,
                }}
              >
                Enterprise Dashboard
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Center Section - Enhanced Search Bar */}
      {!isMobile && (
        <div className="flex-grow-1" style={{ maxWidth: 420, minWidth: 240 }}>
          <div style={{ position: "relative" }}>
            <div
              style={{
                position: "relative",
                background: "linear-gradient(135deg, #2d3748 0%, #374151 100%)", // Gradient background for depth
                borderRadius: 12,
                padding: "2px",
                boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)",
              }}
            >
              <AntSearch
                placeholder="Search projects, tasks, team members..."
                allowClear
                size="large"
                style={{
                  borderRadius: 10,
                  width: "100%",
                  height: 42, // Bigger search bar
                  background: "#2d3748", // Slightly lighter than header for depth
                  border: "1px solid #4a5568", // Subtle border
                  color: "#f7fafc",
                  fontSize: 14,
                  fontWeight: 500,
                  transition: "all 300ms ease-in-out",
                }}
                prefix={
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      width: 20,
                      height: 20,
                      borderRadius: 4,
                      background: "rgba(59, 130, 246, 0.1)",
                      marginRight: 12,
                    }}
                  >
                    <Search
                      size={14}
                      style={{
                        color: "#3B82F6",
                      }}
                    />
                  </div>
                }
                suffix={
                  <div
                    className="d-flex align-items-center fw-semibold"
                    style={{
                      gap: 4,
                      color: "#a0aec0",
                      fontSize: 11,
                      padding: "4px 8px",
                      background: "rgba(255, 255, 255, 0.08)",
                      borderRadius: 6,
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      backdropFilter: "blur(4px)",
                    }}
                  >
                    <Command size={10} />
                    <span>K</span>
                  </div>
                }
                onFocus={(e) => {
                  e.currentTarget.style.boxShadow = "0 0 0 3px rgba(59, 130, 246, 0.15)"
                  e.currentTarget.style.borderColor = "#3B82F6"
                }}
                onBlur={(e) => {
                  e.currentTarget.style.boxShadow = "none"
                  e.currentTarget.style.borderColor = "#4a5568"
                }}
              />
            </div>
          </div>
        </div>
      )}

      {/* Right Section */}
      <div className="d-flex align-items-center" style={{ gap: 16 }}>
        {/* Mobile Search */}
        {isMobile && (
          <Button
            type="text"
            icon={<Search size={18} />}
            className="d-flex align-items-center justify-content-center"
            aria-label="Open search"
            style={{
              width: 40,
              height: 40,
              color: "#ffffff",
              borderRadius: 8,
              flexShrink: 0,
              transition: "all 300ms ease-in-out",
            }}
          />
        )}

        {/* Theme Toggle with Header Blue Accent Colors */}
        <Tooltip title={`Switch to ${mytheme === "light" ? "dark" : "light"} mode`}>
          <Switch
            checked={mytheme === "dark"}
            onChange={handleThemeToggle}
            checkedChildren={<Moon size={12} />}
            unCheckedChildren={<Sun size={12} />}
            size="default"
            style={{
              flexShrink: 0,
              transition: "all 300ms ease-in-out",
            }}
          />
        </Tooltip>

        {/* Time Clock with Badge */}
        <Tooltip title="Time Tracking">
          <div style={{ position: "relative", display: "inline-block" }}>
            <Button
              type="text"
              icon={<Clock size={18} />}
              className="d-flex align-items-center justify-content-center"
              aria-label="Time tracking"
              style={{
                width: 40,
                height: 40,
                color: "#ffffff",
                borderRadius: 8,
                flexShrink: 0,
                transition: "all 300ms ease-in-out",
              }}
              onFocus={(e) => {
                e.currentTarget.style.outline = "2px solid #3B82F6"
                e.currentTarget.style.outlineOffset = "2px"
              }}
              onBlur={(e) => {
                e.currentTarget.style.outline = "none"
              }}
            />
            <div
              style={{
                position: "absolute",
                top: "-2px",
                right: "-2px",
                width: "20px",
                height: "20px",
                backgroundColor: "#3b82f6",
                color: "#ffffff",
                fontSize: "11px",
                fontWeight: 600,
                borderRadius: "10px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                boxShadow: "0 2px 4px rgba(59, 130, 246, 0.3)",
                border: "2px solid #1e293b",
                lineHeight: 1,
              }}
            >
              8
            </div>
          </div>
        </Tooltip>

        {/* User Profile - No Border, Seamless with Header */}
        <Dropdown menu={{ items: userMenuItems }} placement="bottomRight" arrow>
          <div
            className="d-flex align-items-center"
            style={{
              gap: 12,
              padding: "8px 16px",
              borderRadius: 10,
              cursor: "pointer",
              background: "rgba(255, 255, 255, 0.05)", // Subtle background
              border: "none", // No border - seamless with header
              flexShrink: 0,
              transition: "all 300ms ease-in-out",
              backdropFilter: "blur(8px)",
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = "rgba(255, 255, 255, 0.08)"
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = "rgba(255, 255, 255, 0.05)"
            }}
          >
            <Avatar
              size={32}
              style={{
                background: "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)",
                fontSize: 13,
                fontWeight: 600,
                border: "2px solid rgba(59, 130, 246, 0.2)",
                boxShadow: "0 2px 4px rgba(59, 130, 246, 0.15)",
              }}
            >
              JD
            </Avatar>
            {!isMobile && (
              <div style={{ textAlign: "left", lineHeight: 1.2, minWidth: 0 }}>
                <div
                  className="fw-semibold"
                  style={{
                    fontSize: 13,
                    color: "#ffffff",
                    whiteSpace: "nowrap",
                  }}
                >
                  John Doe
                </div>
                <div
                  style={{
                    fontSize: 11,
                    color: "#cbd5e1",
                    fontWeight: 500,
                    whiteSpace: "nowrap",
                  }}
                >
                  Administrator
                </div>
              </div>
            )}
            {!isMobile && (
              <ChevronDown
                size={14}
                style={{
                  color: "#94a3b8",
                  flexShrink: 0,
                  transition: "transform 300ms ease-in-out",
                }}
              />
            )}
          </div>
        </Dropdown>
      </div>
    </header>
  )
}
