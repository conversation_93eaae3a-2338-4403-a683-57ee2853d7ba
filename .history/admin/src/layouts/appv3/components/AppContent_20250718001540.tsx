"use client"

import type React from "react"
import { Outlet } from "react-router"
import { useMediaQuery } from "react-responsive"
import { useThemeStore } from "@/store/themeStore"
import { Breadcrumb, Button, Skeleton } from "antd"
import { HomeOutlined, RightOutlined, PlusOutlined, SettingOutlined } from "@ant-design/icons"


interface AppContentProps {
  collapsed: boolean
}

export const AppContent: React.FC<AppContentProps> = ({ collapsed }) => {
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const { mytheme, mainLoading } = useThemeStore()


  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div style={{ padding: "24px 0" }}>
      <Skeleton active paragraph={{ rows: 8 }} />
      <div style={{ marginTop: 32 }}>
        <div className="row g-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="col-md-6 col-lg-3">
              <div
                style={{
                  background: mytheme === "dark" ? "#374151" : "#ffffff", // White cards on container gray background
                  borderRadius: 8,
                  padding: 24, // Consistent 8px grid spacing
                  boxShadow: "0 2px 4px rgba(0, 0, 0, 0.05)",
                  border: `1px solid ${mytheme === "dark" ? "#4b5563" : "#f3f4f6"}`,
                  transition: "all 300ms ease-in-out", // Smooth transitions
                }}
              >
                <Skeleton active paragraph={{ rows: 2 }} />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )



  return (
    <main
      className="admin-content d-flex flex-column"
      style={{
        gridRow: "2 / -1",
        gridColumn: isMobile ? "1 / -1" : "2 / -1",
        background: mytheme === "dark" ? "#1A1A1A" : "#f8f9fa", // Very dark page background
        padding: isMobile ? "16px" : "24px 32px", // Consistent 8px grid spacing
        overflow: "auto",
        minHeight: 0,
        transition: "all 300ms ease-in-out", // Smooth transitions
      }}
    >
      {/* Optional Breadcrumb Navigation - Subtle, small text with "/" separators */}
      {/* <div style={{ marginBottom: 16 }}>
        <Breadcrumb
          separator={<RightOutlined style={{ fontSize: 12, color: mytheme === "dark" ? "#6b7280" : "#9ca3af" }} />}
          style={{
            fontSize: 12,
            color: mytheme === "dark" ? "#9ca3af" : "#6b7280",
          }}
          items={[
            {
              title: (
                <div className="d-flex align-items-center" style={{ gap: 4 }}>
                  <HomeOutlined style={{ fontSize: 12 }} />
                  <span>Dashboard</span>
                </div>
              ),
            },
            {
              title: "Analytics",
            },
            {
              title: "Overview",
            },
          ]}
        />
      </div> */}

      {/* Optional Page Header with title and right-aligned action buttons */}
      {/* <div
        className="d-flex align-items-center justify-content-between"
        style={{
          marginBottom: 32, // Consistent 8px grid spacing
          paddingBottom: 16,
          borderBottom: `1px solid ${mytheme === "dark" ? "#374151" : "#e5e7eb"}`,
        }}
      >
        <div>
          <h1
            style={{
              fontSize: 24,
              fontWeight: 600,
              color: mytheme === "dark" ? "#f9fafb" : "#111827", // Proper contrast ratios
              margin: 0,
              lineHeight: 1.2,
            }}
          >
            Dashboard Overview
          </h1>
          <p
            style={{
              fontSize: 14,
              color: mytheme === "dark" ? "#d1d5db" : "#6b7280", // Proper contrast ratios
              margin: "4px 0 0 0",
              lineHeight: 1.4,
            }}
          >
            Monitor your business performance and key metrics
          </p>
        </div>
        
        <div className="d-flex align-items-center" style={{ gap: 12 }}>
          <Button
            icon={<SettingOutlined style={{ fontSize: 16 }} />}
            style={{
              borderRadius: 8,
              height: 40,
              display: "flex",
              alignItems: "center",
              gap: 8, // Consistent 8px grid spacing
              transition: "all 300ms ease-in-out", // Smooth transitions
            }}
          >
            Settings
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined style={{ fontSize: 16 }} />}
            style={{
              borderRadius: 8,
              height: 40,
              display: "flex",
              alignItems: "center",
              gap: 8, // Consistent 8px grid spacing
              boxShadow: "0 2px 4px rgba(59, 130, 246, 0.15)",
              transition: "all 300ms ease-in-out", // Smooth transitions
            }}
          >
            Add Widget
          </Button>
        </div>
      </div> */}

      {/* Clean, Borderless Content Areas - Flex grow to push footer down */}
      <div
        className="flex-grow-1"
        style={{
          minHeight: "calc(100vh - 300px)",
          transition: "all 300ms ease-in-out", // Smooth transitions
        }}
      >
        {/* Route Content with Loading States */}
        {mainLoading ? <LoadingSkeleton /> : <Outlet />}
      </div>

      {/* Footer as part of main content flow (not fixed) */}
      <footer
        style={{
          marginTop: "auto", // Push to bottom of content
          padding: "24px 0", // Consistent 8px grid spacing
          borderTop: `1px solid ${mytheme === "dark" ? "#374151" : "#e5e7eb"}`,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          flexWrap: "wrap",
          gap: 16, // Consistent 8px grid spacing
          transition: "all 300ms ease-in-out", // Smooth transitions
        }}
      >
        {/* Left Section - Copyright */}
        <div
          style={{
            fontSize: 12, // Small font size
            color: mytheme === "dark" ? "#9ca3af" : "#6b7280", // Muted text color with proper contrast
            fontWeight: 500,
          }}
        >
          © 2024 WorkFlow Pro Enterprise. All rights reserved.
        </div>

        {/* Right Section - Links & Status with proper spacing */}
        <div className="d-flex align-items-center" style={{ gap: 24 }}>
          <a
            href="#"
            style={{
              fontSize: 12,
              color: mytheme === "dark" ? "#9ca3af" : "#6b7280", // Proper contrast ratios
              textDecoration: "none",
              fontWeight: 500,
              transition: "color 300ms ease-in-out", // Smooth transitions
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.color = mytheme === "dark" ? "#d1d5db" : "#374151"
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.color = mytheme === "dark" ? "#9ca3af" : "#6b7280"
            }}
          >
            Privacy Policy
          </a>
          <a
            href="#"
            style={{
              fontSize: 12,
              color: mytheme === "dark" ? "#9ca3af" : "#6b7280", // Proper contrast ratios
              textDecoration: "none",
              fontWeight: 500,
              transition: "color 300ms ease-in-out", // Smooth transitions
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.color = mytheme === "dark" ? "#d1d5db" : "#374151"
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.color = mytheme === "dark" ? "#9ca3af" : "#6b7280"
            }}
          >
            Support
          </a>
          <div
            className="d-flex align-items-center"
            style={{
              gap: 8, // Consistent 8px grid spacing
              fontSize: 12,
              color: mytheme === "dark" ? "#9ca3af" : "#6b7280", // Proper contrast ratios
              fontWeight: 500,
            }}
          >
            <div
              style={{
                width: 8, // Consistent 8px grid spacing
                height: 8,
                borderRadius: "50%",
                backgroundColor: "#10b981",
                boxShadow: "0 0 0 2px rgba(16, 185, 129, 0.2)",
              }}
            />
            <span>System Online</span>
          </div>
        </div>
      </footer>
    </main>
  )
}
