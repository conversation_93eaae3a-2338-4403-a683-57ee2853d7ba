import { useEffect, useRef, useState, useMemo } from 'react';
import { Layout, Menu } from 'antd';
import type { MenuProps, SiderProps } from 'antd';
import { useLocation } from 'react-router';
import { getOriginalMenuItems } from '../utils/menuAdapter';
import { useThemeStore } from '@/store/themeStore';

const { Sider } = Layout;

interface SidebarProps {
  collapsed: boolean;
  layoutState: any;
  onMenuSelect: (keys: string[]) => void;
  onOpenChange: (keys: string[]) => void;
  theme?: 'light' | 'dark';
  isMobile?: boolean;
}

// Create a mapping from original menu keys to lucide icons
const iconMap: Record<string, React.ReactNode> = {
  dashboard: <LayoutDashboard />,
  clients: <Building2 />,
  employees: <Users />,
  finance: <CreditCard />,
  reports: <FileText />,
  settings: <Settings />,
  devices: <Smartphone />,
}

export const Sidebar: React.FC<SidebarProps> = ({
  collapsed,
  layoutState,
  onMenuSelect,
  onOpenChange,
  theme = "light",
  isMobile = false,
}) => {
  const navigate = useNavigate()
  const location = useLocation()
  const { setCollapsed } = useThemeStore()

  // Get original menu items and convert them for the custom UI
  const originalItems = useMemo(() => getOriginalMenuItems(location.pathname), [location.pathname])

  const handleMenuClick = ({ key }: { key: string }) => {
    // Find the menu item and navigate to its path
    const findMenuItem = (items: any[], targetKey: string): any => {
      for (const item of items) {
        if (item.key === targetKey) return item
        if (item.children) {
          const found = findMenuItem(item.children, targetKey)
          if (found) return found
        }
      }
      return null
    }

    const menuItem = findMenuItem(originalItems, key)
    if (menuItem?.label?.props?.to) {
      navigate(menuItem.label.props.to)
      if (isMobile) {
        setCollapsed(true)
      }
    }
  }

  // Convert original menu items to custom format with lucide icons
  const convertToCustomMenuItems = (items: any[]): any[] => {
    return items.map((item) => {
      const baseItem = {
        key: item.key,
        icon: iconMap[item.key] || item.icon,
        label: typeof item.label === 'string' ? item.label :
          item.label?.props?.children || item.key,
      }

      if (item.children && Array.isArray(item.children)) {
        return {
          ...baseItem,
          children: convertToCustomMenuItems(item.children),
        }
      }

      return baseItem
    })
  }

  const customMenuItems = useMemo(() => convertToCustomMenuItems(originalItems), [originalItems])

  const sidebarWidth = collapsed ? 64 : 280

  return (
    <aside
      className="admin-sidebar d-flex flex-column"
      style={{
        gridRow: isMobile ? "1 / -1" : "2 / -1",
        gridColumn: isMobile ? "1 / -1" : "1 / 2",
        width: isMobile ? 280 : sidebarWidth,
        minWidth: isMobile ? 280 : sidebarWidth,
        background: theme === "dark" ? "#1f2937" : "#ffffff",
        borderRight: `1px solid ${theme === "dark" ? "#374151" : "#e5e7eb"}`,
        transition: "all 300ms ease-in-out",
        position: isMobile ? "fixed" : "sticky",
        top: isMobile ? 0 : 64,
        left: 0,
        height: isMobile ? "100vh" : "calc(100vh - 64px)",
        zIndex: isMobile ? 1001 : 10,
        boxShadow: isMobile
          ? "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
          : "0 2px 4px rgba(0, 0, 0, 0.05)",
        overflow: "hidden",
      }}
    >
      {/* Navigation Menu */}
      <div
        className="flex-grow-1"
        style={{
          overflowY: "auto",
          overflowX: "hidden",
          padding: "8px 0",
        }}
      >
        <Menu
          mode="inline"
          selectedKeys={layoutState.selectedKeys}
          openKeys={collapsed ? [] : layoutState.openKeys}
          onSelect={({ selectedKeys }) => onMenuSelect(selectedKeys)}
          onOpenChange={onOpenChange}
          onClick={handleMenuClick}
          items={customMenuItems}
          theme={theme}
          style={{
            border: "none",
            background: "transparent",
          }}
          inlineCollapsed={collapsed}
        />
      </div>

      {/* Collapse Toggle Button */}
      {!isMobile && (
        <div
          style={{
            padding: "12px",
            borderTop: `1px solid ${theme === "dark" ? "#374151" : "#e5e7eb"}`,
          }}
        >
          <Button
            type="text"
            icon={
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  transition: "transform 300ms ease-in-out",
                }}
              >
                {collapsed ? <ChevronRight size={14} /> : <ChevronLeft size={14} />}
              </div>
            }
            onClick={() => setCollapsed(!collapsed)}
            className="w-100 d-flex align-items-center justify-content-center fw-medium"
            aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
            style={{
              height: 32,
              color: theme === "dark" ? "#9ca3af" : "#6b7280",
              borderRadius: 6,
              transition: "all 300ms ease-in-out",
              fontSize: 12,
            }}
          >
            {!collapsed && <span style={{ marginLeft: 8 }}>Collapse</span>}
          </Button>
        </div>
      )}
    </aside>
  )
}
