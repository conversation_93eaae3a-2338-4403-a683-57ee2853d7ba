"use client"

import type React from "react"
import { useRef } from "react"
import { Layout, FloatButton } from "antd"
import { Outlet } from "react-router"
import { useMediaQuery } from "react-responsive"
import { useThemeStore } from "@/store/themeStore"
import { DashboardContent } from "./DashboardContent"
import FooterNav from "@/layouts/app/FooterNav"

const { Content } = Layout

interface AppContentProps {
  collapsed: boolean
}

export const AppContent: React.FC<AppContentProps> = ({ collapsed }) => {
  const floatBtnRef = useRef<HTMLDivElement>(null)
  const isMobile = useMediaQuery({ maxWidth: 769 })
  const isTablet = useMediaQuery({ maxWidth: 1024 })
  const { mytheme } = useThemeStore()

  return (
    <Layout
      style={{
        marginLeft: isMobile ? 0 : collapsed ? 80 : 256,
        marginTop: isMobile ? 56 : 64,
        minHeight: `calc(100vh - ${isMobile ? 56 : 64}px)`,
        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
        background: mytheme === "light" ? "#f0f2f5" : "#000000",
      }}
    >
      <Content
        style={{
          padding: isMobile ? "16px 12px" : isTablet ? "24px 20px" : "24px 24px",
          minHeight: 360,
          maxWidth: "100%",
          overflowX: "hidden",
          background: "transparent",
        }}
      >
        {/* Show dashboard content by default, or route-specific content */}
        <DashboardContent />
        <div style={{ display: "none" }}>
          <Outlet />
        </div>

        <div ref={floatBtnRef}>
          <FloatButton.BackTop
            style={{
              right: isMobile ? 16 : 24,
              bottom: isMobile ? 16 : 24,
              width: isMobile ? 40 : 48,
              height: isMobile ? 40 : 48,
              boxShadow: mytheme === "light" ? "0 4px 12px rgba(0, 0, 0, 0.15)" : "0 4px 12px rgba(0, 0, 0, 0.25)",
            }}
          />
        </div>
      </Content>

      <FooterNav
        style={{
          textAlign: "center",
          background: "transparent",
          padding: isMobile ? "16px 8px" : "24px 16px",
          color: mytheme === "light" ? "rgba(0, 0, 0, 0.45)" : "rgba(255, 255, 255, 0.45)",
          fontSize: "12px",
          borderTop: `1px solid ${mytheme === "light" ? "#f0f0f0" : "#303030"}`,
          marginTop: "24px",
        }}
      />
    </Layout>
  )
}
