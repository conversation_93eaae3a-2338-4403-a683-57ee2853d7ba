"use client"

import type React from "react"
import { useState } from "react"
import { Card, Tabs, Form, Input, Switch, Select, Button, Divider, Avatar, Upload, Row, Col, Badge } from "antd"
import {
  User,
  Shield,
  Bell,
  Key,
  Smartphone,
  Monitor,
  Camera,
  Save,
  RefreshCw,
  Eye,
  EyeOff,
  Trash2,
} from "lucide-react"
import { useThemeStore } from "@/store/themeStore"

const { TabPane } = Tabs
const { Option } = Select
const { TextArea } = Input
const { Password } = Input

interface SettingsPanelProps {
  className?: string
}

export const SettingsPanel: React.FC<SettingsPanelProps> = ({ className }) => {
  const { mytheme } = useThemeStore()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState("profile")
  const [showPassword, setShowPassword] = useState(false)

  const handleSave = async (values: any) => {
    setLoading(true)
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))
      console.log("Settings saved:", values)
    } finally {
      setLoading(false)
    }
  }

  const tabItems = [
    {
      key: "profile",
      label: (
        <div className="d-flex align-items-center" style={{ gap: 8 }}>
          <User size={16} />
          <span>Profile</span>
        </div>
      ),
      children: (
        <div style={{ padding: "24px 0" }}>
          <Form form={form} layout="vertical" onFinish={handleSave}>
            <Row gutter={24}>
              <Col span={24} md={8}>
                <Card
                  style={{
                    background: mytheme === "dark" ? "#374151" : "#ffffff",
                    border: `1px solid ${mytheme === "dark" ? "#4b5563" : "#e5e7eb"}`,
                    borderRadius: 12,
                    textAlign: "center",
                  }}
                >
                  <div style={{ marginBottom: 24 }}>
                    <Avatar
                      size={120}
                      style={{
                        background: "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)",
                        fontSize: 32,
                        fontWeight: 600,
                        border: "4px solid rgba(59, 130, 246, 0.2)",
                        boxShadow: "0 4px 12px rgba(59, 130, 246, 0.15)",
                      }}
                    >
                      JD
                    </Avatar>
                  </div>
                  <Upload name="avatar" listType="picture" showUploadList={false} beforeUpload={() => false}>
                    <Button
                      icon={<Camera size={16} />}
                      style={{
                        borderRadius: 8,
                        height: 40,
                        fontWeight: 500,
                      }}
                    >
                      Change Photo
                    </Button>
                  </Upload>
                </Card>
              </Col>
              <Col span={24} md={16}>
                <Card
                  title={
                    <div className="d-flex align-items-center" style={{ gap: 12 }}>
                      <div
                        style={{
                          width: 32,
                          height: 32,
                          borderRadius: 8,
                          background: "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <User size={16} style={{ color: "#ffffff" }} />
                      </div>
                      <span style={{ fontSize: 18, fontWeight: 600 }}>Personal Information</span>
                    </div>
                  }
                  style={{
                    background: mytheme === "dark" ? "#374151" : "#ffffff",
                    border: `1px solid ${mytheme === "dark" ? "#4b5563" : "#e5e7eb"}`,
                    borderRadius: 12,
                  }}
                >
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="First Name"
                        name="firstName"
                        initialValue="John"
                        rules={[{ required: true, message: "Please enter your first name" }]}
                      >
                        <Input placeholder="Enter first name" style={{ borderRadius: 8, height: 40 }} />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="Last Name"
                        name="lastName"
                        initialValue="Doe"
                        rules={[{ required: true, message: "Please enter your last name" }]}
                      >
                        <Input placeholder="Enter last name" style={{ borderRadius: 8, height: 40 }} />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="Email Address"
                        name="email"
                        initialValue="<EMAIL>"
                        rules={[
                          { required: true, message: "Please enter your email" },
                          { type: "email", message: "Please enter a valid email" },
                        ]}
                      >
                        <Input placeholder="Enter email address" style={{ borderRadius: 8, height: 40 }} />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="Phone Number" name="phone" initialValue="+****************">
                        <Input placeholder="Enter phone number" style={{ borderRadius: 8, height: 40 }} />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label="Job Title" name="jobTitle" initialValue="Administrator">
                        <Input placeholder="Enter job title" style={{ borderRadius: 8, height: 40 }} />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="Department" name="department" initialValue="IT">
                        <Select placeholder="Select department" style={{ borderRadius: 8 }}>
                          <Option value="IT">Information Technology</Option>
                          <Option value="HR">Human Resources</Option>
                          <Option value="Finance">Finance</Option>
                          <Option value="Marketing">Marketing</Option>
                          <Option value="Sales">Sales</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                  <Form.Item label="Bio" name="bio">
                    <TextArea rows={4} placeholder="Tell us about yourself..." style={{ borderRadius: 8 }} />
                  </Form.Item>
                </Card>
              </Col>
            </Row>
          </Form>
        </div>
      ),
    },
    {
      key: "security",
      label: (
        <div className="d-flex align-items-center" style={{ gap: 8 }}>
          <Shield size={16} />
          <span>Security</span>
        </div>
      ),
      children: (
        <div style={{ padding: "24px 0" }}>
          <Row gutter={24}>
            <Col span={24} lg={12}>
              <Card
                title={
                  <div className="d-flex align-items-center" style={{ gap: 12 }}>
                    <div
                      style={{
                        width: 32,
                        height: 32,
                        borderRadius: 8,
                        background: "linear-gradient(135deg, #EF4444 0%, #DC2626 100%)",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <Key size={16} style={{ color: "#ffffff" }} />
                    </div>
                    <span style={{ fontSize: 18, fontWeight: 600 }}>Password & Authentication</span>
                  </div>
                }
                style={{
                  background: mytheme === "dark" ? "#374151" : "#ffffff",
                  border: `1px solid ${mytheme === "dark" ? "#4b5563" : "#e5e7eb"}`,
                  borderRadius: 12,
                  marginBottom: 24,
                }}
              >
                <Form layout="vertical">
                  <Form.Item label="Current Password" name="currentPassword">
                    <Password
                      placeholder="Enter current password"
                      style={{ borderRadius: 8, height: 40 }}
                      iconRender={(visible) => (visible ? <EyeOff size={16} /> : <Eye size={16} />)}
                    />
                  </Form.Item>
                  <Form.Item label="New Password" name="newPassword">
                    <Password
                      placeholder="Enter new password"
                      style={{ borderRadius: 8, height: 40 }}
                      iconRender={(visible) => (visible ? <EyeOff size={16} /> : <Eye size={16} />)}
                    />
                  </Form.Item>
                  <Form.Item label="Confirm New Password" name="confirmPassword">
                    <Password
                      placeholder="Confirm new password"
                      style={{ borderRadius: 8, height: 40 }}
                      iconRender={(visible) => (visible ? <EyeOff size={16} /> : <Eye size={16} />)}
                    />
                  </Form.Item>
                  <Divider />
                  <div className="d-flex align-items-center justify-content-between" style={{ marginBottom: 16 }}>
                    <div>
                      <div style={{ fontWeight: 600, marginBottom: 4 }}>Two-Factor Authentication</div>
                      <div style={{ fontSize: 12, color: mytheme === "dark" ? "#9ca3af" : "#6b7280" }}>
                        Add an extra layer of security to your account
                      </div>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <Button
                    type="primary"
                    icon={<Save size={16} />}
                    style={{
                      borderRadius: 8,
                      height: 40,
                      fontWeight: 500,
                      boxShadow: "0 2px 4px rgba(59, 130, 246, 0.15)",
                    }}
                  >
                    Update Password
                  </Button>
                </Form>
              </Card>
            </Col>
            <Col span={24} lg={12}>
              <Card
                title={
                  <div className="d-flex align-items-center" style={{ gap: 12 }}>
                    <div
                      style={{
                        width: 32,
                        height: 32,
                        borderRadius: 8,
                        background: "linear-gradient(135deg, #10B981 0%, #059669 100%)",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <Monitor size={16} style={{ color: "#ffffff" }} />
                    </div>
                    <span style={{ fontSize: 18, fontWeight: 600 }}>Active Sessions</span>
                  </div>
                }
                style={{
                  background: mytheme === "dark" ? "#374151" : "#ffffff",
                  border: `1px solid ${mytheme === "dark" ? "#4b5563" : "#e5e7eb"}`,
                  borderRadius: 12,
                }}
              >
                <div style={{ marginBottom: 16 }}>
                  <div className="d-flex align-items-center justify-content-between" style={{ marginBottom: 12 }}>
                    <div className="d-flex align-items-center" style={{ gap: 12 }}>
                      <div
                        style={{
                          width: 40,
                          height: 40,
                          borderRadius: 8,
                          background: mytheme === "dark" ? "#4b5563" : "#f3f4f6",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <Monitor size={18} />
                      </div>
                      <div>
                        <div style={{ fontWeight: 600 }}>Current Session</div>
                        <div style={{ fontSize: 12, color: mytheme === "dark" ? "#9ca3af" : "#6b7280" }}>
                          Chrome on Windows • New York, NY
                        </div>
                      </div>
                    </div>
                    <Badge status="success" text="Active" />
                  </div>
                  <div className="d-flex align-items-center justify-content-between" style={{ marginBottom: 12 }}>
                    <div className="d-flex align-items-center" style={{ gap: 12 }}>
                      <div
                        style={{
                          width: 40,
                          height: 40,
                          borderRadius: 8,
                          background: mytheme === "dark" ? "#4b5563" : "#f3f4f6",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <Smartphone size={18} />
                      </div>
                      <div>
                        <div style={{ fontWeight: 600 }}>Mobile App</div>
                        <div style={{ fontSize: 12, color: mytheme === "dark" ? "#9ca3af" : "#6b7280" }}>
                          iPhone • 2 hours ago
                        </div>
                      </div>
                    </div>
                    <Button size="small" icon={<Trash2 size={14} />} style={{ borderRadius: 6 }}>
                      Revoke
                    </Button>
                  </div>
                </div>
                <Button
                  block
                  icon={<RefreshCw size={16} />}
                  style={{
                    borderRadius: 8,
                    height: 40,
                    fontWeight: 500,
                  }}
                >
                  Revoke All Other Sessions
                </Button>
              </Card>
            </Col>
          </Row>
        </div>
      ),
    },
    {
      key: "notifications",
      label: (
        <div className="d-flex align-items-center" style={{ gap: 8 }}>
          <Bell size={16} />
          <span>Notifications</span>
        </div>
      ),
      children: (
        <div style={{ padding: "24px 0" }}>
          <Row gutter={24}>
            <Col span={24} lg={12}>
              <Card
                title={
                  <div className="d-flex align-items-center" style={{ gap: 12 }}>
                    <div
                      style={{
                        width: 32,
                        height: 32,
                        borderRadius: 8,
                        background: "linear-gradient(135deg, #F59E0B 0%, #D97706 100%)",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <Bell size={16} style={{ color: "#ffffff" }} />
                    </div>
                    <span style={{ fontSize: 18, fontWeight: 600 }}>Email Notifications</span>
                  </div>
                }
                style={{
                  background: mytheme === "dark" ? "#374151" : "#ffffff",
                  border: `1px solid ${mytheme === "dark" ? "#4b5563" : "#e5e7eb"}`,
                  borderRadius: 12,
                  marginBottom: 24,
                }}
              >
                <div style={{ display: "flex", flexDirection: "column", gap: 16 }}>
                  <div className="d-flex align-items-center justify-content-between">
                    <div>
                      <div style={{ fontWeight: 600, marginBottom: 4 }}>Project Updates</div>
                      <div style={{ fontSize: 12, color: mytheme === "dark" ? "#9ca3af" : "#6b7280" }}>
                        Get notified when projects are updated
                      </div>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="d-flex align-items-center justify-content-between">
                    <div>
                      <div style={{ fontWeight: 600, marginBottom: 4 }}>Team Messages</div>
                      <div style={{ fontSize: 12, color: mytheme === "dark" ? "#9ca3af" : "#6b7280" }}>
                        Receive notifications for team messages
                      </div>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="d-flex align-items-center justify-content-between">
                    <div>
                      <div style={{ fontWeight: 600, marginBottom: 4 }}>Task Assignments</div>
                      <div style={{ fontSize: 12, color: mytheme === "dark" ? "#9ca3af" : "#6b7280" }}>
                        Get notified when tasks are assigned to you
                      </div>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="d-flex align-items-center justify-content-between">
                    <div>
                      <div style={{ fontWeight: 600, marginBottom: 4 }}>Weekly Reports</div>
                      <div style={{ fontSize: 12, color: mytheme === "dark" ? "#9ca3af" : "#6b7280" }}>
                        Receive weekly performance reports
                      </div>
                    </div>
                    <Switch />
                  </div>
                </div>
              </Card>
            </Col>
            <Col span={24} lg={12}>
              <Card
                title={
                  <div className="d-flex align-items-center" style={{ gap: 12 }}>
                    <div
                      style={{
                        width: 32,
                        height: 32,
                        borderRadius: 8,
                        background: "linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%)",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <Smartphone size={16} style={{ color: "#ffffff" }} />
                    </div>
                    <span style={{ fontSize: 18, fontWeight: 600 }}>Push Notifications</span>
                  </div>
                }
                style={{
                  background: mytheme === "dark" ? "#374151" : "#ffffff",
                  border: `1px solid ${mytheme === "dark" ? "#4b5563" : "#e5e7eb"}`,
                  borderRadius: 12,
                }}
              >
                <div style={{ display: "flex", flexDirection: "column", gap: 16 }}>
                  <div className="d-flex align-items-center justify-content-between">
                    <div>
                      <div style={{ fontWeight: 600, marginBottom: 4 }}>Desktop Notifications</div>
                      <div style={{ fontSize: 12, color: mytheme === "dark" ? "#9ca3af" : "#6b7280" }}>
                        Show desktop notifications for important updates
                      </div>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="d-flex align-items-center justify-content-between">
                    <div>
                      <div style={{ fontWeight: 600, marginBottom: 4 }}>Mobile Push</div>
                      <div style={{ fontSize: 12, color: mytheme === "dark" ? "#9ca3af" : "#6b7280" }}>
                        Receive push notifications on mobile devices
                      </div>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="d-flex align-items-center justify-content-between">
                    <div>
                      <div style={{ fontWeight: 600, marginBottom: 4 }}>Sound Alerts</div>
                      <div style={{ fontSize: 12, color: mytheme === "dark" ? "#9ca3af" : "#6b7280" }}>
                        Play sound for notifications
                      </div>
                    </div>
                    <Switch />
                  </div>
                  <Divider />
                  <Form.Item label="Notification Frequency" style={{ marginBottom: 0 }}>
                    <Select defaultValue="immediate" style={{ borderRadius: 8 }}>
                      <Option value="immediate">Immediate</Option>
                      <Option value="hourly">Hourly Digest</Option>
                      <Option value="daily">Daily Digest</Option>
                      <Option value="weekly">Weekly Digest</Option>
                    </Select>
                  </Form.Item>
                </div>
              </Card>
            </Col>
          </Row>
        </div>
      ),
    },
  ]

  return (
    <div className={className} style={{ padding: "24px 32px" }}>
      <div style={{ marginBottom: 32 }}>
        <h1
          style={{
            fontSize: 28,
            fontWeight: 700,
            color: mytheme === "dark" ? "#f9fafb" : "#111827",
            margin: 0,
            lineHeight: 1.2,
          }}
        >
          Settings
        </h1>
        <p
          style={{
            fontSize: 16,
            color: mytheme === "dark" ? "#d1d5db" : "#6b7280",
            margin: "8px 0 0 0",
            lineHeight: 1.4,
          }}
        >
          Manage your account settings and preferences
        </p>
      </div>

      <Card
        style={{
          background: mytheme === "dark" ? "#1f2937" : "#ffffff",
          border: `1px solid ${mytheme === "dark" ? "#374151" : "#e5e7eb"}`,
          borderRadius: 16,
          boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
        }}
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          style={{
            minHeight: 600,
          }}
          tabBarStyle={{
            borderBottom: `1px solid ${mytheme === "dark" ? "#374151" : "#e5e7eb"}`,
            marginBottom: 0,
          }}
        />
      </Card>

      {/* Save Button Bar */}
      <div
        style={{
          position: "sticky",
          bottom: 0,
          background: mytheme === "dark" ? "#111827" : "#f8f9fa",
          padding: "16px 0",
          marginTop: 24,
          borderTop: `1px solid ${mytheme === "dark" ? "#374151" : "#e5e7eb"}`,
          display: "flex",
          justifyContent: "flex-end",
          gap: 12,
        }}
      >
        <Button
          size="large"
          style={{
            borderRadius: 8,
            height: 48,
            paddingInline: 24,
            fontWeight: 500,
          }}
        >
          Cancel
        </Button>
        <Button
          type="primary"
          size="large"
          loading={loading}
          icon={<Save size={18} />}
          onClick={() => form.submit()}
          style={{
            borderRadius: 8,
            height: 48,
            paddingInline: 24,
            fontWeight: 500,
            boxShadow: "0 2px 4px rgba(59, 130, 246, 0.15)",
          }}
        >
          Save Changes
        </Button>
      </div>
    </div>
  )
}
