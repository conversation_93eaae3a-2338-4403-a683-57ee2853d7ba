import React, { useEffect, useMemo } from 'react';
import { Layout, Menu } from 'antd';
import { useNavigate, useLocation } from 'react-router';
import { useMediaQuery } from 'react-responsive';
import { Logo } from '@/components';
import { getAdaptedMenuItems, findSelectedMenuItem } from '../utils/menuAdapter';
import type { MenuItem, LayoutState } from '../types';

const { Sider } = Layout;

interface SidebarProps {
  collapsed: boolean;
  layoutState: LayoutState;
  onMenuSelect: (keys: string[]) => void;
  onOpenChange: (keys: string[]) => void;
  theme?: 'light' | 'dark';
  isMobile?: boolean;
}

export const Sidebar: React.FC<SidebarProps> = ({ 
  collapsed, 
  layoutState, 
  onMenuSelect, 
  onOpenChange,
  theme = 'light',
  isMobile = false
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const isTablet = useMediaQuery({ maxWidth: 1024 });

  // Get adapted menu items
  const menuItems = useMemo(() => getAdaptedMenuItems(location.pathname), [location.pathname]);

  const handleMenuClick = ({ key }: { key: string }) => {
    const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
      for (const item of items) {
        if (item.key === targetKey) return item;
        if (item.children) {
          const found = findMenuItem(item.children, targetKey);
          if (found) return found;
        }
      }
      return null;
    };

    const menuItem = findMenuItem(menuItems, key);
    if (menuItem?.path) {
      navigate(menuItem.path);
    }
  };

  // Update selected keys based on location
  useEffect(() => {
    const { selectedKey, openKeys } = findSelectedMenuItem(menuItems, location.pathname);
    if (selectedKey) {
      onMenuSelect([selectedKey]);
      onOpenChange(openKeys);
    }
  }, [location.pathname, menuItems, onMenuSelect, onOpenChange]);

  // Convert MenuItem[] to Ant Design menu items format
  const convertToAntMenuItems = (items: MenuItem[]): any[] => {
    return items.map(item => {
      if (item.children && item.children.length > 0) {
        return {
          key: item.key,
          icon: item.icon,
          label: item.label,
          children: convertToAntMenuItems(item.children)
        };
      }
      return {
        key: item.key,
        icon: item.icon,
        label: item.label
      };
    });
  };

  const antMenuItems = useMemo(() => convertToAntMenuItems(menuItems), [menuItems]);

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={collapsed}
      width={isMobile ? 280 : 240}
      theme={theme}
      style={{
        overflow: 'auto',
        height: '100vh',
        position: 'fixed',
        left: 0,
        top: isMobile ? 0 : 55,
        bottom: 0,
        zIndex: isMobile ? 200 : 100,
        background: theme === 'light' ? '#ffffff' : '#001529',
        border: 'none',
        boxShadow: isMobile ? '4px 0 12px rgba(0, 0, 0, 0.15)' : '2px 0 8px rgba(0,0,0,0.1)',
        paddingTop: isMobile ? 50 : 0,
        transition: 'all 0.2s ease',
      }}
    >
      {/* Logo Section */}
      <div 
        className="d-flex align-items-center justify-content-center border-bottom"
        style={{
          height: isMobile ? 50 : 55,
          padding: '0 16px',
          background: theme === 'light' ? '#fafafa' : '#002140'
        }}
      >
        {!collapsed ? (
          <Logo
            color={theme === 'light' ? 'dark' : 'white'}
            asLink
            href="/"
            imgSize={{ w: 28, h: 28 }}
          />
        ) : (
          <Logo
            color={theme === 'light' ? 'dark' : 'white'}
            asLink
            href="/"
            imgSize={{ w: 24, h: 24 }}
          />
        )}
      </div>

      {/* Menu */}
      <Menu
        mode="inline"
        selectedKeys={layoutState.selectedKeys}
        openKeys={collapsed ? [] : layoutState.openKeys}
        onSelect={({ selectedKeys }) => onMenuSelect(selectedKeys)}
        onOpenChange={onOpenChange}
        onClick={handleMenuClick}
        items={antMenuItems}
        theme={theme}
        style={{ 
          border: 'none',
          height: `calc(100% - ${isMobile ? 50 : 55}px)`,
          background: 'transparent'
        }}
        inlineCollapsed={collapsed}
      />
    </Sider>
  );
};
