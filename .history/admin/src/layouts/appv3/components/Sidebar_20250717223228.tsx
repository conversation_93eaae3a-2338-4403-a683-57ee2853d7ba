import React, { useEffect, useRef, useState, useMemo } from 'react';
import { Layout, Menu } from 'antd';
import type { MenuProps, SiderProps } from 'antd';
import { useLocation } from 'react-router';
import { useMediaQuery } from 'react-responsive';
import { Logo } from '@/components';
import { getOriginalMenuItems, findSelectedMenuItem } from '../utils/menuAdapter';
import { useThemeStore } from '@/store/themeStore';

const { Sider } = Layout;

interface SidebarProps {
  collapsed: boolean;
  onToggle: () => void;
  theme?: 'light' | 'dark';
  isMobile?: boolean;
}

// Compact menu structure with no section header padding
const getCompactMenuItems = (): MenuItem[] => [
  {
    key: "overview",
    label: "OVERVIEW",
    type: "group",
    children: [
      {
        key: "dashboard",
        icon: <LayoutDashboard />,
        label: "Dashboard",
        path: "/dashboard",
      },
      {
        key: "analytics",
        icon: <BarChart3 />,
        label: "Analytics",
        path: "/analytics",
      },
    ],
  },
  {
    key: "workspace",
    label: "WORKSPACE",
    type: "group",
    children: [
      {
        key: "inbox",
        icon: <Mail />,
        label: "Inbox",
        path: "/inbox",
        badge: 8,
      },
      {
        key: "messages",
        icon: <MessageSquare />,
        label: "Messages",
        path: "/messages",
        badge: 3,
      },
      {
        key: "notifications",
        icon: <Bell />,
        label: "Notifications",
        path: "/notifications",
        badge: 5,
      },
      {
        key: "calendar",
        icon: <Calendar />,
        label: "Calendar",
        path: "/calendar",
      },
    ],
  },
  {
    key: "management",
    label: "MANAGEMENT",
    type: "group",
    children: [
      {
        key: "employees",
        icon: <Users />,
        label: "Team Members",
        path: "/employees",
        children: [
          {
            key: "employees-directory",
            label: "Directory",
            path: "/employees/directory",
          },
          {
            key: "employees-attendance",
            label: "Attendance",
            path: "/employees/attendance",
          },
        ],
      },
      {
        key: "clients",
        icon: <Building2 />,
        label: "Clients",
        path: "/clients",
        children: [
          {
            key: "clients-active",
            label: "Active Clients",
            path: "/clients/active",
          },
          {
            key: "clients-prospects",
            label: "Prospects",
            path: "/clients/prospects",
          },
        ],
      },
      {
        key: "projects",
        icon: <Briefcase />,
        label: "Projects",
        path: "/projects",
      },
    ],
  },
  {
    key: "finance",
    label: "FINANCE",
    type: "group",
    children: [
      {
        key: "billing",
        icon: <CreditCard />,
        label: "Billing",
        path: "/billing",
        children: [
          {
            key: "billing-invoices",
            label: "Invoices",
            path: "/billing/invoices",
          },
          {
            key: "billing-payments",
            label: "Payments",
            path: "/billing/payments",
          },
        ],
      },
      {
        key: "reports",
        icon: <TrendingUp />,
        label: "Reports",
        path: "/reports",
      },
    ],
  },
  {
    key: "tools",
    label: "TOOLS",
    type: "group",
    children: [
      {
        key: "integrations",
        icon: <Zap />,
        label: "Integrations",
        path: "/integrations",
      },
      {
        key: "mobile",
        icon: <Smartphone />,
        label: "Mobile App",
        path: "/mobile",
      },
      {
        key: "documents",
        icon: <FileText />,
        label: "Documents",
        path: "/documents",
      },
    ],
  },
  {
    key: "admin",
    label: "ADMINISTRATION",
    type: "group",
    children: [
      {
        key: "settings",
        icon: <Settings />,
        label: "Settings",
        path: "/settings",
      },
      {
        key: "security",
        icon: <Shield />,
        label: "Security",
        path: "/security",
      },
      {
        key: "audit",
        icon: <UserCheck />,
        label: "Audit Logs",
        path: "/audit",
      },
    ],
  },
]

export const Sidebar: React.FC<SidebarProps> = ({
  collapsed,
  layoutState,
  onMenuSelect,
  onOpenChange,
  theme = "light",
  isMobile = false,
}) => {
  const navigate = useNavigate()
  const location = useLocation()
  const { setCollapsed } = useThemeStore()

  const menuItems = useMemo(() => getCompactMenuItems(), [])

  const handleMenuClick = ({ key }: { key: string }) => {
    const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
      for (const item of items) {
        if (item.key === targetKey) return item
        if (item.children) {
          const found = findMenuItem(item.children, targetKey)
          if (found) return found
        }
      }
      return null
    }

    const menuItem = findMenuItem(menuItems, key)
    if (menuItem?.path) {
      navigate(menuItem.path)
    }
  }

  useEffect(() => {
    const { selectedKey, openKeys } = findSelectedMenuItem(menuItems, location.pathname)
    if (selectedKey) {
      onMenuSelect([selectedKey])
      onOpenChange(openKeys)
    }
  }, [location.pathname, menuItems, onMenuSelect, onOpenChange])

  const convertToAntMenuItems = (items: MenuItem[]): any[] => {
    return items.map((item) => {
      if (item.type === "group") {
        return {
          type: "group",
          label: !collapsed ? (
            <div
              style={{
                fontSize: 11,
                fontWeight: 600,
                letterSpacing: "0.5px",
                color: theme === "dark" ? "#9ca3af" : "#6b7280",
                padding: "0",
                margin: "0 0 4px 0",
                textTransform: "uppercase",
              }}
            >
              {item.label}
            </div>
          ) : null,
          children: item.children ? convertToAntMenuItems(item.children) : [],
        }
      }

      if (item.children && item.children.length > 0) {
        return {
          key: item.key,
          icon: (
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                width: collapsed ? 20 : 18, // Smaller collapsed icons (18-20px)
                height: collapsed ? 20 : 18,
                transition: "all 300ms ease-in-out",
                margin: collapsed ? "0 12px 0 0" : "0", // Add right gap when collapsed
              }}
            >
              {item.icon && (
                <item.icon.type
                  size={collapsed ? 20 : 18} // Smaller collapsed icons (18-20px)
                  {...item.icon.props}
                />
              )}
            </div>
          ),
          label: (
            <div className="d-flex align-items-center justify-content-between w-100 position-relative">
              <span className="fw-medium" style={{ fontSize: 14 }}>
                {item.label}
              </span>
              {item.badge && !collapsed && (
                <div
                  className="position-absolute"
                  style={{
                    width: 6,
                    height: 6,
                    borderRadius: "50%",
                    backgroundColor: "#ef4444",
                    right: 8,
                    top: "50%",
                    transform: "translateY(-50%)",
                    boxShadow: "0 0 0 1px rgba(239, 68, 68, 0.2)",
                  }}
                />
              )}
            </div>
          ),
          children: convertToAntMenuItems(item.children),
          style: {
            border: "none",
          },
        }
      }

      return {
        key: item.key,
        icon: (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              width: collapsed ? 20 : 18, // Smaller collapsed icons (18-20px)
              height: collapsed ? 20 : 18,
              transition: "all 300ms ease-in-out",
              margin: collapsed ? "0 12px 0 0" : "0", // Add right gap when collapsed
            }}
          >
            {item.icon && (
              <item.icon.type
                size={collapsed ? 20 : 18} // Smaller collapsed icons (18-20px)
                {...item.icon.props}
              />
            )}
          </div>
        ),
        label: (
          <div className="d-flex align-items-center justify-content-between w-100 position-relative">
            <span className="fw-medium" style={{ fontSize: 14 }}>
              {item.label}
            </span>
            {item.badge && !collapsed && (
              <div
                className="position-absolute"
                style={{
                  width: 6,
                  height: 6,
                  borderRadius: "50%",
                  backgroundColor: "#ef4444",
                  right: 8,
                  top: "50%",
                  transform: "translateY(-50%)",
                  boxShadow: "0 0 0 1px rgba(239, 68, 68, 0.2)",
                }}
              />
            )}
          </div>
        ),
        style: {
          border: "none",
        },
      }
    })
  }

  const antMenuItems = useMemo(() => convertToAntMenuItems(menuItems), [menuItems, collapsed, theme])

  const sidebarWidth = collapsed ? 64 : 280

  return (
    <aside
      className="admin-sidebar d-flex flex-column"
      style={{
        gridRow: isMobile ? "1 / -1" : "2 / -1",
        gridColumn: isMobile ? "1 / -1" : "1 / 2",
        width: isMobile ? 280 : sidebarWidth,
        minWidth: isMobile ? 280 : sidebarWidth,
        background: theme === "dark" ? "#1f2937" : "#ffffff",
        borderRight: `1px solid ${theme === "dark" ? "#374151" : "#e5e7eb"}`,
        transition: "all 300ms ease-in-out",
        position: isMobile ? "fixed" : "sticky",
        top: isMobile ? 0 : 64,
        left: 0,
        height: isMobile ? "100vh" : "calc(100vh - 64px)",
        zIndex: isMobile ? 1001 : 10,
        boxShadow: isMobile
          ? "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
          : "0 2px 4px rgba(0, 0, 0, 0.05)",
        overflow: "hidden",
      }}
    >
      {/* Compact Navigation Menu */}
      <div
        className="flex-grow-1"
        style={{
          overflowY: "auto",
          overflowX: "hidden",
          padding: "8px 0",
        }}
      >
        <Menu
          mode="inline"
          selectedKeys={layoutState.selectedKeys}
          openKeys={collapsed ? [] : layoutState.openKeys}
          onSelect={({ selectedKeys }) => onMenuSelect(selectedKeys)}
          onOpenChange={onOpenChange}
          onClick={handleMenuClick}
          items={antMenuItems}
          theme={theme}
          style={{
            border: "none",
            background: "transparent",
          }}
          inlineCollapsed={collapsed}
        />
      </div>

      {/* Smooth Collapse Toggle Button */}
      {!isMobile && (
        <div
          style={{
            padding: "12px",
            borderTop: `1px solid ${theme === "dark" ? "#374151" : "#e5e7eb"}`,
          }}
        >
          <Button
            type="text"
            icon={
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  transition: "transform 300ms ease-in-out",
                }}
              >
                {collapsed ? <ChevronRight size={14} /> : <ChevronLeft size={14} />}
              </div>
            }
            onClick={() => setCollapsed(!collapsed)}
            className="w-100 d-flex align-items-center justify-content-center fw-medium"
            aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
            style={{
              height: 32,
              color: theme === "dark" ? "#9ca3af" : "#6b7280",
              borderRadius: 6,
              transition: "all 300ms ease-in-out",
              fontSize: 12,
              border: "none", // Remove border from toggle button
              background: "transparent",
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = theme === "dark" ? "#374151" : "#f8fafc"
              e.currentTarget.style.color = theme === "dark" ? "#f9fafb" : "#111827"
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = "transparent"
              e.currentTarget.style.color = theme === "dark" ? "#9ca3af" : "#6b7280"
            }}
            onFocus={(e) => {
              e.currentTarget.style.outline = "2px solid #3B82F6"
              e.currentTarget.style.outlineOffset = "2px"
            }}
            onBlur={(e) => {
              e.currentTarget.style.outline = "none"
            }}
          >
            {!collapsed && <span style={{ marginLeft: 8 }}>Collapse</span>}
          </Button>
        </div>
      )}
    </aside>
  )
}
