"use client"

import type React from "react"
import { Outlet } from "react-router"
import { useMediaQuery } from "react-responsive"
import { useThemeStore } from "@/store/themeStore"
import { theme } from "antd"
import FooterNav from "@/layouts/app/FooterNav"

interface AppContentProps {
  collapsed: boolean
}

export const AppContent: React.FC<AppContentProps> = ({ collapsed }) => {
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const { mytheme } = useThemeStore()
  const {
    token: { colorBgLayout, colorBorder },
  } = theme.useToken()

  const sidebarWidth = isMobile ? 0 : collapsed ? 80 : 280

  return (
    <main
      className="admin-content"
      style={{
        flex: 1,
        minHeight: "calc(100vh - 64px)",
        marginLeft: isMobile ? 0 : 0,
        width: isMobile ? "100%" : `calc(100% - ${sidebarWidth}px)`,
        background: colorBgLayout,
        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
        padding: isMobile ? "24px 16px" : "32px 32px",
        overflow: "auto",
      }}
    >
      {/* Content Container */}
      <div
        style={{
          width: "100%",
          minHeight: "calc(100vh - 200px)",
          background: mytheme === "dark" ? "#1F2937" : "#FFFFFF",
          borderRadius: 12,
          padding: isMobile ? "24px 20px" : "32px 32px",
          boxShadow:
            mytheme === "light"
              ? "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"
              : "0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)",
          border: `1px solid ${colorBorder}`,
          position: "relative",
          overflow: "hidden",
        }}
      >
        {/* Route Content */}
        <Outlet />
      </div>

      {/* Footer */}
      <FooterNav
        style={{
          textAlign: "center",
          background: "transparent",
          padding: isMobile ? "20px 16px" : "24px 32px",
          color: mytheme === "light" ? "rgba(107, 114, 128, 0.7)" : "rgba(156, 163, 175, 0.7)",
          fontSize: "13px",
          fontWeight: 500,
          borderTop: `1px solid ${colorBorder}`,
          marginTop: "32px",
        }}
      />
    </main>
  )
}
