import React from 'react';
import { Layout, Button, theme } from 'antd';
import { MenuUnfoldOutlined, MenuFoldOutlined } from '@ant-design/icons';
import { useMediaQuery } from 'react-responsive';
import { useNavigate } from 'react-router';
import { useThemeStore } from '@/store/themeStore';
import NavSearch from '@/layouts/app/NavSearch';
import User from '@/layouts/app/HeaderNav/User';

const { Header } = Layout;

interface HeaderBarProps {
  collapsed: boolean;
  onToggle: () => void;
}

export const HeaderBar: React.FC<HeaderBarProps> = ({ collapsed, onToggle }) => {
  const { token: { borderRadius } } = theme.useToken();
  const navigate = useNavigate();
  const isMobile = useMediaQuery({ maxWidth: 769 });
  const { mytheme } = useThemeStore();

  return (
    <Header
      style={{
        height: isMobile ? 50 : 55,
        padding: isMobile ? '0 8px' : '0 16px',
        background: mytheme === 'light' ? '#ffffff' : '#001529',
        position: 'fixed',
        top: 0,
        zIndex: 1000,
        width: '100%',
        left: isMobile ? 0 : (collapsed ? 80 : 240),
        width: isMobile ? '100%' : (collapsed ? 'calc(100% - 80px)' : 'calc(100% - 240px)'),
        transition: 'all 0.2s ease',
        borderBottom: `1px solid ${mytheme === 'light' ? '#f0f0f0' : '#303030'}`,
        boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
      }}
      className="d-flex align-items-center justify-content-between"
    >
      <div className="d-flex align-items-center" style={{ flex: 1, minWidth: 0 }}>
        <Button
          type="text"
          icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          onClick={onToggle}
          style={{
            fontSize: isMobile ? '14px' : '16px',
            width: isMobile ? 44 : 64,
            height: isMobile ? 44 : 48,
            color: mytheme === 'light' ? '#000' : '#fff',
            minWidth: isMobile ? 44 : 64,
            flexShrink: 0
          }}
        />

        {!isMobile && (
          <NavSearch isMobile={isMobile} />
        )}
      </div>

      <div className="d-flex align-items-center">
        {isMobile && (
          <div style={{ marginRight: 8 }}>
            <NavSearch isMobile={isMobile} />
          </div>
        )}
        
        <User 
          borderRadius={borderRadius} 
          navigate={navigate} 
          isMobile={isMobile} 
        />
      </div>
    </Header>
  );
};
