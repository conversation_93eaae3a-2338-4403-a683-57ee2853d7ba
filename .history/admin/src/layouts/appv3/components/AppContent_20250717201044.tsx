"use client"

import type React from "react"
import { useRef } from "react"
import { Layout, FloatButton, theme } from "antd"
import { Outlet } from "react-router"
import { useMediaQuery } from "react-responsive"
import { useThemeStore } from "@/store/themeStore"
import FooterNav from "@/layouts/app/FooterNav"

const { Content } = Layout

interface AppContentProps {
  collapsed: boolean
}

export const AppContent: React.FC<AppContentProps> = ({ collapsed }) => {
  const floatBtnRef = useRef<HTMLDivElement>(null)
  const isMobile = useMediaQuery({ maxWidth: 769 })
  const isTablet = useMediaQuery({ maxWidth: 1024 })
  const { mytheme } = useThemeStore()
  const {
    token: { colorBgLayout, colorBorder },
  } = theme.useToken()

  return (
    <Layout
      style={{
        marginLeft: isMobile ? 0 : collapsed ? 80 : 256,
        marginTop: isMobile ? 56 : 64,
        minHeight: `calc(100vh - ${isMobile ? 56 : 64}px)`,
        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
        background: colorBgLayout,
      }}
    >
      <Content
        style={{
          padding: isMobile ? "16px 12px" : isTablet ? "24px 20px" : "24px 24px",
          minHeight: 360,
          maxWidth: "100%",
          overflowX: "hidden",
          background: "transparent",
        }}
      >
        {/* Blank content area - just the outlet */}
        <div
          style={{
            width: "100%",
            minWidth: 0,
            minHeight: "calc(100vh - 200px)",
          }}
        >
          <Outlet />
        </div>

        <div ref={floatBtnRef}>
          <FloatButton.BackTop
            style={{
              right: isMobile ? 16 : 24,
              bottom: isMobile ? 16 : 24,
              width: isMobile ? 40 : 48,
              height: isMobile ? 40 : 48,
              boxShadow: mytheme === "light" ? "0 4px 12px rgba(0, 0, 0, 0.15)" : "0 4px 12px rgba(0, 0, 0, 0.25)",
            }}
          />
        </div>
      </Content>

      <FooterNav
        style={{
          textAlign: "center",
          background: "transparent",
          padding: isMobile ? "16px 8px" : "24px 16px",
          color: mytheme === "light" ? "rgba(0, 0, 0, 0.45)" : "rgba(255, 255, 255, 0.45)",
          fontSize: "12px",
          borderTop: `1px solid ${colorBorder}`,
          marginTop: "24px",
        }}
      />
    </Layout>
  )
}
