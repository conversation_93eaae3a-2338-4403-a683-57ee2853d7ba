"use client"

import type React from "react"
import { But<PERSON>, theme, Badge, Avatar, Dropdown, Switch, Tooltip, Input } from "antd"
import { Menu, Search, Bell, Settings, LogOut, Moon, Sun, User, ChevronDown, Command } from "lucide-react"
import { useMediaQuery } from "react-responsive"
import { useNavigate } from "react-router"
import { useThemeStore } from "@/store/themeStore"

const { Search: AntSearch } = Input

interface HeaderBarProps {
  collapsed: boolean
  onToggle: () => void
}

export const HeaderBar: React.FC<HeaderBarProps> = ({ collapsed, onToggle }) => {
  const {
    token: { colorBgContainer, colorText, colorBorder, colorTextSecondary },
  } = theme.useToken()
  const navigate = useNavigate()
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const isTablet = useMediaQuery({ maxWidth: 1024 })
  const { mytheme, setTheme } = useThemeStore()

  const handleThemeToggle = (checked: boolean) => {
    setTheme(checked ? "dark" : "light")
  }

  const userMenuItems = [
    {
      key: "profile",
      icon: <User size={14} />,
      label: "Profile Settings",
      onClick: () => navigate("/profile"),
    },
    {
      key: "settings",
      icon: <Settings size={14} />,
      label: "Account Settings",
      onClick: () => navigate("/settings"),
    },
    {
      type: "divider" as const,
    },
    {
      key: "logout",
      icon: <LogOut size={14} />,
      label: "Sign Out",
      onClick: () => {
        console.log("Logout")
      },
    },
  ]

  return (
    <header
      className="admin-header d-flex align-items-center justify-content-between"
      style={{
        gridColumn: "1 / -1",
        height: 64,
        background: mytheme === "dark" ? "#1e293b" : "#ffffff",
        borderBottom: `1px solid ${mytheme === "dark" ? "#334155" : "#E2E8F0"}`,
        boxShadow: mytheme === "dark" ? "0 2px 8px rgba(0, 0, 0, 0.15)" : "0 2px 8px rgba(0, 0, 0, 0.06)",
        position: "sticky",
        top: 0,
        zIndex: 100,
        transition: "all 200ms ease",
        padding: "0 24px",
      }}
    >
      {/* Left Section - Menu Toggle & Logo */}
      <div className="d-flex align-items-center" style={{ gap: 12, minWidth: 0 }}>
        {/* Menu Toggle */}
        <Button
          type="text"
          icon={<Menu size={18} />}
          onClick={onToggle}
          className="d-flex align-items-center justify-content-center"
          style={{
            width: 40,
            height: 40,
            color: mytheme === "dark" ? "#F8FAFC" : "#0F172A",
            borderRadius: 6,
            transition: "all 200ms ease",
            flexShrink: 0,
            border: "1px solid transparent",
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = mytheme === "dark" ? "#475569" : "#F1F5F9"
            e.currentTarget.style.borderColor = mytheme === "dark" ? "#64748B" : "#CBD5E1"
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = "transparent"
            e.currentTarget.style.borderColor = "transparent"
          }}
          onFocus={(e) => {
            e.currentTarget.style.outline = "2px solid #3B82F6"
            e.currentTarget.style.outlineOffset = "2px"
          }}
          onBlur={(e) => {
            e.currentTarget.style.outline = "none"
          }}
        />

        {/* Logo & Brand */}
        <div className="d-flex align-items-center" style={{ gap: 12, minWidth: 0 }}>
          <div
            className="d-flex align-items-center justify-content-center"
            style={{
              width: 36,
              height: 36,
              borderRadius: 6,
              background: "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)",
              boxShadow:
                mytheme === "dark" ? "0 2px 4px rgba(59, 130, 246, 0.25)" : "0 2px 4px rgba(59, 130, 246, 0.15)",
              flexShrink: 0,
            }}
          >
            <div
              className="d-flex align-items-center justify-content-center fw-bold"
              style={{
                width: 20,
                height: 20,
                background: "#ffffff",
                borderRadius: 4,
                fontSize: 11,
                color: "#3B82F6",
              }}
            >
              W
            </div>
          </div>
          {!isMobile && (
            <div style={{ minWidth: 0 }}>
              <div
                className="fw-semibold"
                style={{
                  fontSize: 16,
                  color: mytheme === "dark" ? "#F8FAFC" : "#0F172A",
                  lineHeight: 1.2,
                  letterSpacing: "-0.01em",
                }}
              >
                WorkFlow Pro
              </div>
              <div
                style={{
                  fontSize: 12,
                  fontWeight: 500,
                  color: mytheme === "dark" ? "#CBD5E1" : "#64748B",
                  lineHeight: 1,
                }}
              >
                Enterprise Dashboard
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Center Section - Enhanced Search */}
      {!isMobile && (
        <div className="flex-grow-1" style={{ maxWidth: 520, minWidth: 240 }}>
          <AntSearch
            placeholder="Search projects, tasks, team members, or documents..."
            allowClear
            size="large"
            style={{
              borderRadius: 6,
              width: "100%",
              boxShadow: mytheme === "dark" ? "0 2px 8px rgba(0, 0, 0, 0.15)" : "0 2px 8px rgba(0, 0, 0, 0.06)",
            }}
            prefix={
              <Search
                size={16}
                style={{
                  color: mytheme === "dark" ? "#94A3B8" : "#64748B",
                  marginRight: 8,
                }}
              />
            }
            suffix={
              <div
                className="d-flex align-items-center fw-medium"
                style={{
                  gap: 4,
                  color: mytheme === "dark" ? "#94A3B8" : "#64748B",
                  fontSize: 11,
                  padding: "4px 8px",
                  background: mytheme === "dark" ? "rgba(248, 250, 252, 0.08)" : "rgba(15, 23, 42, 0.06)",
                  borderRadius: 4,
                  border: `1px solid ${mytheme === "dark" ? "#475569" : "#CBD5E1"}`,
                }}
              >
                <Command size={10} />
                <span>K</span>
              </div>
            }
          />
        </div>
      )}

      {/* Right Section - Actions */}
      <div className="d-flex align-items-center" style={{ gap: 12 }}>
        {/* Mobile Search */}
        {isMobile && (
          <Button
            type="text"
            icon={<Search size={18} />}
            className="d-flex align-items-center justify-content-center"
            style={{
              width: 40,
              height: 40,
              color: mytheme === "dark" ? "#F8FAFC" : "#0F172A",
              borderRadius: 6,
              flexShrink: 0,
            }}
          />
        )}

        {/* Theme Toggle */}
        <Tooltip title={`Switch to ${mytheme === "light" ? "dark" : "light"} mode`}>
          <Switch
            checked={mytheme === "dark"}
            onChange={handleThemeToggle}
            checkedChildren={<Moon size={12} />}
            unCheckedChildren={<Sun size={12} />}
            size="default"
            style={{ flexShrink: 0 }}
          />
        </Tooltip>

        {/* Notifications with Blue Badge */}
        <Tooltip title="Notifications">
          <Badge
            count={12}
            size="small"
            offset={[-4, 4]}
            style={{
              backgroundColor: "#3b82f6",
              color: "#ffffff",
              fontSize: "11px",
              fontWeight: 600,
              minWidth: "20px",
              height: "20px",
              lineHeight: "20px",
              borderRadius: "10px",
              boxShadow: "0 2px 4px rgba(59, 130, 246, 0.3)",
              border: "2px solid #ffffff",
            }}
          >
            <Button
              type="text"
              icon={<Bell size={18} />}
              className="d-flex align-items-center justify-content-center"
              style={{
                width: 40,
                height: 40,
                color: mytheme === "dark" ? "#F8FAFC" : "#0F172A",
                borderRadius: 6,
                flexShrink: 0,
                transition: "all 200ms ease",
              }}
              onFocus={(e) => {
                e.currentTarget.style.outline = "2px solid #3B82F6"
                e.currentTarget.style.outlineOffset = "2px"
              }}
              onBlur={(e) => {
                e.currentTarget.style.outline = "none"
              }}
            />
          </Badge>
        </Tooltip>

        {/* User Profile */}
        <Dropdown menu={{ items: userMenuItems }} placement="bottomRight" arrow>
          <div
            className="d-flex align-items-center"
            style={{
              gap: 10,
              padding: "6px 12px",
              borderRadius: 6,
              cursor: "pointer",
              transition: "all 200ms ease",
              background: "transparent",
              border: `1px solid ${mytheme === "dark" ? "#475569" : "#CBD5E1"}`,
              flexShrink: 0,
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = mytheme === "dark" ? "#475569" : "#F1F5F9"
              e.currentTarget.style.borderColor = mytheme === "dark" ? "#64748B" : "#94A3B8"
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = "transparent"
              e.currentTarget.style.borderColor = mytheme === "dark" ? "#475569" : "#CBD5E1"
            }}
          >
            <Avatar
              size={32}
              style={{
                background: "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)",
                fontSize: 13,
                fontWeight: 600,
                border: "2px solid rgba(59, 130, 246, 0.2)",
              }}
            >
              JD
            </Avatar>
            {!isMobile && (
              <div style={{ textAlign: "left", lineHeight: 1.2, minWidth: 0 }}>
                <div
                  className="fw-semibold"
                  style={{
                    fontSize: 13,
                    color: mytheme === "dark" ? "#F8FAFC" : "#0F172A",
                    whiteSpace: "nowrap",
                  }}
                >
                  John Doe
                </div>
                <div
                  style={{
                    fontSize: 11,
                    color: mytheme === "dark" ? "#CBD5E1" : "#64748B",
                    fontWeight: 500,
                    whiteSpace: "nowrap",
                  }}
                >
                  Administrator
                </div>
              </div>
            )}
            {!isMobile && (
              <ChevronDown
                size={14}
                style={{
                  color: mytheme === "dark" ? "#94A3B8" : "#64748B",
                  flexShrink: 0,
                }}
              />
            )}
          </div>
        </Dropdown>
      </div>
    </header>
  )
}
