"use client"

import type React from "react"
import { useRef } from "react"
import { Layout, FloatButton, theme } from "antd"
import { Outlet } from "react-router"
import { useMediaQuery } from "react-responsive"
import { useThemeStore } from "@/store/themeStore"
import { ArrowUp } from "lucide-react"
import Footer<PERSON>av from "@/layouts/app/FooterNav"

const { Content } = Layout

interface AppContentProps {
  collapsed: boolean
}

export const AppContent: React.FC<AppContentProps> = ({ collapsed }) => {
  const floatBtnRef = useRef<HTMLDivElement>(null)
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const isTablet = useMediaQuery({ maxWidth: 1024 })
  const { mytheme } = useThemeStore()
  const {
    token: { colorBgLayout, colorBorder, colorBgContainer },
  } = theme.useToken()

  return (
    <Layout
      style={{
        marginLeft: isMobile ? 0 : collapsed ? 80 : 280,
        marginTop: isMobile ? 64 : 72,
        minHeight: `calc(100vh - ${isMobile ? 64 : 72}px)`,
        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
        background: colorBgLayout,
      }}
    >
      <Content
        style={{
          padding: isMobile ? "24px 16px" : isTablet ? "32px 24px" : "32px 32px",
          minHeight: 360,
          maxWidth: "100%",
          overflowX: "hidden",
          background: "transparent",
        }}
      >
        {/* Modern Content Container */}
        <div
          style={{
            width: "100%",
            minWidth: 0,
            minHeight: "calc(100vh - 200px)",
            background: colorBgContainer,
            borderRadius: 16,
            padding: isMobile ? "24px 20px" : "32px 32px",
            boxShadow:
              mytheme === "light"
                ? "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
                : "0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2)",
            border: `1px solid ${colorBorder}`,
            position: "relative",
            overflow: "hidden",
          }}
        >
          {/* Content goes here */}
          <Outlet />
        </div>

        {/* Modern Float Button */}
        <div ref={floatBtnRef}>
          <FloatButton
            icon={<ArrowUp size={20} />}
            type="primary"
            style={{
              right: isMobile ? 20 : 32,
              bottom: isMobile ? 20 : 32,
              width: 56,
              height: 56,
              borderRadius: 16,
              boxShadow: "0 8px 25px rgba(59, 130, 246, 0.3)",
              border: "none",
            }}
            onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
          />
        </div>
      </Content>

      {/* Modern Footer */}
      <FooterNav
        style={{
          textAlign: "center",
          background: "transparent",
          padding: isMobile ? "20px 16px" : "24px 32px",
          color: mytheme === "light" ? "rgba(107, 114, 128, 0.8)" : "rgba(156, 163, 175, 0.8)",
          fontSize: "13px",
          fontWeight: 500,
          borderTop: `1px solid ${colorBorder}`,
          marginTop: "32px",
        }}
      />
    </Layout>
  )
}
