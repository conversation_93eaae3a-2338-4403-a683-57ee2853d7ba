"use client"

import type React from "react"
import { But<PERSON>, theme, Badge, Avatar, Dropdown, Switch, Tooltip, Input } from "antd"
import { Menu, Search, Bell, Settings, LogOut, Moon, Sun, Globe, User, ChevronDown, Command } from "lucide-react"
import { useMediaQuery } from "react-responsive"
import { useNavigate } from "react-router"
import { useThemeStore } from "@/store/themeStore"

const { Search: AntSearch } = Input

interface HeaderBarProps {
  collapsed: boolean
  onToggle: () => void
}

export const HeaderBar: React.FC<HeaderBarProps> = ({ collapsed, onToggle }) => {
  const {
    token: { colorBgContainer, colorText, colorBorder, colorTextSecondary },
  } = theme.useToken()
  const navigate = useNavigate()
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const { mytheme, setTheme } = useThemeStore()

  const handleThemeToggle = (checked: boolean) => {
    setTheme(checked ? "dark" : "light")
  }

  const userMenuItems = [
    {
      key: "profile",
      icon: <User size={14} />,
      label: "Profile Settings",
      onClick: () => navigate("/profile"),
    },
    {
      key: "settings",
      icon: <Settings size={14} />,
      label: "Account Settings",
      onClick: () => navigate("/settings"),
    },
    {
      type: "divider" as const,
    },
    {
      key: "logout",
      icon: <LogOut size={14} />,
      label: "Sign Out",
      onClick: () => {
        console.log("Logout")
      },
    },
  ]

  return (
    <header
      className="admin-header"
      style={{
        gridColumn: "1 / -1",
        height: 56,
        padding: "0 16px",
        background: colorBgContainer,
        borderBottom: `1px solid ${colorBorder}`,
        boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        gap: 16,
        zIndex: 100,
      }}
    >
      {/* Left Section - Menu Toggle & Logo */}
      <div style={{ display: "flex", alignItems: "center", gap: 12, minWidth: 0 }}>
        {/* Menu Toggle */}
        <Button
          type="text"
          icon={<Menu size={16} />}
          onClick={onToggle}
          style={{
            width: 36,
            height: 36,
            color: colorText,
            borderRadius: 6,
            transition: "all 200ms ease",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            flexShrink: 0,
          }}
        />

        {/* Logo & Brand */}
        <div style={{ display: "flex", alignItems: "center", gap: 8, minWidth: 0 }}>
          <div
            style={{
              width: 32,
              height: 32,
              borderRadius: 6,
              background: "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              boxShadow: "0 1px 3px rgba(59, 130, 246, 0.3)",
              flexShrink: 0,
            }}
          >
            <div
              style={{
                width: 18,
                height: 18,
                background: "#FFFFFF",
                borderRadius: 3,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontWeight: 700,
                fontSize: 10,
                color: "#3B82F6",
              }}
            >
              W
            </div>
          </div>
          {!isMobile && (
            <div style={{ minWidth: 0 }}>
              <div
                style={{
                  fontSize: 14,
                  fontWeight: 600,
                  color: colorText,
                  lineHeight: 1.2,
                  letterSpacing: "-0.01em",
                }}
              >
                WorkFlow Pro
              </div>
              <div
                style={{
                  fontSize: 11,
                  fontWeight: 500,
                  color: colorTextSecondary,
                  lineHeight: 1,
                }}
              >
                Enterprise Dashboard
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Center Section - Search */}
      {!isMobile && (
        <div style={{ flex: 1, maxWidth: 400, minWidth: 200 }}>
          <AntSearch
            placeholder="Search..."
            allowClear
            size="middle"
            style={{
              borderRadius: 6,
              width: "100%",
            }}
            prefix={<Search size={14} style={{ color: colorTextSecondary }} />}
            suffix={
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: 2,
                  color: colorTextSecondary,
                  fontSize: 10,
                  fontWeight: 500,
                  padding: "2px 4px",
                  background: mytheme === "dark" ? "rgba(255, 255, 255, 0.08)" : "rgba(0, 0, 0, 0.04)",
                  borderRadius: 3,
                }}
              >
                <Command size={8} />
                <span>K</span>
              </div>
            }
          />
        </div>
      )}

      {/* Right Section - Actions */}
      <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
        {/* Mobile Search */}
        {isMobile && (
          <Button
            type="text"
            icon={<Search size={16} />}
            style={{
              width: 36,
              height: 36,
              color: colorText,
              borderRadius: 6,
              flexShrink: 0,
            }}
          />
        )}

        {/* Theme Toggle */}
        <Tooltip title={`Switch to ${mytheme === "light" ? "dark" : "light"} mode`}>
          <Switch
            checked={mytheme === "dark"}
            onChange={handleThemeToggle}
            checkedChildren={<Moon size={10} />}
            unCheckedChildren={<Sun size={10} />}
            size="small"
            style={{ flexShrink: 0 }}
          />
        </Tooltip>

        {/* Language */}
        <Tooltip title="Language">
          <Button
            type="text"
            icon={<Globe size={16} />}
            style={{
              width: 36,
              height: 36,
              color: colorText,
              borderRadius: 6,
              flexShrink: 0,
            }}
          />
        </Tooltip>

        {/* Notifications */}
        <Tooltip title="Notifications">
          <Badge count={5} size="small" offset={[-2, 2]}>
            <Button
              type="text"
              icon={<Bell size={16} />}
              style={{
                width: 36,
                height: 36,
                color: colorText,
                borderRadius: 6,
                flexShrink: 0,
              }}
            />
          </Badge>
        </Tooltip>

        {/* User Profile */}
        <Dropdown menu={{ items: userMenuItems }} placement="bottomRight" arrow>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: 8,
              padding: "4px 8px",
              borderRadius: 6,
              cursor: "pointer",
              transition: "all 200ms ease",
              background: "transparent",
              border: `1px solid ${colorBorder}`,
              flexShrink: 0,
            }}
          >
            <Avatar
              size={28}
              style={{
                background: "linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)",
                fontSize: 11,
                fontWeight: 600,
                border: "1px solid rgba(59, 130, 246, 0.2)",
              }}
            >
              JD
            </Avatar>
            {!isMobile && (
              <div style={{ textAlign: "left", lineHeight: 1.2, minWidth: 0 }}>
                <div
                  style={{
                    fontSize: 12,
                    fontWeight: 600,
                    color: colorText,
                    whiteSpace: "nowrap",
                  }}
                >
                  John Doe
                </div>
                <div
                  style={{
                    fontSize: 10,
                    color: colorTextSecondary,
                    fontWeight: 500,
                    whiteSpace: "nowrap",
                  }}
                >
                  Administrator
                </div>
              </div>
            )}
            {!isMobile && <ChevronDown size={12} style={{ color: colorTextSecondary, flexShrink: 0 }} />}
          </div>
        </Dropdown>
      </div>
    </header>
  )
}
