"use client"

import type React from "react"
import { useState, useRef, useEffect, useCallback } from "react"
import { useLocation } from "react-router"
import { ConfigProvider, theme } from "antd"
import { useMediaQuery } from "react-responsive"
import { useThemeStore } from "@/store/themeStore"
import { NProgress } from "@/components"
import ModalController from "@/layouts/app/ModalController"
import { Sidebar } from "./components/Sidebar"
import { HeaderBar } from "./components/HeaderBar"
import { AppContent } from "./components/AppContent"
import type { LayoutState } from "./types"

const { defaultAlgorithm, darkAlgorithm } = theme

export const AppV3Layout: React.FC = () => {
  const location = useLocation()
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const isTablet = useMediaQuery({ maxWidth: 1024 })
  const sidenavRef = useRef<HTMLDivElement>(null)
  const [isScrolled, setIsScrolled] = useState(false)

  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)

  const { mytheme, collapsed, setCollapsed, mainLoading } = useThemeStore()

  const [layoutState, setLayoutState] = useState<LayoutState>({
    collapsed: isTablet && !isMobile ? true : false,
    selectedKeys: ["dashboard"],
    openKeys: ["overview"],
  })

  const minSwipeDistance = 50

  const handleToggle = useCallback(() => {
    setCollapsed(!collapsed)
  }, [collapsed, setCollapsed])

  const handleMenuSelect = useCallback((selectedKeys: string[]) => {
    setLayoutState((prev) => ({ ...prev, selectedKeys }))
  }, [])

  const handleOpenChange = useCallback((openKeys: string[]) => {
    setLayoutState((prev) => ({ ...prev, openKeys }))
  }, [])

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return

    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > minSwipeDistance
    const isRightSwipe = distance < -minSwipeDistance

    if (isRightSwipe && collapsed && isMobile) {
      setCollapsed(false)
    }
    if (isLeftSwipe && !collapsed && isMobile) {
      setCollapsed(true)
    }
  }

  // Scroll detection for sticky header shadow
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobile && !collapsed && sidenavRef.current && !sidenavRef.current.contains(event.target as Node)) {
        setCollapsed(true)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [isMobile, collapsed, setCollapsed])

  // Auto-collapse on tablet, expand on desktop
  useEffect(() => {
    if (isTablet && !isMobile) {
      setCollapsed(true)
    } else if (!isTablet && !isMobile) {
      setCollapsed(false)
    }
  }, [isTablet, isMobile, setCollapsed])

  // CSS Custom Properties for Dark Theme Support
  useEffect(() => {
    const root = document.documentElement

    if (mytheme === "dark") {
      root.style.setProperty("--bg-primary", "#0f172a")
      root.style.setProperty("--bg-secondary", "#1e293b")
      root.style.setProperty("--bg-tertiary", "#334155")
      root.style.setProperty("--text-primary", "#f8fafc")
      root.style.setProperty("--text-secondary", "#cbd5e1")
      root.style.setProperty("--text-tertiary", "#94a3b8")
      root.style.setProperty("--border-primary", "#334155")
      root.style.setProperty("--border-secondary", "#475569")
      root.style.setProperty(
        "--shadow-primary",
        "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
      )
      root.style.setProperty("--shadow-secondary", "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)")
    } else {
      root.style.setProperty("--bg-primary", "#ffffff")
      root.style.setProperty("--bg-secondary", "#f8fafc")
      root.style.setProperty("--bg-tertiary", "#f1f5f9")
      root.style.setProperty("--text-primary", "#0f172a")
      root.style.setProperty("--text-secondary", "#475569")
      root.style.setProperty("--text-tertiary", "#64748b")
      root.style.setProperty("--border-primary", "#e2e8f0")
      root.style.setProperty("--border-secondary", "#cbd5e1")
      root.style.setProperty(
        "--shadow-primary",
        "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
      )
      root.style.setProperty("--shadow-secondary", "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)")
    }
  }, [mytheme])

  // Premium Enterprise Theme Configuration with 8px Grid System
  const premiumEnterpriseTheme = {
    algorithm: mytheme === "dark" ? darkAlgorithm : defaultAlgorithm,
    token: {
      // Professional Color System
      colorPrimary: "#3B82F6",
      colorSuccess: "#10B981",
      colorWarning: "#F59E0B",
      colorError: "#EF4444",
      colorInfo: "#3B82F6",

      // Typography Scale with Inter Font
      fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontSize: 14,
      fontSizeHeading1: 32,
      fontSizeHeading2: 24,
      fontSizeHeading3: 20,
      fontSizeHeading4: 16,
      fontSizeHeading5: 14,
      fontSizeSM: 12,
      fontSizeXS: 11,
      fontWeightStrong: 600,

      // 8px Grid Spacing System
      padding: 16,
      paddingXS: 8,
      paddingSM: 12,
      paddingLG: 24,
      paddingXL: 32,
      margin: 16,
      marginXS: 8,
      marginSM: 12,
      marginLG: 24,
      marginXL: 32,

      // Consistent 8px Border Radius
      borderRadius: 8,
      borderRadiusLG: 12,
      borderRadiusSM: 6,
      borderRadiusXS: 4,

      // Subtle Card Shadows
      boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
      boxShadowSecondary: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
      boxShadowTertiary: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",

      // Consistent White Background
      colorBgContainer: "#ffffff",
      colorBgElevated: "#ffffff",
      colorBgLayout: "#ffffff",
      colorBgBase: "#ffffff",
      colorBgSpotlight: "#ffffff",

      // Professional Border Colors
      colorBorder: "#e5e7eb",
      colorBorderSecondary: "#f3f4f6",

      // High Contrast Text Colors
      colorText: "#111827",
      colorTextSecondary: "#6b7280",
      colorTextTertiary: "#9ca3af",
      colorTextQuaternary: "#d1d5db",

      // Subtle Interactive States
      colorFill: "rgba(0, 0, 0, 0.02)",
      colorFillSecondary: "rgba(0, 0, 0, 0.01)",
      colorFillTertiary: "rgba(0, 0, 0, 0.005)",

      // Optimized Control Heights
      controlHeight: 40,
      controlHeightSM: 32,
      controlHeightLG: 48,

      // Professional Line Heights
      lineHeight: 1.5,
      lineHeightHeading1: 1.2,
      lineHeightHeading2: 1.3,
      lineHeightHeading3: 1.4,

      // Smooth Transitions
      motionDurationSlow: "0.3s",
      motionDurationMid: "0.2s",
      motionDurationFast: "0.1s",
      motionEaseInOut: "cubic-bezier(0.4, 0, 0.2, 1)",
      motionEaseOut: "cubic-bezier(0, 0, 0.2, 1)",
      motionEaseIn: "cubic-bezier(0.4, 0, 1, 1)",
    },
    components: {
      Layout: {
        siderBg: "#ffffff",
        headerBg: "#1e293b",
        bodyBg: "#ffffff",
        footerBg: "#f8fafc",
        headerHeight: 64,
        headerPadding: "0 24px",
        triggerBg: "#f3f4f6",
        triggerColor: "#374151",
      },
      Menu: {
        itemBg: "transparent",
        itemSelectedBg: "rgba(59, 130, 246, 0.1)",
        itemHoverBg: "#f8fafc",
        itemActiveBg: "rgba(59, 130, 246, 0.15)",
        itemSelectedColor: "#3B82F6",
        itemColor: "#374151",
        itemHoverColor: "#111827",
        subMenuItemBg: "transparent",
        groupTitleColor: "#6b7280",
        iconSize: 16,
        itemHeight: 40,
        collapsedIconSize: 16,
        itemMarginBlock: 2,
        itemMarginInline: 8,
        itemPaddingInline: 12,
        itemBorderRadius: 8,
        subMenuItemBorderRadius: 6,
        fontSize: 14,
        fontWeight: 500,
      },
      Button: {
        borderRadius: 8,
        controlHeight: 40,
        controlHeightSM: 32,
        controlHeightLG: 48,
        paddingInline: 16,
        paddingInlineSM: 12,
        paddingInlineLG: 24,
        fontWeight: 500,
        primaryShadow: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
        defaultShadow: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
      },
      Input: {
        borderRadius: 8,
        controlHeight: 40,
        controlHeightSM: 32,
        controlHeightLG: 48,
        paddingInline: 16,
        fontSize: 14,
        colorBgContainer: "#ffffff",
        colorBorder: "#d1d5db",
        colorBorderHover: "#9ca3af",
        activeShadow: "0 0 0 3px rgba(59, 130, 246, 0.1)",
        hoverBorderColor: "#9ca3af",
      },
      Card: {
        borderRadius: 8,
        boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
        headerBg: "transparent",
        bodyPadding: 24,
        paddingLG: 32,
      },
      Badge: {
        borderRadius: 8,
        fontWeight: 600,
        fontSize: 11,
        fontSizeSM: 10,
      },
      Avatar: {
        borderRadius: 8,
        fontSize: 14,
        fontWeight: 600,
      },
      Switch: {
        colorPrimary: "#3B82F6",
        colorPrimaryHover: "#2563EB",
        borderRadius: 12,
      },
      Dropdown: {
        colorBgElevated: "#ffffff",
        borderRadius: 8,
        boxShadowSecondary: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
        paddingBlock: 8,
      },
      Tooltip: {
        borderRadius: 8,
        fontSize: 12,
        colorBgSpotlight: "#374151",
      },
    },
  }

  return (
    <ConfigProvider theme={premiumEnterpriseTheme}>
      <NProgress isAnimating={mainLoading} key={location.key} />

      {/* 1600px Max-width Container with Auto Centering */}
      <div
        className="admin-layout-wrapper"
        style={{
          maxWidth: 1600,
          margin: "0 auto",
          minHeight: "100vh",
          background: "#ffffff",
        }}
      >
        <div
          className="admin-layout d-grid"
          style={{
            minHeight: "100vh",
            gridTemplateRows: "64px 1fr auto",
            gridTemplateColumns: isMobile ? "1fr" : collapsed ? "64px 1fr" : "280px 1fr",
            transition: "grid-template-columns 200ms ease-in-out",
          }}
        >
          {/* Mobile Overlay */}
          {isMobile && !collapsed && (
            <div
              className="position-fixed top-0 start-0 w-100 h-100"
              style={{
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                zIndex: 1000,
                backdropFilter: "blur(4px)",
              }}
              onClick={() => setCollapsed(true)}
              onTouchStart={onTouchStart}
              onTouchMove={onTouchMove}
              onTouchEnd={onTouchEnd}
            >
              <div
                ref={sidenavRef}
                onClick={(e) => e.stopPropagation()}
                style={{
                  transform: "translateX(0)",
                  transition: "transform 200ms ease-in-out",
                }}
              >
                <Sidebar
                  collapsed={false}
                  layoutState={layoutState}
                  onMenuSelect={handleMenuSelect}
                  onOpenChange={handleOpenChange}
                  theme={mytheme}
                  isMobile={true}
                />
              </div>
            </div>
          )}

          {/* Sticky Header with Scroll Shadow */}
          <HeaderBar collapsed={collapsed} onToggle={handleToggle} isScrolled={isScrolled} />

          {/* Desktop Sidebar */}
          {!isMobile && (
            <Sidebar
              collapsed={collapsed}
              layoutState={layoutState}
              onMenuSelect={handleMenuSelect}
              onOpenChange={handleOpenChange}
              theme={mytheme}
              isMobile={false}
            />
          )}

          {/* Main Content */}
          <AppContent collapsed={collapsed} />

          {/* Enhanced Footer */}
          <footer
            className="admin-footer text-center"
            style={{
              gridColumn: "1 / -1",
              background: "#f8fafc",
              borderTop: "1px solid #e5e7eb",
              padding: "24px",
              color: "#6b7280",
              fontSize: "13px",
              fontWeight: 500,
              transition: "all 200ms ease-in-out",
            }}
          >
            © 2024 WorkFlow Pro Enterprise. All rights reserved. | Version 3.2.1
          </footer>
        </div>
      </div>

      <ModalController />
    </ConfigProvider>
  )
}
