import { getMenuItems } from '@/constants/Menu';
import type { MenuItem } from '../types';

/**
 * Converts the existing menu items to the new appv3 format
 * This adapter ensures compatibility with the existing menu structure
 */
export const getAdaptedMenuItems = (pathname: string): MenuItem[] => {
  const originalItems = getMenuItems(pathname);
  
  const convertMenuItem = (item: any): MenuItem => {
    // Extract path from Link component props
    const extractPath = (label: any): string | undefined => {
      if (label?.props?.to) {
        return label.props.to;
      }
      return undefined;
    };

    // Extract text from Link component
    const extractLabel = (label: any): string => {
      if (typeof label === 'string') return label;
      if (label?.props?.children) return label.props.children;
      return item.key;
    };

    const converted: MenuItem = {
      key: item.key,
      icon: item.icon,
      label: extractLabel(item.label),
      path: extractPath(item.label),
    };

    if (item.children && Array.isArray(item.children)) {
      converted.children = item.children.map(convertMenuItem);
    }

    return converted;
  };

  return originalItems.map(convertMenuItem);
};

/**
 * Finds the selected menu item based on current pathname
 */
export const findSelectedMenuItem = (items: MenuItem[], pathname: string): { selectedKey: string; openKeys: string[] } => {
  const normalize = (p: string) => p.replace(/^\/+|\/+$/g, '');
  const currentPath = normalize(pathname);

  const findMatch = (items: MenuItem[], parentKey?: string): { selectedKey: string; openKeys: string[] } => {
    for (const item of items) {
      if (item.children) {
        const childResult = findMatch(item.children, item.key);
        if (childResult.selectedKey) {
          return {
            selectedKey: childResult.selectedKey,
            openKeys: parentKey ? [parentKey, ...childResult.openKeys] : [item.key, ...childResult.openKeys]
          };
        }
      } else if (item.path) {
        const itemPath = normalize(item.path);
        if (currentPath === itemPath) {
          return {
            selectedKey: item.key,
            openKeys: parentKey ? [parentKey] : []
          };
        }
      }
    }
    return { selectedKey: '', openKeys: [] };
  };

  return findMatch(items);
};
