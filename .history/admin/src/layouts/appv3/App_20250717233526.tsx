"use client"

import type React from "react"
import { useState, useRef, useEffect, useCallback } from "react"
import { useLocation } from "react-router"
import { ConfigProvider, theme } from "antd"
import { useMediaQuery } from "react-responsive"
import { useThemeStore } from "@/store/themeStore"
import { NProgress } from "@/components"
import ModalController from "@/layouts/app/ModalController"
import { Sidebar } from "./components/Sidebar"
import { HeaderBar } from "./components/HeaderBar"
import { AppContent } from "./components/AppContent"


const { defaultAlgorithm, darkAlgorithm } = theme

export const AppV3Layout: React.FC = () => {

  const location = useLocation()
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const isTablet = useMediaQuery({ minWidth: 769, maxWidth: 1024 })

  const sidenavRef = useRef<HTMLDivElement>(null)
  const [isScrolled, setIsScrolled] = useState(false)

  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)

  const { mytheme, collapsed, setCollapsed, mainLoading } = useThemeStore()

  const [layoutState, setLayoutState] = useState({
    collapsed: isMobile ? true : false,
    selectedKeys: ["dashboard"],
    openKeys: [] as string[],
  })

  const minSwipeDistance = 50

  const handleToggle = useCallback(() => {
    setCollapsed(!collapsed)
  }, [collapsed, setCollapsed])

  const handleMenuSelect = useCallback((selectedKeys: string[]) => {
    setLayoutState((prev) => ({ ...prev, selectedKeys }))
  }, [])

  const handleOpenChange = useCallback((openKeys: string[]) => {
    setLayoutState((prev) => ({ ...prev, openKeys }))
  }, [])

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return

    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > minSwipeDistance
    const isRightSwipe = distance < -minSwipeDistance

    if (isRightSwipe && collapsed && isMobile) {
      setCollapsed(false)
    }
    if (isLeftSwipe && !collapsed && isMobile) {
      setCollapsed(true)
    }
  }

  // Scroll detection for header shadow
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobile && !collapsed && sidenavRef.current && !sidenavRef.current.contains(event.target as Node)) {
        setCollapsed(true)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [isMobile, collapsed, setCollapsed])

  // Auto-collapse on mobile
  useEffect(() => {
    if (isMobile) {
      setCollapsed(true)
    }
  }, [isMobile, setCollapsed])

  // Set body background to match container
  useEffect(() => {
    document.body.style.background = mytheme === "dark" ? "#1A1A1A" : "#f8f9fa"
    document.documentElement.style.background = mytheme === "dark" ? "#1A1A1A" : "#f8f9fa"

    return () => {
      document.body.style.background = ""
      document.documentElement.style.background = ""
    }
  }, [mytheme])

  // Modern Professional Theme with 8px Grid System
  const modernProfessionalTheme = {
    algorithm: mytheme === "dark" ? darkAlgorithm : defaultAlgorithm,
    token: {
      // Professional Color System with Project Primary Color
      colorPrimary: "#2E6454", // Project primary color
      colorSuccess: "#10B981",
      colorWarning: "#F59E0B",
      colorError: "#EF4444",
      colorInfo: "#2E6454",

      // Typography with Inter Font
      fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontSize: 14,
      fontSizeHeading1: 32,
      fontSizeHeading2: 24,
      fontSizeHeading3: 20,
      fontSizeHeading4: 16,
      fontSizeHeading5: 14,
      fontSizeSM: 12,
      fontSizeXS: 11,
      fontWeightStrong: 600,

      // 8px Grid Spacing System
      padding: 16,
      paddingXS: 8,
      paddingSM: 12,
      paddingLG: 24,
      paddingXL: 32,
      margin: 16,
      marginXS: 8,
      marginSM: 12,
      marginLG: 24,
      marginXL: 32,

      // Consistent Border Radius
      borderRadius: 8,
      borderRadiusLG: 12,
      borderRadiusSM: 6,
      borderRadiusXS: 4,

      // Subtle Box Shadows for Depth
      boxShadow: "0 2px 4px rgba(0, 0, 0, 0.05)",
      boxShadowSecondary: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
      boxShadowTertiary: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",

      // Dark Background System - Matching header theme
      colorBgContainer: mytheme === "dark" ? "#303030" : "#ffffff",
      colorBgElevated: mytheme === "dark" ? "#404040" : "#ffffff",
      colorBgLayout: mytheme === "dark" ? "#1A1A1A" : "#f8f9fa", // Very dark page background
      colorBgBase: mytheme === "dark" ? "#1A1A1A" : "#f8f9fa",
      colorBgSpotlight: mytheme === "dark" ? "#303030" : "#ffffff",

      // Dark Border Colors
      colorBorder: mytheme === "dark" ? "#404040" : "#e5e7eb",
      colorBorderSecondary: mytheme === "dark" ? "#333" : "#f3f4f6",

      // High Contrast Text Colors (WCAG 2.1 AA)
      colorText: mytheme === "dark" ? "#f9fafb" : "#111827",
      colorTextSecondary: mytheme === "dark" ? "#d1d5db" : "#6b7280",
      colorTextTertiary: mytheme === "dark" ? "#9ca3af" : "#9ca3af",
      colorTextQuaternary: mytheme === "dark" ? "#6b7280" : "#d1d5db",

      // Subtle Interactive States
      colorFill: mytheme === "dark" ? "rgba(255, 255, 255, 0.05)" : "rgba(0, 0, 0, 0.02)",
      colorFillSecondary: mytheme === "dark" ? "rgba(255, 255, 255, 0.03)" : "rgba(0, 0, 0, 0.01)",
      colorFillTertiary: mytheme === "dark" ? "rgba(255, 255, 255, 0.02)" : "rgba(0, 0, 0, 0.005)",

      // Optimized Control Heights
      controlHeight: 40,
      controlHeightSM: 32,
      controlHeightLG: 48,

      // Professional Line Heights
      lineHeight: 1.5,
      lineHeightHeading1: 1.2,
      lineHeightHeading2: 1.3,
      lineHeightHeading3: 1.4,

      // Smooth Motion Design
      motionDurationSlow: "0.3s",
      motionDurationMid: "0.2s",
      motionDurationFast: "0.15s",
      motionEaseInOut: "cubic-bezier(0.4, 0, 0.2, 1)",
      motionEaseOut: "cubic-bezier(0, 0, 0.2, 1)",
      motionEaseIn: "cubic-bezier(0.4, 0, 1, 1)",
    },
    components: {
      Layout: {
        siderBg: mytheme === "dark" ? "#303030" : "#ffffff",
        headerBg: "#1A1A1A", // Very dark header
        bodyBg: mytheme === "dark" ? "#1A1A1A" : "#f8f9fa", // Very dark page background
        footerBg: mytheme === "dark" ? "#303030" : "#f8f9fa",
        headerHeight: 64,
        headerPadding: "0 24px",
        triggerBg: mytheme === "dark" ? "#404040" : "#f3f4f6",
        triggerColor: mytheme === "dark" ? "#f9fafb" : "#374151",
      },
      Menu: {
        itemBg: "transparent",
        itemSelectedBg: mytheme === "dark"
          ? "rgba(46, 100, 84, 0.15)"
          : "rgba(46, 100, 84, 0.1)",
        itemHoverBg: mytheme === "dark"
          ? "rgba(46, 100, 84, 0.08)"
          : "rgba(46, 100, 84, 0.05)",
        itemActiveBg: mytheme === "dark" ? "rgba(46, 100, 84, 0.2)" : "rgba(46, 100, 84, 0.12)",
        itemSelectedColor: "#2E6454",
        itemColor: mytheme === "dark" ? "#e5e7eb" : "#374151", // Lighter but still readable in dark mode
        itemHoverColor: mytheme === "dark" ? "#f9fafb" : "#111827", // Brightest on hover
        subMenuItemBg: mytheme === "dark" ? "#1A1A1A" : "transparent", // Very dark submenu background
        subMenuBg: mytheme === "dark" ? "#1A1A1A" : "#ffffff", // Submenu container background
        groupTitleColor: mytheme === "dark" ? "#9ca3af" : "#6b7280", // Subtle group titles
        iconSize: 20, // Larger icons for better visibility
        itemHeight: 36, // Balanced height - not too tall, good for touch
        collapsedIconSize: 24, // Better proportion when collapsed
        itemMarginBlock: 2, // More space between items
        itemMarginInline: 12, // Better horizontal spacing
        itemPaddingInline: 14, // Balanced padding
        itemBorderRadius: 8, // More rounded corners
        subMenuItemBorderRadius: 6,
        fontSize: 14,
        fontWeight: 500,
        // Remove all borders from selected/clicked sidebar items
        itemSelectedBorder: "none",
        itemActiveBorder: "none",
        itemHoverBorder: "none",
      },
      Button: {
        borderRadius: 8,
        controlHeight: 40,
        controlHeightSM: 32,
        controlHeightLG: 48,
        paddingInline: 16,
        paddingInlineSM: 12,
        paddingInlineLG: 24,
        fontWeight: 500,
        primaryShadow: "0 2px 4px rgba(59, 130, 246, 0.15)",
        defaultShadow: "0 2px 4px rgba(0, 0, 0, 0.05)",
      },
      Input: {
        borderRadius: 10, // Slightly more rounded for better header integration
        controlHeight: 42, // Bigger search bar
        controlHeightSM: 32,
        controlHeightLG: 40,
        paddingInline: 16, // More padding for better feel
        fontSize: 14,
        colorBgContainer: "#303030", // Match search input background
        colorBorder: "#404040", // Subtle border
        colorBorderHover: "#555",
        activeShadow: "0 0 0 3px rgba(46, 100, 84, 0.15)",
        hoverBorderColor: "#718096",
        colorText: "#f7fafc", // High contrast white text
        colorTextPlaceholder: "#a0aec0",
      },
      Card: {
        borderRadius: 8,
        boxShadow: "0 2px 4px rgba(0, 0, 0, 0.05)",
        headerBg: "transparent",
        bodyPadding: 24,
        paddingLG: 32,
      },
      Badge: {
        borderRadius: 6,
        fontWeight: 600,
        fontSize: 11,
        fontSizeSM: 10,
      },
      Avatar: {
        borderRadius: 8,
        fontSize: 14,
        fontWeight: 600,
      },
      Switch: {
        colorPrimary: "#2E6454", // Project primary color scheme
        colorPrimaryHover: "#1F4A3A", // Darker green for hover
        colorPrimaryActive: "#0F2A1D", // Even darker for active
        borderRadius: 12,
        trackBg: "#404040", // Dark track background matching theme
        trackBgChecked: "#2E6454", // Green when checked
      },
      Dropdown: {
        colorBgElevated: mytheme === "dark" ? "#404040" : "#ffffff",
        borderRadius: 8,
        boxShadowSecondary: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
        paddingBlock: 8,
      },
      Tooltip: {
        borderRadius: 6,
        fontSize: 12,
        colorBgSpotlight: mytheme === "dark" ? "#404040" : "#1f2937",
      },
    },
  }

  return (
    <ConfigProvider theme={modernProfessionalTheme}>
      <style>
        {`
          /* Enhanced Menu Styling */
          .dark-menu-override .ant-menu-item,
          .dark-menu-override .ant-menu-submenu-title,
          .dark-menu-override .ant-menu-submenu > .ant-menu-submenu-title {
            color: #e5e7eb !important;
            transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1) !important;
            position: relative;
          }

          /* Enhanced Hover States */
          .dark-menu-override .ant-menu-item:hover,
          .dark-menu-override .ant-menu-submenu-title:hover,
          .dark-menu-override .ant-menu-submenu > .ant-menu-submenu-title:hover {
            color: #f9fafb !important;
            transform: translateX(2px);
          }

          /* Enhanced Selected States */
          .dark-menu-override .ant-menu-item-selected {
            color: #2E6454 !important;
            font-weight: 600 !important;
            position: relative;
          }

          /* Left Border Indicator for Active Items */
          .dark-menu-override .ant-menu-item-selected::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background: #2E6454;
            border-radius: 0 2px 2px 0;
          }

          /* Link Color Inheritance */
          .dark-menu-override .ant-menu-item a,
          .dark-menu-override .ant-menu-submenu-title a,
          .dark-menu-override .ant-menu-submenu > .ant-menu-submenu-title a {
            color: inherit !important;
          }

          /* Title Content Styling */
          .dark-menu-override .ant-menu-submenu-title .ant-menu-title-content {
            color: inherit !important;
          }

          /* Submenu Background */
          .dark-menu-override .ant-menu-sub {
            background: #1A1A1A !important;
          }
          .dark-menu-override .ant-menu-submenu .ant-menu-sub {
            background: #1A1A1A !important;
          }

          /* Icon Enhancements */
          .dark-menu-override .ant-menu-item .anticon,
          .dark-menu-override .ant-menu-submenu-title .anticon {
            font-size: 20px !important;
            transition: all 200ms ease !important;
          }

          /* Badge Enhancements */
          .menu-badge {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
            box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3) !important;
            animation: pulse 2s infinite !important;
            font-size: 10px !important;
            font-weight: 600 !important;
            min-width: 18px !important;
            height: 18px !important;
            border-radius: 9px !important;
            border: 2px solid ${mytheme === "dark" ? "#303030" : "#ffffff"} !important;
          }

          /* Pulse Animation for Badges */
          @keyframes pulse {
            0%, 100% {
              opacity: 1;
            }
            50% {
              opacity: 0.7;
            }
          }

          /* Section Dividers */
          .menu-section-divider {
            height: 1px;
            background: ${mytheme === "dark" ? "#404040" : "#e5e7eb"};
            margin: 8px 16px;
            opacity: 0.5;
          }

          /* Collapsed State Improvements */
          .dark-menu-override.ant-menu-inline-collapsed .ant-menu-item,
          .dark-menu-override.ant-menu-inline-collapsed .ant-menu-submenu-title {
            padding-inline: 16px !important;
          }

          /* Smooth Chevron Rotation */
          .dark-menu-override .ant-menu-submenu-arrow {
            transition: transform 200ms ease !important;
          }

          .dark-menu-override .ant-menu-submenu-open > .ant-menu-submenu-title .ant-menu-submenu-arrow {
            transform: rotate(90deg) !important;
          }
        `}
      </style>
      <NProgress isAnimating={mainLoading} key={location.key} />

      {/* Increased Max-width Container to 1400px or 85vw */}
      <div
        className="admin-layout-wrapper"
        style={{
          maxWidth: "min(1800px, 85vw)", // Better utilization on larger screens
          margin: "0 auto",
          minHeight: "100vh",
          background: modernProfessionalTheme.token.colorBgLayout, // Container gray entire page background
          position: "relative",
        }}
      >
        {/* CSS Grid Layout Structure with Responsive Breakpoints */}
        <div
          className="admin-layout"
          style={{
            display: "grid",
            gridTemplateRows: "64px 1fr",
            gridTemplateColumns: isMobile
              ? "1fr"
              : collapsed
                ? "64px 1fr"
                : isTablet
                  ? "240px 1fr"  // Narrower sidebar on tablets
                  : "280px 1fr", // Full width on desktop
            minHeight: "100vh",
            transition: "grid-template-columns 300ms ease-in-out", // Smooth transitions for all interactive elements
          }}
        >
          {/* Mobile Overlay */}
          {isMobile && !collapsed && (
            <div
              className="position-fixed top-0 start-0 w-100 h-100"
              style={{
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                zIndex: 1000,
                backdropFilter: "blur(4px)",
              }}
              onClick={() => setCollapsed(true)}
              onTouchStart={onTouchStart}
              onTouchMove={onTouchMove}
              onTouchEnd={onTouchEnd}
            >
              <div
                ref={sidenavRef}
                onClick={(e) => e.stopPropagation()}
                style={{
                  transform: "translateX(0)",
                  transition: "transform 300ms ease-in-out",
                }}
              >
                <Sidebar
                  collapsed={false}
                  layoutState={layoutState}
                  onMenuSelect={handleMenuSelect}
                  onOpenChange={handleOpenChange}
                  theme={mytheme}
                  isMobile={true}
                />
              </div>
            </div>
          )}

          {/* Dark Themed Header */}
          <HeaderBar collapsed={collapsed} onToggle={handleToggle} isScrolled={isScrolled} />

          {/* Desktop Sidebar */}
          {!isMobile && (
            <Sidebar
              collapsed={collapsed}
              layoutState={layoutState}
              onMenuSelect={handleMenuSelect}
              onOpenChange={handleOpenChange}
              theme={mytheme}
              isMobile={false}
            />
          )}

          {/* Main Content with Natural Footer Flow */}
          <AppContent collapsed={collapsed} />
        </div>
      </div>

      <ModalController />
    </ConfigProvider>
  )
}
