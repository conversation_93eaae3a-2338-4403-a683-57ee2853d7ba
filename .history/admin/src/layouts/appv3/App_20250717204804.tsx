"use client"

import type React from "react"
import { useState, useRef, useEffect, useCallback } from "react"
import { useLocation } from "react-router"
import { ConfigProvider, theme } from "antd"
import { useMediaQuery } from "react-responsive"
import { useThemeStore } from "@/store/themeStore"
import { NProgress } from "@/components"
import ModalController from "@/layouts/app/ModalController"
import { Sidebar } from "./components/Sidebar"
import { HeaderBar } from "./components/HeaderBar"
import { AppContent } from "./components/AppContent"
import type { LayoutState } from "./types"

const { defaultAlgorithm, darkAlgorithm } = theme

export const AppV3Layout: React.FC = () => {
  const location = useLocation()
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const isTablet = useMediaQuery({ maxWidth: 1024 })
  const sidenavRef = useRef<HTMLDivElement>(null)

  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)

  const { mytheme, collapsed, setCollapsed, mainLoading } = useThemeStore()

  const [layoutState, setLayoutState] = useState<LayoutState>({
    collapsed: isTablet && !isMobile ? true : false,
    selectedKeys: ["dashboard"],
    openKeys: ["overview"],
  })

  const minSwipeDistance = 50

  const handleToggle = useCallback(() => {
    setCollapsed(!collapsed)
  }, [collapsed, setCollapsed])

  const handleMenuSelect = useCallback((selectedKeys: string[]) => {
    setLayoutState((prev) => ({ ...prev, selectedKeys }))
  }, [])

  const handleOpenChange = useCallback((openKeys: string[]) => {
    setLayoutState((prev) => ({ ...prev, openKeys }))
  }, [])

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return

    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > minSwipeDistance
    const isRightSwipe = distance < -minSwipeDistance

    if (isRightSwipe && collapsed && isMobile) {
      setCollapsed(false)
    }
    if (isLeftSwipe && !collapsed && isMobile) {
      setCollapsed(true)
    }
  }

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobile && !collapsed && sidenavRef.current && !sidenavRef.current.contains(event.target as Node)) {
        setCollapsed(true)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [isMobile, collapsed, setCollapsed])

  // Auto-collapse on tablet, expand on desktop
  useEffect(() => {
    if (isTablet && !isMobile) {
      setCollapsed(true)
    } else if (!isTablet && !isMobile) {
      setCollapsed(false)
    }
  }, [isTablet, isMobile, setCollapsed])

  // Professional Dark Theme Configuration
  const professionalDarkTheme = {
    algorithm: mytheme === "dark" ? darkAlgorithm : defaultAlgorithm,
    token: {
      // Professional Color System
      colorPrimary: "#3B82F6",
      colorSuccess: "#10B981",
      colorWarning: "#F59E0B",
      colorError: "#EF4444",
      colorInfo: "#3B82F6",

      // Inter Font System
      fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontSize: 13,
      fontSizeHeading1: 24,
      fontSizeHeading2: 20,
      fontSizeHeading3: 16,
      fontSizeHeading4: 14,
      fontSizeHeading5: 13,
      fontWeightStrong: 600,

      // Precise Spacing System (4px/8px/12px/16px)
      padding: 12,
      paddingXS: 4,
      paddingSM: 8,
      paddingLG: 16,
      paddingXL: 24,
      margin: 12,
      marginXS: 4,
      marginSM: 8,
      marginLG: 16,
      marginXL: 24,

      // Professional Border Radius
      borderRadius: 6,
      borderRadiusLG: 8,
      borderRadiusSM: 4,
      borderRadiusXS: 3,

      // Subtle Professional Shadows
      boxShadow: mytheme === "dark" ? "0 1px 3px rgba(0, 0, 0, 0.3)" : "0 1px 3px rgba(0, 0, 0, 0.1)",
      boxShadowSecondary: mytheme === "dark" ? "0 1px 2px rgba(0, 0, 0, 0.2)" : "0 1px 2px rgba(0, 0, 0, 0.05)",
      boxShadowTertiary:
        mytheme === "dark"
          ? "0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.2)"
          : "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",

      // Dark Theme Color Palette
      colorBgContainer: mytheme === "dark" ? "#1a1a1a" : "#FFFFFF",
      colorBgElevated: mytheme === "dark" ? "#262626" : "#FFFFFF",
      colorBgLayout: mytheme === "dark" ? "#0a0a0a" : "#FAFAFA",
      colorBgBase: mytheme === "dark" ? "#0a0a0a" : "#FFFFFF",
      colorBgSpotlight: mytheme === "dark" ? "#1a1a1a" : "#FFFFFF",

      // Professional Border Colors
      colorBorder: mytheme === "dark" ? "#374151" : "#E5E7EB",
      colorBorderSecondary: mytheme === "dark" ? "#4B5563" : "#F3F4F6",

      // High Contrast Text Colors (7:1 ratio for dark mode)
      colorText: mytheme === "dark" ? "#ffffff" : "#1F2937",
      colorTextSecondary: mytheme === "dark" ? "#d1d5db" : "#6B7280",
      colorTextTertiary: mytheme === "dark" ? "#9ca3af" : "#9CA3AF",
      colorTextQuaternary: mytheme === "dark" ? "#6b7280" : "#D1D5DB",

      // Subtle Interactive States
      colorFill: mytheme === "dark" ? "rgba(255, 255, 255, 0.06)" : "rgba(0, 0, 0, 0.02)",
      colorFillSecondary: mytheme === "dark" ? "rgba(255, 255, 255, 0.04)" : "rgba(0, 0, 0, 0.01)",
      colorFillTertiary: mytheme === "dark" ? "rgba(255, 255, 255, 0.02)" : "rgba(0, 0, 0, 0.005)",

      // Compact Control Heights
      controlHeight: 36,
      controlHeightSM: 28,
      controlHeightLG: 44,

      // Professional Line Heights
      lineHeight: 1.5,
      lineHeightHeading1: 1.2,
      lineHeightHeading2: 1.3,
      lineHeightHeading3: 1.4,
    },
    components: {
      Layout: {
        siderBg: mytheme === "dark" ? "#1a1a1a" : "#FFFFFF",
        headerBg: mytheme === "dark" ? "#262626" : "#FFFFFF",
        bodyBg: mytheme === "dark" ? "#0a0a0a" : "#FAFAFA",
        footerBg: mytheme === "dark" ? "#1a1a1a" : "#FFFFFF",
        headerHeight: 56,
        headerPadding: "0 16px",
        triggerBg: mytheme === "dark" ? "#404040" : "#F3F4F6",
        triggerColor: mytheme === "dark" ? "#ffffff" : "#1F2937",
      },
      Menu: {
        itemBg: "transparent",
        itemSelectedBg: mytheme === "dark" ? "rgba(59, 130, 246, 0.15)" : "rgba(59, 130, 246, 0.05)",
        itemHoverBg: mytheme === "dark" ? "#404040" : "rgba(0, 0, 0, 0.02)",
        itemActiveBg: mytheme === "dark" ? "rgba(59, 130, 246, 0.2)" : "rgba(59, 130, 246, 0.08)",
        itemSelectedColor: "#3B82F6",
        itemColor: mytheme === "dark" ? "#ffffff" : "#1F2937",
        itemHoverColor: mytheme === "dark" ? "#ffffff" : "#1F2937",
        subMenuItemBg: "transparent",
        groupTitleColor: mytheme === "dark" ? "#9ca3af" : "#6B7280",
        iconSize: 16,
        itemHeight: 36,
        collapsedIconSize: 16,
        itemMarginBlock: 1,
        itemMarginInline: 8,
        itemPaddingInline: 12,
        itemBorderRadius: 6,
        subMenuItemBorderRadius: 4,
        fontSize: 13,
        fontWeight: 500,
      },
      Button: {
        borderRadius: 6,
        controlHeight: 36,
        controlHeightSM: 28,
        controlHeightLG: 44,
        paddingInline: 12,
        paddingInlineSM: 8,
        paddingInlineLG: 16,
        fontWeight: 500,
        primaryShadow: mytheme === "dark" ? "0 1px 2px rgba(59, 130, 246, 0.3)" : "0 1px 2px rgba(59, 130, 246, 0.2)",
        defaultShadow: mytheme === "dark" ? "0 1px 2px rgba(0, 0, 0, 0.2)" : "0 1px 2px rgba(0, 0, 0, 0.05)",
      },
      Input: {
        borderRadius: 6,
        controlHeight: 36,
        controlHeightSM: 28,
        controlHeightLG: 44,
        paddingInline: 12,
        fontSize: 13,
        colorBgContainer: mytheme === "dark" ? "#262626" : "#FFFFFF",
        colorBorder: mytheme === "dark" ? "#374151" : "#E5E7EB",
        colorBorderHover: mytheme === "dark" ? "#4B5563" : "#9CA3AF",
        activeShadow: "0 0 0 2px rgba(59, 130, 246, 0.1)",
      },
      Badge: {
        borderRadius: 6,
        fontWeight: 600,
        fontSize: 10,
        fontSizeSM: 9,
      },
      Avatar: {
        borderRadius: 6,
        fontSize: 12,
        fontWeight: 600,
      },
      Switch: {
        colorPrimary: "#3B82F6",
        colorPrimaryHover: "#2563EB",
        borderRadius: 10,
      },
      Dropdown: {
        colorBgElevated: mytheme === "dark" ? "#262626" : "#FFFFFF",
        borderRadius: 8,
        boxShadowSecondary:
          mytheme === "dark"
            ? "0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.2)"
            : "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
        paddingBlock: 8,
      },
      Tooltip: {
        borderRadius: 4,
        fontSize: 11,
        colorBgSpotlight: mytheme === "dark" ? "#262626" : "#1F2937",
      },
    },
  }

  // CSS Custom Properties for Theme
  useEffect(() => {
    const root = document.documentElement
    if (mytheme === "dark") {
      root.style.setProperty("--admin-sidebar-bg", "#1a1a1a")
      root.style.setProperty("--admin-header-bg", "linear-gradient(135deg, #1f2937 0%, #374151 100%)")
      root.style.setProperty("--admin-content-bg", "#0a0a0a")
      root.style.setProperty("--admin-text-color", "#ffffff")
      root.style.setProperty("--admin-border-color", "#374151")
      root.style.setProperty("--admin-hover-bg", "#404040")
      root.style.setProperty("--admin-header-accent", "rgba(30, 64, 175, 0.1)")
    } else {
      root.style.setProperty("--admin-sidebar-bg", "#ffffff")
      root.style.setProperty("--admin-header-bg", "#ffffff")
      root.style.setProperty("--admin-content-bg", "#fafafa")
      root.style.setProperty("--admin-text-color", "#1f2937")
      root.style.setProperty("--admin-border-color", "#e5e7eb")
      root.style.setProperty("--admin-hover-bg", "rgba(0, 0, 0, 0.02)")
      root.style.setProperty("--admin-header-accent", "rgba(59, 130, 246, 0.05)")
    }
  }, [mytheme])

  return (
    <ConfigProvider theme={professionalDarkTheme}>
      <NProgress isAnimating={mainLoading} key={location.key} />

      <div
        className="admin-layout d-grid"
        style={{
          minHeight: "100vh",
          background: professionalDarkTheme.token.colorBgLayout,
          gridTemplateRows: "56px 1fr",
          gridTemplateColumns: isMobile ? "1fr" : collapsed ? "64px 1fr" : "240px 1fr",
          transition: "all 300ms ease-in-out",
        }}
      >
        {/* Mobile Overlay */}
        {isMobile && !collapsed && (
          <div
            className="position-fixed top-0 start-0 w-100 h-100"
            style={{
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              zIndex: 1000,
              backdropFilter: "blur(2px)",
            }}
            onClick={() => setCollapsed(true)}
            onTouchStart={onTouchStart}
            onTouchMove={onTouchMove}
            onTouchEnd={onTouchEnd}
          >
            <div
              ref={sidenavRef}
              onClick={(e) => e.stopPropagation()}
              style={{
                transform: "translateX(0)",
                transition: "transform 300ms ease-in-out",
              }}
            >
              <Sidebar
                collapsed={false}
                layoutState={layoutState}
                onMenuSelect={handleMenuSelect}
                onOpenChange={handleOpenChange}
                theme={mytheme}
                isMobile={true}
              />
            </div>
          </div>
        )}

        {/* Header */}
        <HeaderBar collapsed={collapsed} onToggle={handleToggle} />

        {/* Desktop Sidebar */}
        {!isMobile && (
          <Sidebar
            collapsed={collapsed}
            layoutState={layoutState}
            onMenuSelect={handleMenuSelect}
            onOpenChange={handleOpenChange}
            theme={mytheme}
            isMobile={false}
          />
        )}

        {/* Main Content */}
        <AppContent collapsed={collapsed} />
      </div>

      <ModalController />
    </ConfigProvider>
  )
}
