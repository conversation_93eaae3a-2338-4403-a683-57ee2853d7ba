"use client"

import type React from "react"
import { useState, useRef, useEffect, useCallback } from "react"
import { useLocation } from "react-router"
import { ConfigProvider, theme } from "antd"
import { useMediaQuery } from "react-responsive"
import { useThemeStore } from "@/store/themeStore"
import { NProgress } from "@/components"
import ModalController from "@/layouts/app/ModalController"
import { Sidebar } from "./components/Sidebar"
import { HeaderBar } from "./components/HeaderBar"
import { AppContent } from "./components/AppContent"
import type { LayoutState } from "./types"

const { defaultAlgorithm, darkAlgorithm } = theme

export const AppV3Layout: React.FC = () => {
  const location = useLocation()
  const isMobile = useMediaQuery({ maxWidth: 768 })
  const isTablet = useMediaQuery({ maxWidth: 1024 })
  const sidenavRef = useRef<HTMLDivElement>(null)

  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)

  const { mytheme, collapsed, setCollapsed, mainLoading } = useThemeStore()

  const [layoutState, setLayoutState] = useState<LayoutState>({
    collapsed: isTablet && !isMobile ? true : false,
    selectedKeys: ["dashboard"],
    openKeys: ["overview"],
  })

  const minSwipeDistance = 50

  const handleToggle = useCallback(() => {
    setCollapsed(!collapsed)
  }, [collapsed, setCollapsed])

  const handleMenuSelect = useCallback((selectedKeys: string[]) => {
    setLayoutState((prev) => ({ ...prev, selectedKeys }))
  }, [])

  const handleOpenChange = useCallback((openKeys: string[]) => {
    setLayoutState((prev) => ({ ...prev, openKeys }))
  }, [])

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return

    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > minSwipeDistance
    const isRightSwipe = distance < -minSwipeDistance

    if (isRightSwipe && collapsed && isMobile) {
      setCollapsed(false)
    }
    if (isLeftSwipe && !collapsed && isMobile) {
      setCollapsed(true)
    }
  }

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobile && !collapsed && sidenavRef.current && !sidenavRef.current.contains(event.target as Node)) {
        setCollapsed(true)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [isMobile, collapsed, setCollapsed])

  // Auto-collapse on tablet, expand on desktop
  useEffect(() => {
    if (isTablet && !isMobile) {
      setCollapsed(true)
    } else if (!isTablet && !isMobile) {
      setCollapsed(false)
    }
  }, [isTablet, isMobile, setCollapsed])

  // Professional Enterprise Theme Configuration
  const enterpriseThemeConfig = {
    algorithm: mytheme === "dark" ? darkAlgorithm : defaultAlgorithm,
    token: {
      // Professional Color System
      colorPrimary: "#3B82F6",
      colorSuccess: "#10B981",
      colorWarning: "#F59E0B",
      colorError: "#EF4444",
      colorInfo: "#3B82F6",

      // Inter Font System
      fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontSize: 13,
      fontSizeHeading1: 24,
      fontSizeHeading2: 20,
      fontSizeHeading3: 16,
      fontSizeHeading4: 14,
      fontSizeHeading5: 13,
      fontWeightStrong: 600,

      // Compact Spacing System (8px/12px/16px)
      padding: 12,
      paddingXS: 8,
      paddingSM: 12,
      paddingLG: 16,
      paddingXL: 24,
      margin: 12,
      marginXS: 8,
      marginSM: 12,
      marginLG: 16,
      marginXL: 24,

      // Professional Border Radius
      borderRadius: 6,
      borderRadiusLG: 8,
      borderRadiusSM: 4,
      borderRadiusXS: 3,

      // Subtle Professional Shadows
      boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
      boxShadowSecondary: "0 1px 2px rgba(0, 0, 0, 0.05)",
      boxShadowTertiary: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",

      // Enterprise Color Palette
      colorBgContainer: mytheme === "dark" ? "#1F2937" : "#FFFFFF",
      colorBgElevated: mytheme === "dark" ? "#374151" : "#FFFFFF",
      colorBgLayout: mytheme === "dark" ? "#111827" : "#FAFAFA",
      colorBgBase: mytheme === "dark" ? "#111827" : "#FFFFFF",
      colorBgSpotlight: mytheme === "dark" ? "#1F2937" : "#FFFFFF",

      // Professional Border Colors
      colorBorder: mytheme === "dark" ? "#374151" : "#E5E7EB",
      colorBorderSecondary: mytheme === "dark" ? "#4B5563" : "#F3F4F6",

      // High Contrast Text Colors (4.5:1 ratio)
      colorText: mytheme === "dark" ? "#F9FAFB" : "#1F2937",
      colorTextSecondary: mytheme === "dark" ? "#D1D5DB" : "#6B7280",
      colorTextTertiary: mytheme === "dark" ? "#9CA3AF" : "#9CA3AF",
      colorTextQuaternary: mytheme === "dark" ? "#6B7280" : "#D1D5DB",

      // Subtle Interactive States
      colorFill: mytheme === "dark" ? "rgba(255, 255, 255, 0.04)" : "rgba(0, 0, 0, 0.02)",
      colorFillSecondary: mytheme === "dark" ? "rgba(255, 255, 255, 0.02)" : "rgba(0, 0, 0, 0.01)",
      colorFillTertiary: mytheme === "dark" ? "rgba(255, 255, 255, 0.01)" : "rgba(0, 0, 0, 0.005)",

      // Compact Control Heights
      controlHeight: 36,
      controlHeightSM: 28,
      controlHeightLG: 44,

      // Professional Line Heights
      lineHeight: 1.5,
      lineHeightHeading1: 1.2,
      lineHeightHeading2: 1.3,
      lineHeightHeading3: 1.4,
    },
    components: {
      Layout: {
        siderBg: mytheme === "dark" ? "#1F2937" : "#FFFFFF",
        headerBg: mytheme === "dark" ? "#1F2937" : "#FFFFFF",
        bodyBg: mytheme === "dark" ? "#111827" : "#FAFAFA",
        footerBg: mytheme === "dark" ? "#1F2937" : "#FFFFFF",
        headerHeight: 56,
        headerPadding: "0 16px",
        triggerBg: mytheme === "dark" ? "#374151" : "#F3F4F6",
        triggerColor: mytheme === "dark" ? "#F9FAFB" : "#1F2937",
      },
      Menu: {
        itemBg: "transparent",
        itemSelectedBg: mytheme === "dark" ? "rgba(59, 130, 246, 0.1)" : "rgba(59, 130, 246, 0.05)",
        itemHoverBg: mytheme === "dark" ? "rgba(255, 255, 255, 0.04)" : "rgba(0, 0, 0, 0.02)",
        itemActiveBg: mytheme === "dark" ? "rgba(59, 130, 246, 0.15)" : "rgba(59, 130, 246, 0.08)",
        itemSelectedColor: "#3B82F6",
        itemColor: mytheme === "dark" ? "#F9FAFB" : "#1F2937",
        itemHoverColor: mytheme === "dark" ? "#F9FAFB" : "#1F2937",
        subMenuItemBg: "transparent",
        groupTitleColor: "#6B7280",
        iconSize: 16,
        itemHeight: 36,
        collapsedIconSize: 16,
        itemMarginBlock: 1,
        itemMarginInline: 8,
        itemPaddingInline: 12,
        itemBorderRadius: 6,
        subMenuItemBorderRadius: 4,
        fontSize: 13,
        fontWeight: 500,
      },
      Button: {
        borderRadius: 6,
        controlHeight: 36,
        controlHeightSM: 28,
        controlHeightLG: 44,
        paddingInline: 12,
        paddingInlineSM: 8,
        paddingInlineLG: 16,
        fontWeight: 500,
        primaryShadow: "0 1px 2px rgba(59, 130, 246, 0.2)",
        defaultShadow: "0 1px 2px rgba(0, 0, 0, 0.05)",
      },
      Input: {
        borderRadius: 6,
        controlHeight: 36,
        controlHeightSM: 28,
        controlHeightLG: 44,
        paddingInline: 12,
        fontSize: 13,
        colorBgContainer: mytheme === "dark" ? "#374151" : "#FFFFFF",
        colorBorder: mytheme === "dark" ? "#4B5563" : "#E5E7EB",
        colorBorderHover: mytheme === "dark" ? "#6B7280" : "#9CA3AF",
        activeShadow: "0 0 0 2px rgba(59, 130, 246, 0.1)",
      },
      Badge: {
        borderRadius: 6,
        fontWeight: 600,
        fontSize: 10,
        fontSizeSM: 9,
      },
      Avatar: {
        borderRadius: 6,
        fontSize: 12,
        fontWeight: 600,
      },
      Switch: {
        colorPrimary: "#3B82F6",
        colorPrimaryHover: "#2563EB",
        borderRadius: 10,
      },
      Dropdown: {
        colorBgElevated: mytheme === "dark" ? "#374151" : "#FFFFFF",
        borderRadius: 8,
        boxShadowSecondary: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
        paddingBlock: 8,
      },
      Tooltip: {
        borderRadius: 4,
        fontSize: 11,
        colorBgSpotlight: mytheme === "dark" ? "#374151" : "#1F2937",
      },
    },
  }

  return (
    <ConfigProvider theme={enterpriseThemeConfig}>
      <NProgress isAnimating={mainLoading} key={location.key} />

      <div
        className="admin-layout"
        style={{
          minHeight: "100vh",
          background: enterpriseThemeConfig.token.colorBgLayout,
          display: "grid",
          gridTemplateRows: "56px 1fr",
          gridTemplateColumns: isMobile ? "1fr" : collapsed ? "64px 1fr" : "240px 1fr",
          transition: "grid-template-columns 300ms cubic-bezier(0.4, 0, 0.2, 1)",
        }}
      >
        {/* Mobile Overlay */}
        {isMobile && !collapsed && (
          <div
            style={{
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "rgba(0, 0, 0, 0.4)",
              zIndex: 1000,
              backdropFilter: "blur(2px)",
            }}
            onClick={() => setCollapsed(true)}
            onTouchStart={onTouchStart}
            onTouchMove={onTouchMove}
            onTouchEnd={onTouchEnd}
          >
            <div
              ref={sidenavRef}
              onClick={(e) => e.stopPropagation()}
              style={{
                transform: "translateX(0)",
                transition: "transform 300ms cubic-bezier(0.4, 0, 0.2, 1)",
              }}
            >
              <Sidebar
                collapsed={false}
                layoutState={layoutState}
                onMenuSelect={handleMenuSelect}
                onOpenChange={handleOpenChange}
                theme={mytheme}
                isMobile={true}
              />
            </div>
          </div>
        )}

        {/* Header */}
        <HeaderBar collapsed={collapsed} onToggle={handleToggle} />

        {/* Desktop Sidebar */}
        {!isMobile && (
          <Sidebar
            collapsed={collapsed}
            layoutState={layoutState}
            onMenuSelect={handleMenuSelect}
            onOpenChange={handleOpenChange}
            theme={mytheme}
            isMobile={false}
          />
        )}

        {/* Main Content */}
        <AppContent collapsed={collapsed} />
      </div>

      <ModalController />
    </ConfigProvider>
  )
}
