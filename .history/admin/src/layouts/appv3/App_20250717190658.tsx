import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useLocation } from 'react-router';
import { Layout, ConfigProvider, theme } from 'antd';
import { useMediaQuery } from 'react-responsive';
import { useThemeStore } from '@/store/themeStore';
import { NProgress } from '@/components';
import ModalController from '@/layouts/app/ModalController';
import { Sidebar } from './components/Sidebar';
import { HeaderBar } from './components/HeaderBar';
import { AppContent } from './components/AppContent';
import type { LayoutState } from './types';

const { defaultAlgorithm, darkAlgorithm } = theme;

export const AppV3Layout: React.FC = () => {
  const location = useLocation();
  const isMobile = useMediaQuery({ maxWidth: 769 });
  const sidenavRef = useRef<HTMLDivElement>(null);
  
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  const {
    mytheme,
    collapsed,
    setCollapsed,
    mainLoading,
  } = useThemeStore();

  const [layoutState, setLayoutState] = useState<LayoutState>({
    collapsed: false,
    selectedKeys: ['dashboard-home'],
    openKeys: [],
  });

  // Minimum swipe distance (in px)
  const minSwipeDistance = 50;

  const handleToggle = useCallback(() => {
    setCollapsed(!collapsed);
  }, [collapsed, setCollapsed]);

  const handleMenuSelect = useCallback((selectedKeys: string[]) => {
    setLayoutState((prev) => ({ ...prev, selectedKeys }));
  }, []);

  const handleOpenChange = useCallback((openKeys: string[]) => {
    setLayoutState((prev) => ({ ...prev, openKeys }));
  }, []);

  // Touch handlers for mobile swipe
  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isRightSwipe && collapsed && isMobile) {
      setCollapsed(false);
    }
    if (isLeftSwipe && !collapsed && isMobile) {
      setCollapsed(true);
    }
  };

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobile && !collapsed && sidenavRef.current && !sidenavRef.current.contains(event.target as Node)) {
        setCollapsed(true);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isMobile, collapsed, setCollapsed]);

  // Auto-collapse sidebar on mobile orientation change
  useEffect(() => {
    const handleOrientationChange = () => {
      if (isMobile) {
        setCollapsed(true);
      }
    };

    window.addEventListener('orientationchange', handleOrientationChange);
    return () => window.removeEventListener('orientationchange', handleOrientationChange);
  }, [isMobile, setCollapsed]);

  // Theme configuration for Ant Design
  const themeConfig = {
    algorithm: mytheme === 'dark' ? darkAlgorithm : defaultAlgorithm,
    token: {
      colorPrimary: '#1890ff',
      borderRadius: 6,
    },
    components: {
      Layout: {
        siderBg: mytheme === 'dark' ? '#001529' : '#ffffff',
        headerBg: mytheme === 'dark' ? '#001529' : '#ffffff',
      },
      Menu: {
        itemBg: 'transparent',
        itemSelectedBg: mytheme === 'dark' ? '#1890ff20' : '#e6f7ff',
        itemHoverBg: mytheme === 'dark' ? '#ffffff10' : '#f5f5f5',
      },
    },
  };

  return (
    <ConfigProvider theme={themeConfig}>
      <NProgress isAnimating={mainLoading} key={location.key} />
      
      <Layout 
        className="min-vh-100"
        style={{
          touchAction: 'pan-y pinch-zoom'
        }}
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
      >
        {/* Mobile Overlay & Sidebar */}
        {isMobile && !collapsed && (
          <div
            className="mobile-sidenav-overlay"
            onClick={() => setCollapsed(true)}
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.45)',
              zIndex: 150,
              animation: 'fadeIn 0.2s ease-out'
            }}
          >
            <div
              ref={sidenavRef}
              onClick={(e) => e.stopPropagation()}
              style={{
                transform: 'translateX(0)',
                transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                animation: 'slideInLeft 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
              }}
            >
              <Sidebar
                collapsed={false}
                layoutState={layoutState}
                onMenuSelect={handleMenuSelect}
                onOpenChange={handleOpenChange}
                theme="light"
                isMobile={true}
              />
            </div>
          </div>
        )}

        {/* Desktop Sidebar */}
        {!isMobile && (
          <Sidebar
            collapsed={collapsed}
            layoutState={layoutState}
            onMenuSelect={handleMenuSelect}
            onOpenChange={handleOpenChange}
            theme={mytheme}
            isMobile={false}
          />
        )}

        <Layout>
          <HeaderBar collapsed={collapsed} onToggle={handleToggle} />
          <AppContent collapsed={collapsed} />
        </Layout>
      </Layout>

      <ModalController />
    </ConfigProvider>
  );
};
