"use client"

import type React from "react"
import { useState, useRef, useEffect, useCallback } from "react"
import { useLocation } from "react-router"
import { Layout, ConfigProvider, theme } from "antd"
import { useMediaQuery } from "react-responsive"
import { useThemeStore } from "@/store/themeStore"
import { NProgress } from "@/components"
import ModalController from "@/layouts/app/ModalController"
import { Sidebar } from "./components/Sidebar"
import { HeaderBar } from "./components/HeaderBar"
import { AppContent } from "./components/AppContent"
import type { LayoutState } from "./types"

const { defaultAlgorithm, darkAlgorithm } = theme

export const AppV3Layout: React.FC = () => {
  const location = useLocation()
  const isMobile = useMediaQuery({ maxWidth: 769 })
  const sidenavRef = useRef<HTMLDivElement>(null)

  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)

  const { mytheme, collapsed, setCollapsed, mainLoading } = useThemeStore()

  const [layoutState, setLayoutState] = useState<LayoutState>({
    collapsed: false,
    selectedKeys: ["dashboard-home"],
    openKeys: [],
  })

  const minSwipeDistance = 50

  const handleToggle = useCallback(() => {
    setCollapsed(!collapsed)
  }, [collapsed, setCollapsed])

  const handleMenuSelect = useCallback((selectedKeys: string[]) => {
    setLayoutState((prev) => ({ ...prev, selectedKeys }))
  }, [])

  const handleOpenChange = useCallback((openKeys: string[]) => {
    setLayoutState((prev) => ({ ...prev, openKeys }))
  }, [])

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return

    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > minSwipeDistance
    const isRightSwipe = distance < -minSwipeDistance

    if (isRightSwipe && collapsed && isMobile) {
      setCollapsed(false)
    }
    if (isLeftSwipe && !collapsed && isMobile) {
      setCollapsed(true)
    }
  }

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobile && !collapsed && sidenavRef.current && !sidenavRef.current.contains(event.target as Node)) {
        setCollapsed(true)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [isMobile, collapsed, setCollapsed])

  useEffect(() => {
    const handleOrientationChange = () => {
      if (isMobile) {
        setCollapsed(true)
      }
    }

    window.addEventListener("orientationchange", handleOrientationChange)
    return () => window.removeEventListener("orientationchange", handleOrientationChange)
  }, [isMobile, setCollapsed])

  // Fixed theme configuration
  const themeConfig = {
    algorithm: mytheme === "dark" ? darkAlgorithm : defaultAlgorithm,
    token: {
      colorPrimary: "#1677ff",
      colorSuccess: "#52c41a",
      colorWarning: "#faad14",
      colorError: "#ff4d4f",
      colorInfo: "#1677ff",
      borderRadius: 8,
      wireframe: false,
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
      fontSize: 14,
      // Layout colors
      colorBgContainer: mytheme === "dark" ? "#141414" : "#ffffff",
      colorBgElevated: mytheme === "dark" ? "#1f1f1f" : "#ffffff",
      colorBgLayout: mytheme === "dark" ? "#000000" : "#f0f2f5",
      colorBgBase: mytheme === "dark" ? "#000000" : "#ffffff",
      // Border colors
      colorBorder: mytheme === "dark" ? "#424242" : "#d9d9d9",
      colorBorderSecondary: mytheme === "dark" ? "#303030" : "#f0f0f0",
      // Text colors
      colorText: mytheme === "dark" ? "rgba(255, 255, 255, 0.88)" : "rgba(0, 0, 0, 0.88)",
      colorTextSecondary: mytheme === "dark" ? "rgba(255, 255, 255, 0.65)" : "rgba(0, 0, 0, 0.65)",
      colorTextTertiary: mytheme === "dark" ? "rgba(255, 255, 255, 0.45)" : "rgba(0, 0, 0, 0.45)",
      colorTextQuaternary: mytheme === "dark" ? "rgba(255, 255, 255, 0.25)" : "rgba(0, 0, 0, 0.25)",
      // Fill colors
      colorFill: mytheme === "dark" ? "rgba(255, 255, 255, 0.18)" : "rgba(0, 0, 0, 0.15)",
      colorFillSecondary: mytheme === "dark" ? "rgba(255, 255, 255, 0.12)" : "rgba(0, 0, 0, 0.06)",
      colorFillTertiary: mytheme === "dark" ? "rgba(255, 255, 255, 0.08)" : "rgba(0, 0, 0, 0.04)",
      colorFillQuaternary: mytheme === "dark" ? "rgba(255, 255, 255, 0.04)" : "rgba(0, 0, 0, 0.02)",
    },
    components: {
      Layout: {
        siderBg: mytheme === "dark" ? "#001529" : "#ffffff",
        headerBg: mytheme === "dark" ? "#001529" : "#ffffff",
        bodyBg: mytheme === "dark" ? "#000000" : "#f0f2f5",
        footerBg: mytheme === "dark" ? "#001529" : "#ffffff",
        headerHeight: isMobile ? 56 : 64,
        headerPadding: isMobile ? "0 16px" : "0 24px",
        triggerBg: mytheme === "dark" ? "#002140" : "#fafafa",
        triggerColor: mytheme === "dark" ? "rgba(255, 255, 255, 0.88)" : "rgba(0, 0, 0, 0.88)",
      },
      Menu: {
        itemBg: "transparent",
        itemSelectedBg: mytheme === "dark" ? "#1677ff1a" : "#e6f4ff",
        itemHoverBg: mytheme === "dark" ? "rgba(255, 255, 255, 0.08)" : "rgba(0, 0, 0, 0.04)",
        itemActiveBg: mytheme === "dark" ? "#1677ff33" : "#bae0ff",
        itemSelectedColor: "#1677ff",
        itemColor: mytheme === "dark" ? "rgba(255, 255, 255, 0.88)" : "rgba(0, 0, 0, 0.88)",
        itemHoverColor: mytheme === "dark" ? "rgba(255, 255, 255, 0.88)" : "rgba(0, 0, 0, 0.88)",
        subMenuItemBg: "transparent",
        groupTitleColor: mytheme === "dark" ? "rgba(255, 255, 255, 0.67)" : "rgba(0, 0, 0, 0.45)",
        iconSize: 16,
        itemHeight: 40,
        collapsedIconSize: 16,
        itemMarginBlock: 2,
        itemMarginInline: 8,
        itemPaddingInline: 16,
        itemBorderRadius: 6,
        subMenuItemBorderRadius: 6,
      },
      Button: {
        borderRadius: 6,
        controlHeight: 32,
        paddingInline: 15,
        fontWeight: 500,
        primaryShadow: "0 2px 0 rgba(5, 145, 255, 0.1)",
      },
      Input: {
        borderRadius: 6,
        controlHeight: 32,
        paddingInline: 11,
        colorBgContainer: mytheme === "dark" ? "#141414" : "#ffffff",
      },
      Card: {
        borderRadius: 8,
        paddingLG: 24,
        headerBg: "transparent",
        colorBgContainer: mytheme === "dark" ? "#141414" : "#ffffff",
      },
      Badge: {
        borderRadius: 10,
        fontWeight: 500,
      },
      Avatar: {
        borderRadius: 6,
      },
      Switch: {
        colorPrimary: "#1677ff",
        colorPrimaryHover: "#4096ff",
      },
      Dropdown: {
        colorBgElevated: mytheme === "dark" ? "#1f1f1f" : "#ffffff",
        borderRadius: 8,
      },
    },
  }

  return (
    <ConfigProvider theme={themeConfig}>
      <NProgress isAnimating={mainLoading} key={location.key} />

      <Layout
        className="min-vh-100"
        style={{
          touchAction: "pan-y pinch-zoom",
          background: mytheme === "dark" ? "#000000" : "#f0f2f5",
        }}
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
      >
        {/* Mobile Overlay & Sidebar */}
        {isMobile && !collapsed && (
          <div
            className="mobile-sidenav-overlay"
            onClick={() => setCollapsed(true)}
            style={{
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "rgba(0, 0, 0, 0.45)",
              zIndex: 150,
              animation: "fadeIn 0.2s ease-out",
            }}
          >
            <div
              ref={sidenavRef}
              onClick={(e) => e.stopPropagation()}
              style={{
                transform: "translateX(0)",
                transition: "transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                animation: "slideInLeft 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
              }}
            >
              <Sidebar
                collapsed={false}
                layoutState={layoutState}
                onMenuSelect={handleMenuSelect}
                onOpenChange={handleOpenChange}
                theme="light"
                isMobile={true}
              />
            </div>
          </div>
        )}

        {/* Desktop Sidebar */}
        {!isMobile && (
          <Sidebar
            collapsed={collapsed}
            layoutState={layoutState}
            onMenuSelect={handleMenuSelect}
            onOpenChange={handleOpenChange}
            theme={mytheme}
            isMobile={false}
          />
        )}

        <Layout>
          <HeaderBar collapsed={collapsed} onToggle={handleToggle} />
          <AppContent collapsed={collapsed} />
        </Layout>
      </Layout>

      <ModalController />
    </ConfigProvider>
  )
}
