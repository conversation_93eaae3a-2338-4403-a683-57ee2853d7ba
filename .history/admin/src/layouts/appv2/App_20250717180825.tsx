import { useState, useRef, useEffect } from 'react';
import { useLocation } from 'react-router';
import {
  Button,
  FloatButton,
  Layout,
} from 'antd';

import ModalController from '../app/ModalController';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';

import { Logo } from '@/components';
import { useMediaQuery } from 'react-responsive';
import SideNav from './SideNav';
import HeaderNav from './HeaderNav';
import FooterNav from '../app/FooterNav';
import { NProgress } from '../../components';
import { useThemeStore } from '@/store/themeStore';
import { ThemeManager } from '@/utils/themeUtils';
import ModernSearch from './ModernSearch';
import HeaderIcons from './HeaderIcons';
import RightDrawer from './RightDrawer';
import './AppV2.css';

const { Content } = Layout;

export const AppV2Layout = ({ children }) => {
  const location = useLocation();
  const isMobile = useMediaQuery({ maxWidth: 769 });
  const isTablet = useMediaQuery({ maxWidth: 1024 });

  const [drawerOpen, setDrawerOpen] = useState(false);

  const nodeRef = useRef<HTMLDivElement>(null);
  const floatBtnRef = useRef<HTMLDivElement>(null);
  const sidenavRef = useRef<HTMLDivElement>(null);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  const {
    themeMode,
    layoutOptions,
    getCurrentThemeColor,
    collapsed,
    setCollapsed,
    mainLoading,
    // Legacy support
    mytheme,
  } = useThemeStore();

  // Generate theme-aware component styles
  const componentStyles = ThemeManager.generateComponentStyles(themeMode, getCurrentThemeColor());

  // Minimum swipe distance (in px)
  const minSwipeDistance = 50;

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isRightSwipe && collapsed && isMobile) {
      setCollapsed(false);
    }
    if (isLeftSwipe && !collapsed && isMobile) {
      setCollapsed(true);
    }
  };

  // DISABLED: Close mobile menu when clicking outside (causing infinite loop with AppLayout)
  // useEffect(() => {
  //   const handleClickOutside = (event: MouseEvent) => {
  //     if (isMobile && !collapsed && sidenavRef.current && !sidenavRef.current.contains(event.target as Node)) {
  //       setCollapsed(true);
  //     }
  //   };

  //   document.addEventListener('mousedown', handleClickOutside);
  //   return () => document.removeEventListener('mousedown', handleClickOutside);
  // }, [isMobile, collapsed, setCollapsed]);

  // DISABLED: Auto-collapse sidebar on mobile orientation change (causing infinite loop with AppLayout)
  // useEffect(() => {
  //   const handleOrientationChange = () => {
  //     if (isMobile) {
  //       setCollapsed(true);
  //     }
  //   };

  //   window.addEventListener('orientationchange', handleOrientationChange);
  //   return () => window.removeEventListener('orientationchange', handleOrientationChange);
  // }, [isMobile, setCollapsed]);

  return (
    <>
      <NProgress isAnimating={mainLoading} key={location.key} />

      <HeaderNav
        style={{
          height: isMobile ? 50 : 55,
          padding: isMobile ? '0 8px' : '0 10px',
          background: componentStyles.header.background,
          position: layoutOptions.fixedHeader ? 'sticky' : 'static',
          top: 0,
          zIndex: 1000,
          boxShadow: isMobile
            ? '0 4px 12px rgba(0,0,0,0.15)'
            : '0 2px 8px rgba(0,0,0,0.08), 0 1px 3px rgba(0,0,0,0.06)',
          borderBottom: `1px solid ${componentStyles.header.borderColor}`,
          backdropFilter: 'blur(8px)',
          transition: 'all 0.2s ease',
          color: componentStyles.header.textColor,
        }}
        className='w-100 d-flex align-items-center theme-header'
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
      >

        <div className="logo-container">
          <Logo
            color={componentStyles.header.textColor}
            asLink
            href="/"
            imgSize={{ w: isMobile ? 26 : 30, h: isMobile ? 26 : 30 }}
          />
        </div>

        <div className="w-100 d-flex align-items-center justify-content-between">
          <div className="d-flex align-items-center">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{
                fontSize: isMobile ? '14px' : '16px',
                width: isMobile ? 40 : 44,
                height: isMobile ? 40 : 44,
                color: componentStyles.header.textColor,
                minWidth: isMobile ? 40 : 44,
                flexShrink: 0,
                borderRadius: '8px',
                transition: 'all 0.2s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: '16px'
              }}
              className="hamburger-menu-btn"
            />

            <div style={{ flex: 1, maxWidth: isMobile ? '240px' : '380px' }}>
              <ModernSearch
                placeholder="Search employees..."
                size="middle"
                style={{ width: '100%' }}
              />
            </div>
          </div>

          <HeaderIcons onSettingsClick={() => setDrawerOpen(true)} />
        </div>

      </HeaderNav>

      <Layout
        style={{
          minHeight: '100vh',
          touchAction: 'pan-y pinch-zoom'
        }}
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
      >

        {/* Mobile Overlay & SideNav */}
        {isMobile && !collapsed && (
          <div
            className="mobile-sidenav-overlay"
            onClick={() => setCollapsed(true)}
            style={{
              animation: 'fadeIn 0.2s ease-out'
            }}
          >
            <div
              ref={sidenavRef}
              onClick={(e) => e.stopPropagation()}
              style={{
                transform: 'translateX(0)',
                transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                animation: 'slideInLeft 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
              }}
            >
              <SideNav
                trigger={null}
                collapsed={false}
                width={layoutOptions.compactSidebar ? layoutOptions.sidebarWidth - 40 : layoutOptions.sidebarWidth}
                theme={themeMode}
                style={{
                  position: 'fixed',
                  top: 0,
                  bottom: 0,
                  left: 0,
                  height: '100vh',
                  zIndex: 200,
                  background: componentStyles.sidebar.background,
                  backgroundColor: componentStyles.sidebar.background,
                  border: 'none',
                  boxShadow: '4px 0 12px rgba(0, 0, 0, 0.15)',
                  paddingTop: isMobile ? 50 : 60,
                  color: componentStyles.sidebar.textColor,
                }}
                className="theme-sidebar"
              />
            </div>
          </div>
        )}

        {/* Desktop SideNav */}
        {!isMobile && (
          <SideNav
            trigger={null}
            collapsible
            collapsed={collapsed}
            onCollapse={setCollapsed}
            width={layoutOptions.compactSidebar ? layoutOptions.sidebarWidth - 40 : layoutOptions.sidebarWidth}
            theme={themeMode}
            style={{
              position: 'fixed',
              top: 55,
              bottom: 0,
              left: 0,
              zIndex: 100,
              background: componentStyles.sidebar.background,
              border: 'none',
              borderRight: `1px solid ${componentStyles.sidebar.borderColor}`,
              transition: 'all 0.2s ease',
              color: componentStyles.sidebar.textColor,
            }}
            className="theme-sidebar"
          />
        )}

        {/* Main Content */}
        <Layout style={{
          marginLeft: !isMobile ? (collapsed ? 80 : (layoutOptions.compactSidebar ? layoutOptions.sidebarWidth - 40 : layoutOptions.sidebarWidth)) : 0,
          transition: !isMobile ? 'margin-left 0.2s ease' : 'none'
        }}>

          <Content
            style={{
              padding: isMobile ? '8px' : (isTablet ? '12px 16px' : '16px 20px'),
              minHeight: 360,
              maxWidth: '100%',
              overflowX: 'hidden',
              background: componentStyles.content.background,
              color: componentStyles.content.textColor,
            }}
            className="theme-content"
          >
            <div ref={nodeRef} style={{ width: '100%', minWidth: 0 }}>
              {children}
            </div>
            <div ref={floatBtnRef}>
              <FloatButton.BackTop
                style={{
                  right: isMobile ? 16 : 24,
                  bottom: isMobile ? 16 : 24,
                  width: isMobile ? 40 : 48,
                  height: isMobile ? 40 : 48
                }}
              />
            </div>
          </Content>

          <FooterNav
            style={{
              textAlign: 'center',
              background: 'none',
              padding: isMobile ? '8px' : '16px'
            }}
          />
        </Layout>
      </Layout>

      <ModalController />

      <RightDrawer
        open={drawerOpen}
        onClose={() => setDrawerOpen(false)}
      />

    </>
  );
};
