import { useState, useRef, useEffect } from 'react';
import { useLocation, Outlet } from 'react-router';
import {
  Button,
  FloatButton,
  Layout,
} from 'antd';

import ModalController from '../app/ModalController';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';

import { Logo } from '@/components';
import { useMediaQuery } from 'react-responsive';
import SideNav from './SideNav';
import HeaderNav from './HeaderNav';
import FooterNav from '../app/FooterNav';
import { NProgress } from '../../components';
import { useThemeStore } from '@/store/themeStore';
import ModernSearch from './ModernSearch';
import HeaderIcons from './HeaderIcons';
import RightDrawer from './RightDrawer';
import './AppV2.css';

const { Content } = Layout;

export const AppV2Layout = ({ children }) => {
  const location = useLocation();
  const isMobile = useMediaQuery({ maxWidth: 769 });
  const isTablet = useMediaQuery({ maxWidth: 1024 });

  const [drawerOpen, setDrawerOpen] = useState(false);

  const nodeRef = useRef<HTMLDivElement>(null);
  const floatBtnRef = useRef<HTMLDivElement>(null);
  const sidenavRef = useRef<HTMLDivElement>(null);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  const {
    mytheme,
    collapsed,
    setCollapsed,
    mainLoading,
  } = useThemeStore();

  // Minimum swipe distance (in px)
  const minSwipeDistance = 50;

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isRightSwipe && collapsed && isMobile) {
      setCollapsed(false);
    }
    if (isLeftSwipe && !collapsed && isMobile) {
      setCollapsed(true);
    }
  };

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobile && !collapsed && sidenavRef.current && !sidenavRef.current.contains(event.target as Node)) {
        setCollapsed(true);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isMobile, collapsed, setCollapsed]);

  // Auto-collapse sidebar on mobile orientation change
  useEffect(() => {
    const handleOrientationChange = () => {
      if (isMobile) {
        setCollapsed(true);
      }
    };

    window.addEventListener('orientationchange', handleOrientationChange);
    return () => window.removeEventListener('orientationchange', handleOrientationChange);
  }, [isMobile, setCollapsed]);

  return (
    <>
      <NProgress isAnimating={mainLoading} key={location.key} />

      <HeaderNav
        style={{
          height: isMobile ? 50 : 55,
          padding: isMobile ? '0 8px' : '0 10px',
          background: mytheme === 'light'
            ? 'linear-gradient(135deg, #ffffff 0%, #fafbfc 100%)'
            : 'linear-gradient(135deg, #1A1A1A 0%, #2d2d2d 100%)',
          position: 'sticky',
          top: 0,
          zIndex: 1000,
          boxShadow: isMobile
            ? '0 4px 12px rgba(0,0,0,0.15)'
            : '0 2px 8px rgba(0,0,0,0.08), 0 1px 3px rgba(0,0,0,0.06)',
          borderBottom: '1px solid rgba(240, 240, 240, 0.8)',
          backdropFilter: 'blur(8px)',
          transition: 'all 0.2s ease',
        }}
        className='w-100 d-flex align-items-center'
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
      >

        <div className="logo-container">
          <Logo
            color={mytheme === 'light' ? '#2E6454' : 'white'}
            asLink
            href="/"
            imgSize={{ w: isMobile ? 26 : 30, h: isMobile ? 26 : 30 }}
          />
        </div>

        <div className="w-100 d-flex align-items-center justify-content-between">
          <div className="d-flex align-items-center">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{
                fontSize: isMobile ? '14px' : '16px',
                width: isMobile ? 40 : 44,
                height: isMobile ? 40 : 44,
                color: mytheme === 'light' ? '#2E6454' : '#fff',
                minWidth: isMobile ? 40 : 44,
                flexShrink: 0,
                borderRadius: '8px',
                transition: 'all 0.2s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: '16px'
              }}
              className="hamburger-menu-btn"
            />

            <div style={{ flex: 1, maxWidth: isMobile ? '240px' : '380px' }}>
              <ModernSearch
                placeholder="Search employees..."
                size="middle"
                style={{ width: '100%' }}
              />
            </div>
          </div>

          <HeaderIcons onSettingsClick={() => setDrawerOpen(true)} />
        </div>

      </HeaderNav>

      <Layout
        style={{
          minHeight: '100vh',
          touchAction: 'pan-y pinch-zoom'
        }}
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
      >

        {/* Mobile Overlay & SideNav */}
        {isMobile && !collapsed && (
          <div
            className="mobile-sidenav-overlay"
            onClick={() => setCollapsed(true)}
            style={{
              animation: 'fadeIn 0.2s ease-out'
            }}
          >
            <div
              ref={sidenavRef}
              onClick={(e) => e.stopPropagation()}
              style={{
                transform: 'translateX(0)',
                transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                animation: 'slideInLeft 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
              }}
            >
              <SideNav
                trigger={null}
                collapsed={false}
                width={280}
                theme="light"
                style={{
                  position: 'fixed',
                  top: 0,
                  bottom: 0,
                  left: 0,
                  height: '100vh',
                  zIndex: 200,
                  background: '#ffffff',
                  backgroundColor: '#ffffff',
                  border: 'none',
                  boxShadow: '4px 0 12px rgba(0, 0, 0, 0.15)',
                  paddingTop: isMobile ? 50 : 60,
                }}
              />
            </div>
          </div>
        )}

        {/* Desktop SideNav */}
        {!isMobile && (
          <SideNav
            trigger={null}
            collapsible
            collapsed={collapsed}
            onCollapse={setCollapsed}
            width={240}
            theme={mytheme}
            style={{
              position: 'fixed',
              top: 55,
              bottom: 0,
              left: 0,
              zIndex: 100,
              background: '#ffffff',
              border: 'none',
              borderRight: '1px solid #f0f0f0',
              transition: 'all 0.2s ease',
            }}
          />
        )}

        {/* Main Content */}
        <Layout style={{
          marginLeft: !isMobile ? (collapsed ? 80 : 240) : 0,
          transition: !isMobile ? 'margin-left 0.2s ease' : 'none'
        }}>

          <Content
            style={{
              padding: isMobile ? '8px' : (isTablet ? '12px 16px' : '16px 20px'),
              minHeight: 360,
              maxWidth: '100%',
              overflowX: 'hidden',
              background: '#f8f9fa'
            }}
          >
            <div ref={nodeRef} style={{ width: '100%', minWidth: 0 }}>
              {children}
            </div>
            <div ref={floatBtnRef}>
              <FloatButton.BackTop
                style={{
                  right: isMobile ? 16 : 24,
                  bottom: isMobile ? 16 : 24,
                  width: isMobile ? 40 : 48,
                  height: isMobile ? 40 : 48
                }}
              />
            </div>
          </Content>

          <FooterNav
            style={{
              textAlign: 'center',
              background: 'none',
              padding: isMobile ? '8px' : '16px'
            }}
          />
        </Layout>
      </Layout>

      <ModalController />

      <RightDrawer
        open={drawerOpen}
        onClose={() => setDrawerOpen(false)}
      />

    </>
  );
};
