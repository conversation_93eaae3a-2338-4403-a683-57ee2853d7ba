// ** Libraries
import React, { useState, useEffect } from 'react';
import { DatePicker, Spin, Typography, Tooltip } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useMediaQuery } from 'react-responsive';
import { ReloadOutlined } from '@ant-design/icons';
import numeral from 'numeral';

// ** Utils
import { api } from '@/utils/api';
import useTypography from '@/utils/typography';

// ** Store
import { useAuthStore } from '@/store/authStore';

// ** Components
import { StatCard, statCards } from './DashboardCards';

// ** Styles
import './DashboardCard.css';

const { RangePicker } = DatePicker;
const { Text } = Typography;

// ** Helper function to calculate current week starting Sunday
const getCurrentWeekRange = (): [Dayjs, Dayjs] => {
  const now = dayjs();
  const dayOfWeek = now.day();
  const daysToSubtract = dayOfWeek % 7;
  const weekStart = now.subtract(daysToSubtract, 'day').startOf('day');
  const weekEnd = weekStart.add(6, 'day').endOf('day');
  return [weekStart, weekEnd];
};

const DashboardCard = () => {
  const typography = useTypography().typography;
  const isMobile = useMediaQuery({ maxWidth: 767 });
  const { user } = useAuthStore();

  const [range, setRange] = useState<[Dayjs | null, Dayjs | null]>(() => {
    const [start, end] = getCurrentWeekRange();
    return [start, end];
  });
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<Record<string, number | string>>({});
  const [refreshing, setRefreshing] = useState(false);

  // ** Fetch dashboard statistics
  const fetchDashboardStats = async (startDate: string, endDate: string, isRefresh = false) => {
    if (!user?.account?.id) return;

    isRefresh ? setRefreshing(true) : setLoading(true);

    try {
      const response = await api('GetDashboardStatistics', {
        account_id: user.account.id,
        start: startDate,
        end: endDate
      });

      if (response?.status === 'success' && response?.data) {
        setStats(response.data);
      }
    } catch (err) {
      console.error('[DashboardCard] API Error:', err);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // ** Effect to fetch data when range changes
  useEffect(() => {
    if (!range[0] || !range[1] || !user?.account?.id) return;

    const startDate = range[0].format('YYYY-MM-DD');
    const endDate = range[1].format('YYYY-MM-DD');
    fetchDashboardStats(startDate, endDate);
  }, [range, user?.account?.id]);

  // ** Refresh handler
  const handleRefresh = async () => {
    if (!range[0] || !range[1]) return;

    const startDate = range[0].format('YYYY-MM-DD');
    const endDate = range[1].format('YYYY-MM-DD');
    await fetchDashboardStats(startDate, endDate, true);
  };

  // ** Don't render on mobile
  if (isMobile) return null;

  return (
    <div style={{ width: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
      {/* Shared max-width container for dashboard and attendance */}
      <div style={{ width: '100%', maxWidth: 1200, margin: '0 auto' }}>
        <div className="dashboard-container" style={{
          background: '#fff',
          borderRadius: 10,
          border: '1px solid #e8e8f0',
          boxShadow: '0 2px 12px rgba(0,0,0,0.08)',
          padding: '56px 48px', // Increased internal padding for more breathing room
          position: 'relative',
          marginBottom: 16,
        }}>
          {/* Header - Enhanced Layout */}
          <div style={{
            display: 'flex',
            alignItems: 'flex-start',
            justifyContent: 'space-between',
            marginBottom: 24,
            gap: 28,
            paddingBottom: 0,
          }}>
            {/* Title Section */}
            <div style={{ flex: 1 }}>
              <Text style={{
                fontSize: 26,
                fontWeight: 700,
                color: '#111827',
                lineHeight: 1.2,
                display: 'block',
                marginBottom: 8,
              }}>
                Dashboard Overview
              </Text>
              <Text style={{
                fontSize: 15,
                color: '#6b7280',
                fontWeight: 400,
                lineHeight: 1.4,
              }}>
                Real-time workforce insights
              </Text>
            </div>
            {/* Controls + Auto-refresh */}
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'flex-end',
              gap: 6,
              minWidth: 320,
              marginRight: 16,
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                <RangePicker
                  allowClear={false}
                  value={range}
                  onChange={(dates) => setRange(dates as [Dayjs, Dayjs])}
                  variant="filled"
                  style={{
                    minWidth: 180,
                    height: 42,
                    borderRadius: 8,
                    border: 'none',
                    fontSize: 14,
                    background: '#f5f5f5',
                  }}
                  placeholder={['Start Date', 'End Date']}
                  format="MM/DD/YYYY"
                />
                {refreshing && (
                  <Spin
                    size="small"
                    style={{ marginLeft: 6, marginRight: 2 }}
                  />
                )}
                <Tooltip title="Refresh data">
                  <div
                    style={{
                      width: 42,
                      height: 42,
                      borderRadius: 8,
                      border: 'none',
                      background: '#f5f5f5',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                    }}
                    onClick={handleRefresh}
                  >
                    <ReloadOutlined
                      className={refreshing ? 'spin' : ''}
                      style={{ fontSize: 17, color: '#6b7280' }}
                    />
                  </div>
                </Tooltip>
              </div>
              {/* Subtle auto-refresh info */}
              <span style={{
                fontSize: 12,
                color: '#9ca3af',
                marginTop: 2,
                display: 'flex',
                alignItems: 'center',
                gap: 5,
                fontWeight: 400,
                letterSpacing: 0.1,
                marginLeft: 8,
              }}>
                <span style={{
                  width: 6,
                  height: 6,
                  borderRadius: '50%',
                  background: '#10b981',
                  display: 'inline-block',
                  marginRight: 4,
                }} />
                Auto-refresh every 5 minutes
              </span>
            </div>
          </div>

          {/* Stats Grid - 4 columns */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(4, 1fr)',
            gap: 40,
            width: '100%',
            marginBottom: 0,
          }}>
            {statCards.map((card) => (
              <StatCard key={card.key} card={card} stats={stats} loading={loading} />
            ))}
          </div>
        </div>
        {/* The attendance block below should also use maxWidth: 1200 and be centered for alignment */}
      </div>
      {/* Add pulse animation */}
      <style>{`
        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }
      `}</style>
    </div>
  );
};

export default DashboardCard;
