import React, { Suspense, memo } from 'react';
import { Helmet } from 'react-helmet-async';
import { useAuthStore } from '@/store/authStore';
import DashboardCard from './DashboardCard';

// ** Components
const OnsiteDashboard = React.lazy(() => import('./OnsiteDashboard'));

// ** Loading Component
const DashboardLoader = memo(() => (
  <div className="d-flex justify-content-center align-items-center p-4">
    <div className="spinner-border text-primary" role="status">
      <span className="visually-hidden">Loading...</span>
    </div>
  </div>
));

DashboardLoader.displayName = 'DashboardLoader';

// ** Error Boundary Component
class DashboardErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): { hasError: boolean } {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Dashboard Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="d-flex justify-content-center align-items-center p-4">
          <div className="text-center">
            <h5 className="text-danger mb-3">Something went wrong</h5>
            <button
              className="btn btn-primary btn-sm"
              onClick={() => this.setState({ hasError: false })}
            >
              Try Again
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

const Dashboard = memo(() => {
  const { user } = useAuthStore((s) => s);
  const currentLayout = localStorage.getItem('vea-admin-layout') || 'app';

  return (
    <div className="dashboard-container">
      <Helmet>
        <title>VEA Timeclock - Dashboard</title>
        <meta name="description" content="Real-time employee attendance dashboard" />
      </Helmet>

      <div className="row p-0 mb-4">
        <div className="col-12">
          <LayoutSwitcher currentLayout={currentLayout} />
        </div>
      </div>

      <div className="row p-0 mb-4">
        <div className="col-12">
          <DashboardCard />
        </div>
      </div>

      <div className="row p-0">
        <div className="col-lg-6 col-md-12">
          <div className="shadow-sm p-3 rounded-3 bg-white">
            <DashboardErrorBoundary>
              <Suspense fallback={<DashboardLoader />}>
                <OnsiteDashboard />
              </Suspense>
            </DashboardErrorBoundary>
          </div>
        </div>
      </div>
    </div>
  );
});

Dashboard.displayName = 'Dashboard';

export default Dashboard;
