import React, { useState } from 'react';
import {
  Button,
  Space,
  Row,
  Col,
  Typography,
  Empty,
  Input
} from 'antd';
import {
  UnorderedListOutlined,
  AppstoreOutlined,
  FilterOutlined,
  SortAscendingOutlined,
  PlusOutlined,
  SearchOutlined
} from '@ant-design/icons';
import { FiUsers } from 'react-icons/fi';
import { PRIMARY_COLOR } from '@/utils/consts';
import { BlankContainer } from '@/components';


const { Text } = Typography;

const EmployeeTemplate: React.FC = () => {
  const [viewType, setViewType] = useState<'list' | 'card'>('list');
  const [searchText, setSearchText] = useState('');

  return (
    <BlankContainer
      title="Employee Management"
      subtitle="Manage your team members and their information"
      padding="none"
      headerActions={
        <Space size="small">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            style={{ backgroundColor: PRIMARY_COLOR }}
          >
            Add Employee
          </Button>
          <Button
            type={viewType === 'list' ? 'primary' : 'default'}
            icon={<UnorderedListOutlined />}
            onClick={() => setViewType('list')}
            className={`view-toggle-btn ${viewType === 'list' ? 'active' : ''}`}
            style={{
              backgroundColor: viewType === 'list' ? PRIMARY_COLOR : 'transparent',
              borderColor: viewType === 'list' ? PRIMARY_COLOR : '#d9d9d9',
            }}
          >
            List View
          </Button>
          <Button
            type={viewType === 'card' ? 'primary' : 'default'}
            icon={<AppstoreOutlined />}
            onClick={() => setViewType('card')}
            className={`view-toggle-btn ${viewType === 'card' ? 'active' : ''}`}
            style={{
              backgroundColor: viewType === 'card' ? PRIMARY_COLOR : 'transparent',
              borderColor: viewType === 'card' ? PRIMARY_COLOR : '#d9d9d9',
            }}
          >
            Card View
          </Button>
        </Space>
      }
    >
      {/* Stats and Search Section */}
      <div className="stats-section search-filter-section" style={{
        padding: '16px 20px',
        borderBottom: '1px solid rgba(240, 240, 240, 0.8)',
        background: 'rgba(250, 251, 252, 0.5)'
      }}>
        <Row justify="space-between" align="middle">
          <Col>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '10px',
              padding: '8px 12px',
              backgroundColor: 'rgba(46, 100, 84, 0.05)',
              borderRadius: '8px',
              border: '1px solid rgba(46, 100, 84, 0.1)'
            }}>
              <div style={{
                width: '32px',
                height: '32px',
                borderRadius: '6px',
                backgroundColor: PRIMARY_COLOR,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <FiUsers size={16} color="white" />
              </div>
              <div>
                <Text style={{
                  fontSize: '13px',
                  color: '#64748b',
                  display: 'block',
                  lineHeight: '1.2'
                }}>
                  Total Employees
                </Text>
                <Text style={{
                  fontSize: '16px',
                  color: '#1a202c',
                  fontWeight: 600,
                  display: 'block',
                  lineHeight: '1.2'
                }}>
                  0 persons
                </Text>
              </div>
            </div>
          </Col>
          <Col>
            <Space size="middle">
              <Input.Search
                placeholder="Search employees..."
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: 320 }}
                size="middle"
                allowClear
              />
              <Button
                icon={<FilterOutlined />}
                className="filter-sort-btn"
                size="middle"
              >
                Filter
              </Button>
              <Button
                icon={<SortAscendingOutlined />}
                className="filter-sort-btn"
                size="middle"
              >
                Sort
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      {/* Content Area */}
      <div style={{
        padding: '32px 20px',
        minHeight: '300px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            <div>
              <Text type="secondary" style={{ fontSize: '16px', display: 'block', marginBottom: '8px' }}>
                No employees found
              </Text>
              <Text type="secondary" style={{ fontSize: '14px' }}>
                Start by adding your first team member
              </Text>
            </div>
          }
        >
          <Button
            type="primary"
            icon={<PlusOutlined />}
            style={{ backgroundColor: PRIMARY_COLOR }}
          >
            Add First Employee
          </Button>
        </Empty>
      </div>

      {/* Footer Section */}
      <div className="pagination-section" style={{
        padding: '12px 20px',
        borderTop: '1px solid rgba(240, 240, 240, 0.8)',
        background: 'linear-gradient(135deg, #fafbfc 0%, #ffffff 100%)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Text type="secondary" style={{
          fontSize: '13px',
          color: '#64748b',
          fontWeight: 500
        }}>
          Ready to manage your team
        </Text>
        <Space>
          <Button size="small" type="text">
            Import Employees
          </Button>
          <Button size="small" type="text">
            Export Data
          </Button>
        </Space>
      </div>
    </BlankContainer>
  );
};

export default EmployeeTemplate;
