import React, { useState } from 'react';
import {
  Typo<PERSON>,
  <PERSON><PERSON>,
  Card,
  Space,
  Table,
  Avatar,
  Tag,
  Dropdown,
  Badge,
  Pagination,
  Row,
  Col
} from 'antd';
import {
  UnorderedListOutlined,
  AppstoreOutlined,
  FilterOutlined,
  SortAscendingOutlined,
  MoreOutlined,
  UserOutlined,
  CalendarOutlined,
  CaretUpOutlined,
  CaretDownOutlined
} from '@ant-design/icons';
import { FiUsers } from 'react-icons/fi';
import { PRIMARY_COLOR } from '@/utils/consts';
import { BlankContainer } from '@/components';


const { Title, Text } = Typography;

// Mock employee data
const mockEmployees = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    payroll: '121044A413',
    department: 'Finance',
    role: 'Sr. Accountant',
    joiningDate: 'Feb 23, 2025',
    contractType: 'Full-time',
    avatar: 'https://images.unsplash.com/photo-*************-2616b9c5e8e1?w=150&h=150&fit=crop&crop=face'
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    payroll: '*********',
    department: 'Engineer',
    role: 'Lead Back End Dev',
    joiningDate: 'Feb 18, 2025',
    contractType: 'Freelance',
    avatar: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
  },
  {
    id: 3,
    name: 'Leslie Alexander',
    email: '<EMAIL>',
    payroll: '18219444AJ',
    department: 'Product',
    role: 'Jr. Technical Product',
    joiningDate: 'Dec 25, 2024',
    contractType: 'Internship',
    avatar: 'https://images.unsplash.com/photo-*************-6461ffad8d80?w=150&h=150&fit=crop&crop=face'
  },
  {
    id: 4,
    name: 'Esther Howard',
    email: '<EMAIL>',
    payroll: 'MN2404B11',
    department: 'Finance',
    role: 'Lead Accountant',
    joiningDate: 'Jan 10, 2025',
    contractType: 'Part-time',
    avatar: 'https://images.unsplash.com/photo-**********-94ddf0286df2?w=150&h=150&fit=crop&crop=face'
  },
  {
    id: 5,
    name: 'Cameron Williamson',
    email: '<EMAIL>',
    payroll: 'HSA518188',
    department: 'Engineer',
    role: 'Sr. DevOps',
    joiningDate: 'Mar 30, 2025',
    contractType: 'Freelance',
    avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
  },
  {
    id: 6,
    name: 'Albert Flores',
    email: '<EMAIL>',
    payroll: 'NKA4CH700',
    department: 'Marketing',
    role: 'Jr. Digital Marketing',
    joiningDate: 'Oct 4, 2024',
    contractType: 'Part-time',
    avatar: 'https://images.unsplash.com/photo-*************-00dcc994a43e?w=150&h=150&fit=crop&crop=face'
  },
  {
    id: 7,
    name: 'Annette Black',
    email: '<EMAIL>',
    payroll: 'SJAB81742',
    department: 'Engineer',
    role: 'Jr. Front End Dev',
    joiningDate: 'Dec 19, 2024',
    contractType: 'Internship',
    avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face'
  },
  {
    id: 8,
    name: 'Darlene Robertson',
    email: '<EMAIL>',
    payroll: '717384AON',
    department: 'Marketing',
    role: 'Sr. Content Writer',
    joiningDate: 'Jan 28, 2025',
    contractType: 'Full-time',
    avatar: 'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=150&h=150&fit=crop&crop=face'
  },
  {
    id: 9,
    name: 'Dianne Russell',
    email: '<EMAIL>',
    payroll: 'JAH066661',
    department: 'Product',
    role: 'Lead Product Manager',
    joiningDate: 'Feb 12, 2025',
    contractType: 'Full-time',
    avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150&h=150&fit=crop&crop=face'
  },
  {
    id: 10,
    name: 'Arlene McCoy',
    email: '<EMAIL>',
    payroll: 'LAKE69137',
    department: 'Product',
    role: 'Sr. UI/UX Designer',
    joiningDate: 'Nov 10, 2024',
    contractType: 'Part-time',
    avatar: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face'
  }
];

// Department colors
const departmentColors = {
  Finance: '#52c41a',
  Engineer: '#1890ff',
  Product: '#722ed1',
  Marketing: '#fa8c16'
};

// Contract type colors
const contractTypeColors = {
  'Full-time': '#2E6454',
  'Freelance': '#52c41a',
  'Internship': '#722ed1',
  'Part-time': '#fa8c16'
};

const EmployeeManagement: React.FC = () => {
  const [viewType, setViewType] = useState<'list' | 'card'>('list');
  const [searchText, setSearchText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  const columns = [
    {
      title: (
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          Name
          <CaretUpOutlined style={{ fontSize: '10px', opacity: 0.5 }} />
        </div>
      ),
      dataIndex: 'name',
      key: 'name',
      sorter: true,
      render: (text: string, record: any) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <Avatar
            size={40}
            src={record.avatar}
            icon={<UserOutlined />}
            className="employee-avatar"
            style={{
              border: '2px solid #f0f0f0',
              transition: 'all 0.2s ease'
            }}
          />
          <div>
            <div style={{
              fontWeight: 600,
              color: '#303030',
              fontSize: '14px',
              lineHeight: '1.4'
            }}>
              {text}
            </div>
            <div style={{
              fontSize: '12px',
              color: '#8c8c8c',
              lineHeight: '1.3'
            }}>
              {record.email}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: (
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          Payroll
          <CaretUpOutlined style={{ fontSize: '10px', opacity: 0.5 }} />
        </div>
      ),
      dataIndex: 'payroll',
      key: 'payroll',
      sorter: true,
      render: (text: string) => (
        <Text style={{
          fontFamily: 'SF Mono, Monaco, monospace',
          fontSize: '13px',
          fontWeight: 500,
          color: '#495057',
          backgroundColor: '#f8f9fa',
          padding: '4px 8px',
          borderRadius: '4px',
          border: '1px solid #e9ecef'
        }}>
          {text}
        </Text>
      ),
    },
    {
      title: (
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          Department
          <CaretUpOutlined style={{ fontSize: '10px', opacity: 0.5 }} />
        </div>
      ),
      dataIndex: 'department',
      key: 'department',
      sorter: true,
      render: (department: string) => (
        <div className="department-indicator">
          <div
            className="department-dot"
            style={{
              backgroundColor: departmentColors[department],
            }}
          />
          <Text style={{ fontSize: '13px', fontWeight: 500 }}>{department}</Text>
        </div>
      ),
    },
    {
      title: (
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          Role
          <CaretUpOutlined style={{ fontSize: '10px', opacity: 0.5 }} />
        </div>
      ),
      dataIndex: 'role',
      key: 'role',
      sorter: true,
      render: (text: string) => (
        <Text style={{
          fontSize: '13px',
          fontWeight: 500,
          color: '#495057'
        }}>
          {text}
        </Text>
      ),
    },
    {
      title: (
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          Joining Date
          <CaretUpOutlined style={{ fontSize: '10px', opacity: 0.5 }} />
        </div>
      ),
      dataIndex: 'joiningDate',
      key: 'joiningDate',
      sorter: true,
      render: (date: string) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
          <CalendarOutlined style={{
            color: '#2E6454',
            fontSize: '12px',
            padding: '2px',
            backgroundColor: 'rgba(46, 100, 84, 0.1)',
            borderRadius: '3px'
          }} />
          <Text style={{
            fontSize: '13px',
            fontWeight: 500,
            color: '#495057'
          }}>
            {date}
          </Text>
        </div>
      ),
    },
    {
      title: (
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          Contract Type
          <CaretUpOutlined style={{ fontSize: '10px', opacity: 0.5 }} />
        </div>
      ),
      dataIndex: 'contractType',
      key: 'contractType',
      sorter: true,
      render: (type: string) => (
        <Tag
          color={contractTypeColors[type]}
          className="contract-badge"
        >
          {type}
        </Tag>
      ),
    },
    {
      title: 'Action',
      key: 'action',
      width: 80,
      align: 'center' as const,
      render: () => (
        <Dropdown
          menu={{
            items: [
              {
                key: '1',
                label: 'View Details',
                icon: <UserOutlined style={{ fontSize: '12px' }} />
              },
              {
                key: '2',
                label: 'Edit',
                icon: <CaretUpOutlined style={{ fontSize: '12px' }} />
              },
              {
                key: '3',
                label: 'Delete',
                icon: <CaretDownOutlined style={{ fontSize: '12px' }} />
              },
            ],
          }}
          trigger={['click']}
          placement="bottomRight"
        >
          <Button
            type="text"
            icon={<MoreOutlined />}
            className="action-menu-btn"
            style={{
              color: '#8c8c8c',
              width: '32px',
              height: '32px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          />
        </Dropdown>
      ),
    },
  ];

  return (
    <BlankContainer
      title="Employee"
      subtitle="View and manage employee"
      padding="none"
      headerActions={
        <Space size="small">
          <Button
            type={viewType === 'list' ? 'primary' : 'default'}
            icon={<UnorderedListOutlined />}
            onClick={() => setViewType('list')}
            className={`view-toggle-btn ${viewType === 'list' ? 'active' : ''}`}
            style={{
              backgroundColor: viewType === 'list' ? PRIMARY_COLOR : 'transparent',
              borderColor: viewType === 'list' ? PRIMARY_COLOR : '#d9d9d9',
            }}
          >
            List View
          </Button>
          <Button
            type={viewType === 'card' ? 'primary' : 'default'}
            icon={<AppstoreOutlined />}
            onClick={() => setViewType('card')}
            className={`view-toggle-btn ${viewType === 'card' ? 'active' : ''}`}
            style={{
              backgroundColor: viewType === 'card' ? PRIMARY_COLOR : 'transparent',
              borderColor: viewType === 'card' ? PRIMARY_COLOR : '#d9d9d9',
            }}
          >
            Card View
          </Button>
        </Space>
      }
    >

      {/* Stats and Search Section */}
      <div className="stats-section search-filter-section" style={{
        padding: '16px 20px',
        borderBottom: '1px solid rgba(240, 240, 240, 0.8)',
        background: 'rgba(250, 251, 252, 0.5)'
      }}>
        <Row justify="space-between" align="middle">
          <Col>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '10px',
              padding: '8px 12px',
              backgroundColor: 'rgba(46, 100, 84, 0.05)',
              borderRadius: '8px',
              border: '1px solid rgba(46, 100, 84, 0.1)'
            }}>
              <div style={{
                width: '32px',
                height: '32px',
                borderRadius: '6px',
                backgroundColor: PRIMARY_COLOR,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <FiUsers size={16} color="white" />
              </div>
              <div>
                <Text style={{
                  fontSize: '13px',
                  color: '#64748b',
                  display: 'block',
                  lineHeight: '1.2'
                }}>
                  Total Employee
                </Text>
                <Text style={{
                  fontSize: '16px',
                  color: '#1a202c',
                  fontWeight: 600,
                  display: 'block',
                  lineHeight: '1.2'
                }}>
                  1,285 persons
                </Text>
              </div>
            </div>
          </Col>
          <Col>
            <Space size="middle">
              <ModernSearch
                placeholder="Search payroll or name"
                value={searchText}
                onChange={setSearchText}
                style={{ width: 320 }}
                size="middle"
              />
              <Button
                icon={<FilterOutlined />}
                className="filter-sort-btn"
                size="middle"
              >
                Filter
              </Button>
              <Button
                icon={<SortAscendingOutlined />}
                className="filter-sort-btn"
                size="middle"
              >
                Sort
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      {/* Table Section */}
      <div style={{ padding: '0' }}>
        <Table
          columns={columns}
          dataSource={mockEmployees}
          pagination={false}
          rowKey="id"
          className="employee-table"
          style={{ background: 'white' }}
          scroll={{ x: 1000 }}
          size="middle"
        />
      </div>

      {/* Pagination Section */}
      <div className="pagination-section" style={{
        padding: '12px 20px',
        borderTop: '1px solid rgba(240, 240, 240, 0.8)',
        background: 'linear-gradient(135deg, #fafbfc 0%, #ffffff 100%)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Text type="secondary" style={{
          fontSize: '13px',
          color: '#64748b',
          fontWeight: 500
        }}>
          Showing <strong>1</strong> to <strong>10</strong> of <strong>100</strong> entries
        </Text>
        <Pagination
          current={currentPage}
          total={100}
          pageSize={pageSize}
          onChange={setCurrentPage}
          showSizeChanger={false}
          size="default"
          showQuickJumper
          showTotal={(total, range) =>
            `${range[0]}-${range[1]} of ${total} items`
          }
        />
      </div>
    </BlankContainer>
  );
};

export default EmployeeManagement;
