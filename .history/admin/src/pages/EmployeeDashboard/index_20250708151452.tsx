import React from 'react';
import { useLocation } from 'react-router';
import { AppV2Layout } from '@/layouts/appv2';
import EmployeeTemplate from './EmployeeTemplate';
import EmployeeManagement from './EmployeeManagement';
import DemoPage from './DemoPage';

const EmployeeDashboard: React.FC = () => {
  const location = useLocation();

  // Show different content based on the route
  const renderContent = () => {
    if (location.pathname === '/employee-dashboard/employee') {
      return <EmployeeTemplate />;
    }

    if (location.pathname === '/employee-dashboard/employee-demo') {
      return <EmployeeManagement />;
    }

    // Default to demo page for other routes
    return <DemoPage />;
  };

  return (
    <AppV2Layout>
      {renderContent()}
    </AppV2Layout>
  );
};

export default EmployeeDashboard;
