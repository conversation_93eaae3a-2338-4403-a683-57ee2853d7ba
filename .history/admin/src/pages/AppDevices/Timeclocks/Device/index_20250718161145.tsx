import React from 'react';

// ** Antd Components
import {
  <PERSON><PERSON>,
  <PERSON>,
  Typography,
  Button
} from 'antd';

// ** Icons
import {
  EyeOutlined,
  HistoryOutlined
} from '@ant-design/icons';

// ** Store
import { useAuthStore } from '@/store/authStore';
import { useModalStore } from '@/store/modalStore';

// ** Hooks
import useFeedback from '@/hooks/useFeedback';

// ** React Query
import { useQuery, useQueryClient } from '@tanstack/react-query';

// ** API
import { api } from '@/utils/api';

// ** Components
import ViewDevice from './ViewDevice';
import ViewActivity from './ViewActivity';
import EditDevice from './EditDevice';

const { Text } = Typography;

interface DeviceProps {
  mode: 'add' | 'view' | 'edit';
  deviceId?: number;
  onSuccess?: () => void;
}

const Device: React.FC<DeviceProps> = ({ mode, deviceId, onSuccess }) => {
  const [currentMode, setCurrentMode] = React.useState<'view' | 'edit'>(mode === 'add' ? 'edit' : 'view');
  const [activeTab, setActiveTab] = React.useState('device');

  // ** Store
  const { user } = useAuthStore();
  const { hideModal } = useModalStore();
  const queryClient = useQueryClient();

  // ** Hooks
  const { Ntf } = useFeedback();

  const account_id = user?.account?.id;
  const user_id = user?.id;

  // ** Fetch device data for view/edit mode
  const { data: deviceData, isLoading: deviceLoading, error: deviceError } = useQuery({
    queryKey: ['device-data', deviceId, account_id],
    queryFn: async () => {
      if ((mode === 'view' || mode === 'edit') && deviceId && account_id) {
        const res = await api('GetDevice', { id: deviceId, account_id }, 'POST');

        // Check if the response indicates an error
        if (res?.status === 'error') {
          throw new Error(res.message || 'Failed to fetch device data');
        }

        // Ensure we return the data or throw if it's missing
        if (!res?.data) {
          throw new Error('Device data not found');
        }

        return res.data;
      }
      return null;
    },
    enabled: (mode === 'view' || mode === 'edit') && !!deviceId && !!account_id,
    retry: (failureCount, error: any) => {
      // Don't retry on 404 errors
      if (error?.message?.includes('not found') || error?.message?.includes('access denied')) {
        return false;
      }
      return failureCount < 2;
    }
  });

  if (deviceLoading) {
    return (
      <div className="text-center py-5">
        <Spin size="large" />
        <div className="mt-3">
          <Text type="secondary">Loading device data...</Text>
        </div>
      </div>
    );
  }

  // Handle error state
  if (deviceError) {
    return (
      <div className="text-center py-5">
        <div className="mb-3">
          <Text type="danger" style={{ fontSize: '16px' }}>
            Failed to load device data
          </Text>
        </div>
        <div className="mb-3">
          <Text type="secondary">
            {deviceError?.message || 'An unexpected error occurred'}
          </Text>
        </div>
        <Button
          type="primary"
          onClick={() => queryClient.invalidateQueries({ queryKey: ['device-data', deviceId, account_id] })}
        >
          Try Again
        </Button>
      </div>
    );
  }

  const commonProps = {
    deviceData,
    user,
    account_id,
    user_id,
    deviceId,
    mode,
    currentMode,
    setCurrentMode,
    hideModal,
    queryClient,
    Ntf,
    onSuccess
  };

  return (
    <div className="px-4 py-2">
      <style>
        {`
          .fw-bold-labels .ant-form-item-label > label {
            font-weight: 600 !important;
          }
          .ant-tabs-tab-active .ant-tabs-tab-btn {
            font-weight: 600 !important;
          }
        `}
      </style>

      {/* Add mode - no tabs, just the form */}
      {mode === 'add' && <EditDevice {...commonProps} />}

      {/* View/Edit modes - use tabs */}
      {mode !== 'add' && (
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'device',
              label: (
                <span>
                  <EyeOutlined className="me-2" />
                  View Device
                </span>
              ),
              children: currentMode === 'view' ?
                <ViewDevice {...commonProps} /> :
                <EditDevice {...commonProps} />
            },
            {
              key: 'activity',
              label: (
                <span>
                  <HistoryOutlined className="me-2" />
                  View Activity
                </span>
              ),
              children: <ViewActivity {...commonProps} />
            }
          ]}
        />
      )}
    </div>
  );
};

export default Device; 
