import React from 'react';

// ** Antd Components
import {
  Button,
  Switch,
  Typography,
  Tag,
  Row,
  Col,
  Alert,
  Progress,
  Space
} from 'antd';

// ** Icons
import {
  EditOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';

// ** API
import { api } from '@/utils/api';
import dayjs from '@/utils/dayjs';

// ** Utils
import TimeAgo from 'react-timeago';

const { Title, Text } = Typography;

interface ViewDeviceProps {
  deviceData: any;
  user: any;
  account_id: any;
  deviceId: any;
  setCurrentMode: (mode: 'view' | 'edit') => void;
  queryClient: any;
  Ntf: any;
}

const ViewDevice: React.FC<ViewDeviceProps> = ({
  deviceData,
  user,
  account_id,
  deviceId,
  setCurrentMode,
  queryClient,
  Ntf
}) => {
  const [activeToggleLoading, setActiveToggleLoading] = React.useState(false);

  const isDeviceInactive = deviceData && !deviceData.active;

  // Mock data for enhanced display
  const batteryLevel = 85;
  const lastActivity = deviceData?.last_event || dayjs().toISOString();
  const firmwareVersion = "iOS 17.1.2";
  const connectionStatus = deviceData?.active ? 'Connected' : 'Disconnected';
  const uptimeHours = 142;

  // ** Handle active toggle
  const handleActiveToggle = async (checked: boolean) => {
    if (deviceId) {
      setActiveToggleLoading(true);
      try {
        const res = await api('PatchDeviceStatus', {
          id: deviceId,
          field: 'active',
          value: checked
        }, 'POST');

        if (res?.status === 'success') {
          // Update React Query cache for device list
          queryClient.setQueryData(
            ['timeclock-devices', account_id],
            (oldData: any[]) => {
              if (!oldData) return oldData;
              return oldData.map(device =>
                device.id === deviceId
                  ? { ...device, active: checked }
                  : device
              );
            }
          );

          // Update React Query cache for device details
          queryClient.setQueryData(
            ['device-data', deviceId, account_id],
            (oldData: any) => {
              if (!oldData) return oldData;
              return { ...oldData, active: checked };
            }
          );

          Ntf('success', 'Success', `Device ${checked ? 'activated' : 'deactivated'} successfully!`);
        } else {
          Ntf('error', 'Error', res?.message || 'Failed to update device status');
        }
      } catch (error) {
        console.error('[ActiveToggle] Error:', error);
        Ntf('error', 'Error', 'An unexpected error occurred');
      } finally {
        setActiveToggleLoading(false);
      }
    }
  };

  return (
    <div>
      {/* Clean Header */}
      <div className="d-flex justify-content-between align-items-center mb-4 pb-3 border-bottom">
        <div className="d-flex align-items-center gap-3">
          <Title level={4} className="mb-0">
            {deviceData?.name || (deviceData?.is_inactivated ? 'Inactivated Device' : 'Unknown Device')}
          </Title>
          <Switch
            checked={deviceData?.active}
            loading={activeToggleLoading}
            onChange={handleActiveToggle}
            size="small"
          />
        </div>

        <Button
          color="default"
          variant="filled"
          icon={<EditOutlined />}
          onClick={() => setCurrentMode('edit')}
          size="small"
        >
          Edit
        </Button>
      </div>

      {/* Simple Information Layout */}
      <Row gutter={[24, 16]}>
        <Col xs={24} sm={12}>
          <div className="mb-4">
            <Title level={5} className="mb-3">Device Information</Title>

            <div className="mb-3">
              <Text type="secondary" className="small d-block mb-1">Account</Text>
              <Text>{user?.account?.company_name || 'Unknown'}</Text>
            </div>

            <div className="mb-3">
              <Text type="secondary" className="small d-block mb-1">Device Number</Text>
              <Tag bordered={false} color="blue">#{deviceData?.device_no || 'Not set'}</Tag>
            </div>

            <div className="mb-3">
              <Text type="secondary" className="small d-block mb-1">Brand</Text>
              <Text>{deviceData?.brand || 'Not specified'}</Text>
            </div>

            <div className="mb-3">
              <Text type="secondary" className="small d-block mb-1">Model</Text>
              <Text>{deviceData?.model || 'Not specified'}</Text>
            </div>

            <div className="mb-3">
              <Text type="secondary" className="small d-block mb-1">Serial Number</Text>
              <Tag bordered={false} color="geekblue">
                {deviceData?.serial_number || 'Not set'}
              </Tag>
            </div>

            <div className="mb-3">
              <Text type="secondary" className="small d-block mb-1">Status</Text>
              <div className="d-flex align-items-center gap-2">
                <Progress
                  type="circle"
                  size={32}
                  percent={batteryLevel}
                  strokeColor={batteryLevel > 20 ? '#52c41a' : '#ff4d4f'}
                  format={() => ''}
                />
                <div>
                  <div className="small">{batteryLevel}% Battery</div>
                  <div className="small text-muted">{uptimeHours}h Uptime</div>
                </div>
              </div>
            </div>
          </div>
        </Col>

        <Col xs={24} sm={12}>
          <div className="mb-4">
            <Title level={5} className="mb-3">Assignment</Title>

            <div className="mb-3">
              <Text type="secondary" className="small d-block mb-1">Client</Text>
              {deviceData?.client_name ? (
                <div>
                  <Text strong>{deviceData.client_name}</Text>
                  <Tag bordered={false} color="orange" className="ms-2">Active</Tag>
                </div>
              ) : (
                <div>
                  <Text type="secondary">Not assigned</Text>
                  <Button
                    type="link"
                    size="small"
                    className="p-0 ms-2"
                    onClick={() => setCurrentMode('edit')}
                  >
                    Assign
                  </Button>
                </div>
              )}
            </div>

            <div className="mb-3">
              <Text type="secondary" className="small d-block mb-1">Location</Text>
              {deviceData?.location_name ? (
                <div>
                  <Text>{deviceData.location_name}</Text>
                  <Tag bordered={false} color="cyan" className="ms-2">#{deviceData.location_id}</Tag>
                </div>
              ) : (
                <div>
                  <Text type="secondary">Not assigned</Text>
                  <Button
                    type="link"
                    size="small"
                    className="p-0 ms-2"
                    onClick={() => setCurrentMode('edit')}
                  >
                    Assign
                  </Button>
                </div>
              )}
            </div>

            <div className="mb-3">
              <Text type="secondary" className="small d-block mb-1">Departments</Text>
              {deviceData?.departments ? (
                <Tag bordered={false} color="purple">
                  {deviceData.departments}
                </Tag>
              ) : (
                <Text type="secondary">None assigned</Text>
              )}
            </div>

            {lastActivity && (
              <div>
                <Text type="secondary" className="small d-block mb-1">Last Activity</Text>
                <Text className="small"><TimeAgo date={lastActivity} /></Text>
              </div>
            )}
          </div>
        </Col>
      </Row>

      {/* Inactive Device Alert */}
      {isDeviceInactive && (
        <Alert
          message="Device is Inactive"
          description="Employees cannot clock in with this device."
          type="warning"
          showIcon
          className="mb-0"
          action={
            <Button
              size="small"
              type="primary"
              onClick={() => handleActiveToggle(true)}
              loading={activeToggleLoading}
            >
              Activate
            </Button>
          }
        />
      )}
    </div>
  );
};

export default ViewDevice; 
