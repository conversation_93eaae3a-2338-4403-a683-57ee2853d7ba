import React from 'react';

// ** Antd Components
import {
  Form,
  Input,
  Select,
  Switch,
  Button,
  Row,
  Col,
  Typography,
  Divider,
  Alert,
  Spin,
  AutoComplete
} from 'antd';

// ** Icons
import {
  SaveOutlined,
  CloseOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  UserOutlined,
  EnvironmentOutlined,
  TeamOutlined,
  TabletOutlined,
  ExclamationCircleOutlined,
  EyeOutlined
} from '@ant-design/icons';

// ** React Query
import { useQuery } from '@tanstack/react-query';

// ** API
import { api } from '@/utils/api';

const { Title, Text } = Typography;

interface EditDeviceProps {
  deviceData: any;
  user: any;
  account_id: any;
  user_id: any;
  deviceId: any;
  mode: 'add' | 'view' | 'edit';
  currentMode: 'view' | 'edit';
  setCurrentMode: (mode: 'view' | 'edit') => void;
  hideModal: () => void;
  queryClient: any;
  Ntf: any;
  onSuccess?: () => void;
}

const EditDevice: React.FC<EditDeviceProps> = ({
  deviceData,
  user,
  account_id,
  user_id,
  deviceId,
  mode,
  currentMode,
  setCurrentMode,
  hideModal,
  queryClient,
  Ntf,
  onSuccess
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = React.useState(false);
  const [activeToggleLoading, setActiveToggleLoading] = React.useState(false);
  const [selectedClientId, setSelectedClientId] = React.useState<number | null>(null);
  const [selectedAccountId, setSelectedAccountId] = React.useState<number | null>(null);

  // ** Fetch clients
  const { data: clientsData, isLoading: clientsLoading } = useQuery({
    queryKey: ['clients-for-devices', account_id],
    queryFn: async () => {
      const res = await api('GetClients', { account_id, active: true }, 'POST');
      return res?.data || [];
    },
    enabled: !!account_id
  });

  // ** Fetch next device number for new devices
  const { data: nextDeviceNumberData } = useQuery({
    queryKey: ['next-device-number', account_id],
    queryFn: async () => {
      const res = await api('GetNextDeviceNumber', { account_id }, 'POST');
      return res?.data;
    },
    enabled: !!account_id && mode === 'add'
  });

  // ** Check if user can select account (super admin only)
  const canSelectAccount = user?.role === 1 && user?.account?.id === 1;

  // ** Fetch accounts list (only for super admin)
  const { data: accountsData = [] } = useQuery({
    queryKey: ['accounts-for-devices'],
    queryFn: async () => {
      const response = await api('GetSystemAccounts', {
        user_id: user?.id,
        role: user?.role,
        account_id: user?.account?.id
      }, 'POST');
      return response?.data || [];
    },
    enabled: canSelectAccount,
    staleTime: 5 * 60 * 1000 // 5 minutes
  });

  // ** Fetch locations based on selected client
  const { data: locationsData, isLoading: locationsLoading } = useQuery({
    queryKey: ['client-locations', selectedClientId, account_id],
    queryFn: async () => {
      if (selectedClientId) {
        const res = await api('GetClientLocations', { client_id: selectedClientId, account_id }, 'POST');
        return res?.data || [];
      }
      return [];
    },
    enabled: !!selectedClientId && !!account_id
  });

  // ** Fetch departments based on selected client
  const { data: departmentsData, isLoading: departmentsLoading } = useQuery({
    queryKey: ['client-departments', selectedClientId, account_id],
    queryFn: async () => {
      if (selectedClientId) {
        const res = await api('GetClientDepartments', { client_id: selectedClientId, account_id }, 'POST');
        return res?.data || [];
      }
      return [];
    },
    enabled: !!selectedClientId && !!account_id
  });

  // ** Set form values when device data is loaded
  React.useEffect(() => {
    if (deviceData && (mode === 'view' || mode === 'edit')) {
      setSelectedClientId(deviceData.client_id || null);
      setSelectedAccountId(deviceData.account_id || account_id);

      form.setFieldsValue({
        device_name: deviceData.name,
        device_code: deviceData.device_code,
        device_no: deviceData.device_no || deviceData.suggested_device_no,
        brand: deviceData.brand,
        client_id: deviceData.client_id,
        location_id: deviceData.location_id,
        departments: deviceData.departments,
        serial_number: deviceData.serial_number,
        model: deviceData.model,
        account_id: deviceData.account_id || account_id,
      });
    }
  }, [deviceData, mode, form, account_id]);

  // ** Set next device number for new devices
  React.useEffect(() => {
    if (mode === 'add' && nextDeviceNumberData?.next_device_no) {
      form.setFieldsValue({
        device_no: nextDeviceNumberData.next_device_no,
        account_id: account_id
      });
      setSelectedAccountId(account_id);
    }
  }, [nextDeviceNumberData, mode, form, account_id]);

  // ** Handle active toggle
  const handleActiveToggle = async (checked: boolean) => {
    if ((mode === 'view' || mode === 'edit') && deviceId) {
      setActiveToggleLoading(true);
      try {
        const res = await api('PatchDeviceStatus', {
          id: deviceId,
          field: 'active',
          value: checked
        }, 'POST');

        if (res?.status === 'success') {
          queryClient.setQueryData(
            ['timeclock-devices', account_id],
            (oldData: any[]) => {
              if (!oldData) return oldData;
              return oldData.map(device =>
                device.id === deviceId
                  ? { ...device, active: checked }
                  : device
              );
            }
          );

          queryClient.setQueryData(
            ['device-data', deviceId, account_id],
            (oldData: any) => {
              if (!oldData) return oldData;
              return { ...oldData, active: checked };
            }
          );

          Ntf('success', 'Success', `Device ${checked ? 'activated' : 'deactivated'} successfully!`);
        } else {
          Ntf('error', 'Error', res?.message || 'Failed to update device status');
        }
      } catch (error) {
        console.error('[ActiveToggle] Error:', error);
        Ntf('error', 'Error', 'An unexpected error occurred');
      } finally {
        setActiveToggleLoading(false);
      }
    }
  };

  // ** Handle client selection
  const handleClientChange = (clientId: number | null) => {
    setSelectedClientId(clientId);
    form.setFieldsValue({
      location_id: undefined,
      departments: undefined
    });
  };

  // ** Form submission
  const handleSubmit = async (values: any) => {
    setLoading(true);

    try {
      if ((mode === 'view' || mode === 'edit') && deviceId) {
        const formData = {
          id: deviceId,
          account_id: canSelectAccount ? (values.account_id || selectedAccountId) : account_id,
          user_id,
          device_number: values.device_no,
          device_id: values.device_code,
          name: values.device_name,
          client_id: values.client_id || null,
          location_id: values.location_id || null,
          departments: values.departments || null,
          brand: values.brand || null,
          serial_number: values.serial_number || null,
          model: values.model || null
        };

        const res = await api('EditDevice', formData, 'POST');

        if (res?.status === 'success') {
          queryClient.setQueryData(
            ['timeclock-devices', account_id],
            (oldData: any[]) => {
              if (!oldData) return oldData;
              return oldData.map(device =>
                device.id === deviceId
                  ? {
                    ...device,
                    name: values.device_name,
                    device_code: values.device_code,
                    device_no: values.device_no,
                    brand: values.brand,
                    client_id: values.client_id,
                    location_id: values.location_id,
                    departments: values.departments,
                    serial_number: values.serial_number,
                    model: values.model
                  }
                  : device
              );
            }
          );

          queryClient.setQueryData(
            ['device-data', deviceId, account_id],
            (oldData: any) => ({
              ...oldData,
              name: values.device_name,
              device_code: values.device_code,
              device_no: values.device_no,
              brand: values.brand,
              client_id: values.client_id,
              location_id: values.location_id,
              departments: values.departments,
              serial_number: values.serial_number,
              model: values.model
            })
          );

          Ntf('success', 'Success', 'Timeclock device updated successfully!');
          onSuccess?.();
          hideModal();
        } else {
          Ntf('error', 'Error', res?.message || 'Failed to update device');
        }
      } else {
        const formData = {
          db_event: 'add',
          account_id,
          user_id,
          device_id: values.device_code,
          device_name: values.device_name,
          client_id: values.client_id || null,
          location_id: values.location_id || null,
          departments: values.departments || null,
          device_no: values.device_no,
          brand: values.brand || null,
          serial_number: values.serial_number || null,
          model: values.model || null,
          active: true
        };

        const res = await api('ManageDevice', formData, 'POST');

        if (res?.status === 'success') {
          queryClient.invalidateQueries({ queryKey: ['timeclock-devices'] });

          Ntf('success', 'Success', 'Timeclock device added successfully!');
          onSuccess?.();
          hideModal();
        } else {
          Ntf('error', 'Error', res?.message || 'Failed to add device');
        }
      }
    } catch (error) {
      console.error('[DeviceForm] Error:', error);
      Ntf('error', 'Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  // ** Handle Delete
  const handleDelete = async () => {
    if ((mode === 'view' || mode === 'edit') && deviceId) {
      setLoading(true);
      try {
        const res = await api('ManageDevice', {
          db_event: 'delete',
          id: deviceId,
          account_id,
          user_id
        }, 'POST');

        if (res?.status === 'success') {
          queryClient.setQueryData(
            ['timeclock-devices', account_id],
            (oldData: any[]) => {
              if (!oldData) return oldData;
              return oldData.filter(device => device.id !== deviceId);
            }
          );

          Ntf('success', 'Success', 'Device deleted successfully!');
          onSuccess?.();
          hideModal();
        } else {
          Ntf('error', 'Error', res?.message || 'Failed to delete device');
        }
      } catch (error) {
        console.error('[DeleteDevice] Error:', error);
        Ntf('error', 'Error', 'An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    }
  };

  // ** Enhanced department options
  const departmentOptions = React.useMemo(() => {
    if (!departmentsData) return [];

    return departmentsData.map((dept: any) => {
      const locationText = dept.location_name || 'No Location';

      return {
        label: `${dept.department_name} - ${locationText}`,
        value: dept.id,
        search: `${dept.department_name} ${locationText}`.toLowerCase()
      };
    });
  }, [departmentsData]);

  // ** Enhanced location options
  const locationOptions = React.useMemo(() => {
    if (!locationsData) return [];

    return locationsData.map((location: any) => ({
      label: `${location.location_name} #${location.location_id}`,
      value: location.location_id,
      search: location.location_name?.toLowerCase() || ''
    }));
  }, [locationsData]);

  const isDeviceInactive = (mode === 'view' || mode === 'edit') && deviceData && !deviceData.active;

  return (
    <div>
      {/* Clean Header */}
      <div className="d-flex justify-content-between align-items-center mb-4 pb-3 border-bottom">
        <div className="d-flex align-items-center gap-3">
          <Title level={4} className="mb-0">
            {mode === 'add' ? 'Add New Timeclock Device' : deviceData?.name || 'Unknown Device'}
          </Title>
          {mode !== 'add' && (
            <Switch
              checked={deviceData?.active}
              loading={activeToggleLoading}
              onChange={handleActiveToggle}
              size="small"
            />
          )}
        </div>

        {mode !== 'add' && (
          <Button
            color="default"
            variant="filled"
            icon={<EyeOutlined />}
            onClick={() => setCurrentMode('view')}
            size="small"
          >
            View
          </Button>
        )}
      </div>

      {/* Inactive Device Message */}
      {isDeviceInactive && (
        <div className="text-center py-5">
          <Alert
            message="Device is Inactive"
            description="Device isn't active, employees will not be able to clock in. Activate the device to access configuration options."
            type="warning"
            icon={<ExclamationCircleOutlined />}
            showIcon
            className="mb-4"
          />

          <Button
            onClick={hideModal}
            icon={<CloseOutlined />}
          >
            Close
          </Button>
        </div>
      )}

      {/* Form */}
      {(!isDeviceInactive || mode === 'add') && (
        <>
          {/* Warning Alert */}
          {mode !== 'add' && deviceData?.device_code && (
            <Alert
              message={<Text className="small">Device ID cannot be changed once set because it is associated with timeclock records.</Text>}
              type="warning"
              showIcon
              className="mb-3"
              style={{ border: 'none' }}
            />
          )}

          <Form
            form={form}
            layout="vertical"
            variant="filled"
            onFinish={handleSubmit}
            initialValues={{
              brand: 'iPad'
            }}
          >
            {/* Basic Information */}
            <div className="mb-4">
              <Title level={5} className="text-primary mb-3">
                <InfoCircleOutlined className="me-2" />
                Basic Information
              </Title>

              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12}>
                  {canSelectAccount ? (
                    <Form.Item
                      label="Select Account"
                      name="account_id"
                      rules={[{ required: true, message: 'Please select an account' }]}
                    >
                      <Select
                        placeholder="Select account"
                        value={selectedAccountId}
                        onChange={(value) => setSelectedAccountId(value)}
                        options={accountsData.map((account: any) => ({
                          label: account.company_name,
                          value: account.id
                        }))}
                        prefix={<UserOutlined />}
                      />
                    </Form.Item>
                  ) : (
                      <Form.Item label="Account">
                        <Input
                          value={user?.account?.company_name || 'Unknown Account'}
                          disabled
                          className="text-muted"
                          prefix={<UserOutlined />}
                        />
                      </Form.Item>
                  )}
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="Device Number"
                    name="device_no"
                    rules={[{ required: true, message: 'Please enter device number' }]}
                  >
                    <Input
                      type="number"
                      placeholder={mode === 'add' ? (nextDeviceNumberData?.next_device_no?.toString() || "1") : "27"}
                      disabled={mode !== 'add' && deviceData?.device_no}
                    />
                  </Form.Item>
                  {mode !== 'add' && deviceData?.device_no && (
                    <Text type="secondary" className="small">
                      Device Number cannot be changed once set.
                    </Text>
                  )}
                  {deviceData?.is_inactivated && (
                    <Text type="warning" className="small">
                      This device is inactivated and needs to be assigned to a client.
                    </Text>
                  )}
                </Col>
              </Row>

              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="Serial Number"
                    name="serial_number"
                  >
                    <Input
                      placeholder="ABC123XYZ789"
                      disabled={mode !== 'add' && deviceData?.serial_number}
                    />
                  </Form.Item>
                  {mode !== 'add' && deviceData?.serial_number && (
                    <Text type="secondary" className="small">
                      Serial Number cannot be changed once set.
                    </Text>
                  )}
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="Device ID"
                    name="device_code"
                    rules={[{ required: true, message: 'Please enter device ID' }]}
                  >
                    <Input
                      placeholder="4fed45ebdee655d2"
                      disabled={mode !== 'add' && deviceData?.device_code}
                      className="font-monospace"
                    />
                  </Form.Item>
                  {mode !== 'add' && deviceData?.device_code && (
                    <Text type="secondary" className="small">
                      Device ID locked - associated with timeclock records.
                    </Text>
                  )}
                </Col>
              </Row>

              <Row gutter={[16, 16]}>
                <Col xs={24}>
                  <Form.Item
                    label="Time Clock Name"
                    name="device_name"
                    rules={[{ required: true, message: 'Please enter timeclock name' }]}
                  >
                    <Input placeholder="D27" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="Device Brand"
                    name="brand"
                  >
                    <AutoComplete
                      placeholder="iPad"
                      options={[
                        { value: 'iPad' },
                        { value: 'iPad Pro' },
                        { value: 'iPad Air' },
                        { value: 'iPad Mini' },
                        { value: 'Samsung Galaxy Tab' },
                        { value: 'Samsung Galaxy Tab S' },
                        { value: 'Samsung Galaxy Tab A' },
                        { value: 'Microsoft Surface' },
                        { value: 'Microsoft Surface Pro' },
                        { value: 'Microsoft Surface Go' },
                        { value: 'Amazon Fire Tablet' },
                        { value: 'Amazon Fire HD' },
                        { value: 'Lenovo Tab' },
                        { value: 'Huawei MatePad' },
                        { value: 'Google Pixel Tablet' },
                        { value: 'ASUS ZenPad' },
                        { value: 'Generic Android Tablet' },
                        { value: 'Generic Tablet' }
                      ]}
                      filterOption={(inputValue, option) =>
                        option!.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                      }
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="Device Model"
                    name="model"
                  >
                    <Input placeholder="Pro 12.9-inch (6th generation)" />
                  </Form.Item>
                </Col>
              </Row>
            </div>

            {/* Assignment & Location */}
            <div className="mb-4">
              <Title level={5} className="text-success mb-3">
                <EnvironmentOutlined className="me-2" />
                Assignment & Location
              </Title>

              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="Client Assignment"
                    name="client_id"
                  >
                    <Select
                      placeholder="Select client (optional)"
                      allowClear
                      showSearch
                      loading={clientsLoading}
                      onChange={handleClientChange}
                      filterOption={(input, option) =>
                        String(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                      }
                      options={clientsData?.map((client: any) => ({
                        label: client.name,
                        value: client.id
                      }))}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label="Location"
                    name="location_id"
                  >
                    <Select
                      placeholder={selectedClientId ? "Select a Location" : "Select client first"}
                      allowClear
                      disabled={!selectedClientId}
                      loading={locationsLoading}
                      showSearch
                      filterOption={(input, option: any) =>
                        option?.search?.includes(input.toLowerCase()) || false
                      }
                      options={locationOptions}
                      notFoundContent={
                        selectedClientId ? (
                          locationsLoading ? <Spin size="small" /> : "No locations found"
                        ) : (
                          "Please select a client first"
                        )
                      }
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    label={
                      <span>
                        <TeamOutlined className="me-1" />
                        Departments
                        <span className="badge bg-light text-muted ms-2 small">Not functional yet</span>
                      </span>
                    }
                    name="departments"
                  >
                    <Select
                      mode="multiple"
                      placeholder={selectedClientId ? "Select departments (optional)" : "Select client first"}
                      allowClear
                      disabled={!selectedClientId}
                      loading={departmentsLoading}
                      showSearch
                      filterOption={(input, option: any) =>
                        option?.search?.includes(input.toLowerCase()) || false
                      }
                      options={departmentOptions}
                      notFoundContent={
                        selectedClientId ? (
                          departmentsLoading ? <Spin size="small" /> : "No departments found"
                        ) : (
                          "Please select a client first"
                        )
                      }
                    />
                  </Form.Item>
                </Col>
              </Row>
            </div>

            <Divider />

            {/* Action Buttons */}
            <Row gutter={16} className="mb-3">
              <Col xs={24} sm={8}>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  icon={<SaveOutlined />}
                  block
                >
                  {mode === 'add' ? 'Add Device' : 'Update Device'}
                </Button>
              </Col>

              <Col xs={24} sm={8}>
                <Button
                  onClick={hideModal}
                  icon={<CloseOutlined />}
                  block
                >
                  Cancel
                </Button>
              </Col>

              {mode !== 'add' && (
                <Col xs={24} sm={8}>
                  <Button
                    danger
                    onClick={handleDelete}
                    loading={loading}
                    icon={<DeleteOutlined />}
                    block
                    disabled={!deviceData?.is_deletable}
                    title={!deviceData?.is_deletable ? 'Cannot delete device - it has associated timeclock records' : ''}
                  >
                    Delete Device
                  </Button>
                </Col>
              )}
            </Row>
          </Form>
        </>
      )}
    </div>
  );
};

export default EditDevice; 
