import React, { useState, useCallback, useMemo } from 'react';
import {
  Skeleton,
  Button,
  Badge,
  Tooltip,
  Dropdown,
  Progress,
  Tag,
  Switch,
  Breadcrumb,
  Empty,
  Divider,
  Select,
  Drawer
} from 'antd';
import { useMediaQuery } from 'react-responsive';
import { useQuery } from '@tanstack/react-query';
import { HiOutlineChevronDown } from "react-icons/hi";
import { IoAddCircleOutline } from "react-icons/io5";
import { FcTabletAndroid } from 'react-icons/fc';
import {
  ReloadOutlined,
  SettingOutlined,
  PoweroffOutlined,
  CheckOutlined,
  SyncOutlined,
  HomeOutlined,
  MoreOutlined,
  MenuOutlined
} from '@ant-design/icons';

// ** Imports
import { useAuthStore } from '@/store/authStore';
import { useModalStore } from '@/store/modalStore';
import useFeedback from '@/hooks/useFeedback';
import { PageHeader } from '@/components';
import Device from './Device';
import { api } from '@/utils/api';
import TimeAgo from 'react-timeago';
import { getConnectionStatus } from './utils/timeclockHelpers';

// ** Constants
const PRIMARY_COLOR = '#1890ff';

// ** Responsive breakpoints using react-responsive
const BREAKPOINTS = {
  mobile: '(max-width: 767px)',
  tablet: '(min-width: 768px) and (max-width: 1023px)',
  desktop: '(min-width: 1024px)'
};

// ** Memoized Responsive Styles
const getResponsiveStyles = (isMobile: boolean, isTablet: boolean) => ({
  container: {
    height: '100vh',
    display: 'flex',
    flexDirection: 'column' as const
  },
  mainContent: {
    flex: 1,
    display: 'flex',
    overflow: 'hidden',
    flexDirection: isMobile ? 'column' as const : 'row' as const
  },
  sidebar: {
    width: isMobile ? '100%' : isTablet ? '280px' : '320px',
    backgroundColor: '#fafafa',
    borderRight: isMobile ? 'none' : '1px solid #f0f0f0',
    borderBottom: isMobile ? '1px solid #f0f0f0' : 'none',
    display: 'flex',
    flexDirection: 'column' as const,
    maxHeight: isMobile ? '300px' : 'auto'
  },
  deviceContainer: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column' as const,
    overflow: 'hidden',
    minHeight: isMobile ? '60vh' : 'auto'
  },
  statsBar: {
    padding: isMobile ? '12px 16px' : isTablet ? '14px 20px' : '16px 24px',
    backgroundColor: '#fff',
    borderBottom: '1px solid #f0f0f0',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: isMobile ? 'column' as const : 'row' as const,
    gap: isMobile ? '12px' : '0'
  },
  filterSection: {
    padding: isMobile ? '6px 8px' : '8px 12px',
    backgroundColor: '#fff',
    borderBottom: '1px solid #f0f0f0',
    width: '100%',
    boxSizing: 'border-box' as const
  },
  deviceTable: {
    padding: isMobile ? '8px' : isTablet ? '12px' : '16px'
  },
  mobileHeader: {
    display: isMobile ? 'flex' : 'none',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '12px 16px',
    backgroundColor: '#fff',
    borderBottom: '1px solid #f0f0f0'
  }
});

// ** Enhanced Device Status Function
const getEnhancedDeviceStatus = (device: any) => {
  // Check if device is explicitly marked as inactive
  if (device.active === false) {
    return {
      status: 'inactive' as const,
      text: 'Inactive',
      color: '#666666'
    };
  }

  const status = getConnectionStatus(device);

  // Map offline status to disconnected for consistency
  if (status.status === 'online' && !device.last_event) {
    return {
      status: 'disconnected' as const,
      text: 'Disconnected',
      color: '#d9534f'
    };
  }

  return status;
};

// ** Enhanced Device Table Component (Responsive)
const DeviceTable = React.memo<{
  devices: any[];
  onDeviceClick: (deviceId: number) => void;
}>(({ devices, onDeviceClick }) => {
  const isMobile = useMediaQuery({ query: BREAKPOINTS.mobile });
  const isTablet = useMediaQuery({ query: BREAKPOINTS.tablet });
  const [sortOrder, setSortOrder] = React.useState<'asc' | 'desc'>('desc');

  const sortedDevices = React.useMemo(() => {
    return [...devices].sort((a, b) => {
      const dateA = a.last_event ? new Date(a.last_event).getTime() : 0;
      const dateB = b.last_event ? new Date(b.last_event).getTime() : 0;
      return sortOrder === 'desc' ? dateB - dateA : dateA - dateB;
    });
  }, [devices, sortOrder]);

  const toggleSort = () => setSortOrder(current => current === 'desc' ? 'asc' : 'desc');

  return (
    <div style={{ padding: isMobile ? '8px' : '16px' }}>
      <div style={{
        marginBottom: '16px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexDirection: isMobile ? 'column' : 'row',
        gap: isMobile ? '8px' : '0'
      }}>
        <span style={{ fontSize: isMobile ? '14px' : '16px', fontWeight: 500 }}>
          {sortedDevices.length} devices
        </span>
        <Button
          type="text"
          size={isMobile ? 'small' : 'middle'}
          onClick={toggleSort}
          style={{ display: 'flex', alignItems: 'center', gap: '4px' }}
        >
          Sort by date
          <span style={{ fontSize: '12px' }}>
            {sortOrder === 'desc' ? '↓ Recent first' : '↑ Oldest first'}
          </span>
        </Button>
      </div>

      <div style={{
        backgroundColor: '#fff',
        borderRadius: '8px',
        border: '1px solid #f0f0f0',
        overflow: 'hidden'
      }}>
        {/* Desktop Header */}
        {!isMobile && (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            padding: '8px 16px',
            backgroundColor: '#fafafa',
            borderBottom: '1px solid #f0f0f0',
            fontSize: '12px',
            fontWeight: 500,
            color: '#666'
          }}>
            <div style={{ width: '30px', textAlign: 'center' }}>
              <Tooltip title="Has timeclock records">📊</Tooltip>
            </div>
            <div style={{ flex: 1, paddingLeft: '12px' }}>Device</div>
            <div style={{ width: isTablet ? '100px' : '120px' }}>Status</div>
            <div style={{ width: isTablet ? '80px' : '100px' }}>Battery</div>
            <div style={{ width: isTablet ? '100px' : '120px' }}>Last Seen</div>
            <div style={{ width: '60px', textAlign: 'center' }}>Actions</div>
          </div>
        )}

        {sortedDevices.length === 0 ? (
          <Empty description="No devices found" style={{ padding: '40px' }} />
        ) : (
          <div>
            {sortedDevices.map((device, index) => (
              <div
                key={device.id}
                style={{
                  display: 'flex',
                  alignItems: isMobile ? 'flex-start' : 'center',
                  padding: isMobile ? '16px' : '12px 16px',
                  borderBottom: index < sortedDevices.length - 1 ? '1px solid #f0f0f0' : 'none',
                  cursor: 'pointer',
                  transition: 'background-color 0.2s',
                  flexDirection: isMobile ? 'column' : 'row',
                  gap: isMobile ? '12px' : '0'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#fafafa'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                onClick={() => onDeviceClick(device.id)}
              >
                {/* Records Check Icon */}
                {!isMobile && (
                  <div style={{ width: '30px', display: 'flex', justifyContent: 'center' }}>
                    {device.have_records && (
                      <CheckOutlined style={{ color: '#52c41a', fontSize: '14px' }} />
                    )}
                  </div>
                )}

                {/* Device Info */}
                <div style={{
                  flex: 1,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  width: isMobile ? '100%' : 'auto'
                }}>
                  <div style={{
                    width: isMobile ? '48px' : '40px',
                    height: isMobile ? '48px' : '40px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: '#f5f5f5',
                    borderRadius: '8px'
                  }}>
                    <FcTabletAndroid size={isMobile ? 28 : 24} />
                  </div>
                  <div style={{ flex: 1 }}>
                    <div style={{
                      fontWeight: 500,
                      fontSize: isMobile ? '16px' : '14px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px'
                    }}>
                      {device.name}
                      {isMobile && device.have_records && (
                        <CheckOutlined style={{ color: '#52c41a', fontSize: '14px' }} />
                      )}
                    </div>
                    <div style={{
                      fontSize: isMobile ? '13px' : '12px',
                      color: '#666',
                      marginTop: '-2px'
                    }}>
                      {device.brand}
                      {device.location && ` • ${device.location}`}
                    </div>
                  </div>
                </div>

                {/* Mobile/Desktop Status */}
                {isMobile ? (
                  // Mobile Status Row
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    width: '100%',
                    flexWrap: 'wrap',
                    gap: '12px'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                      {(() => {
                        const status = getEnhancedDeviceStatus(device);
                        return (
                          <>
                            <div style={{
                              width: '8px',
                              height: '8px',
                              borderRadius: '50%',
                              backgroundColor: status.color
                            }} />
                            <span style={{ color: status.color, fontSize: '14px' }}>
                              {status.text}
                            </span>
                          </>
                        );
                      })()}
                    </div>

                    <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                      {(() => {
                        const level = device.battery_level || 100;
                        const color = level > 50 ? '#52c41a' : level > 20 ? '#faad14' : '#ff4d4f';
                        return (
                          <>
                            <Progress
                              type="circle"
                              size={20}
                              percent={level}
                              strokeColor={color}
                              showInfo={false}
                            />
                            <span style={{ fontSize: '13px' }}>{level}%</span>
                          </>
                        );
                      })()}
                    </div>

                    <div style={{ fontSize: '13px', color: '#666' }}>
                      {device.last_event ? <TimeAgo date={device.last_event} /> : 'Never'}
                    </div>

                    <Dropdown
                      menu={{
                        items: [
                          {
                            key: 'edit',
                            label: 'Edit Device',
                            icon: <SettingOutlined />,
                            onClick: (e) => {
                              e.domEvent.stopPropagation();
                              onDeviceClick(device.id);
                            },
                          },
                          {
                            key: 'restart',
                            label: 'Restart Device',
                            icon: <ReloadOutlined />,
                          },
                          {
                            key: 'delete',
                            label: 'Delete Device',
                            icon: <PoweroffOutlined />,
                            danger: true,
                          },
                        ],
                      }}
                    >
                      <Button
                        type="text"
                        icon={<MoreOutlined />}
                        onClick={(e) => e.stopPropagation()}
                        size="small"
                      />
                    </Dropdown>
                  </div>
                ) : (
                  // Desktop Status Row
                  <>
                    <div style={{ width: isTablet ? '100px' : '120px' }}>
                      {(() => {
                        const status = getEnhancedDeviceStatus(device);
                        return (
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <div style={{
                              width: '8px',
                              height: '8px',
                              borderRadius: '50%',
                              backgroundColor: status.color
                            }} />
                            <span style={{ color: status.color, fontSize: '14px' }}>
                              {status.text}
                            </span>
                          </div>
                        );
                      })()}
                    </div>

                    <div style={{ width: isTablet ? '80px' : '100px', display: 'flex', alignItems: 'center', gap: '8px' }}>
                      {(() => {
                        const level = device.battery_level || 100;
                        const color = level > 50 ? '#52c41a' : level > 20 ? '#faad14' : '#ff4d4f';
                        return (
                          <>
                            <Progress
                              type="circle"
                              size={24}
                              percent={level}
                              strokeColor={color}
                              showInfo={false}
                            />
                            <span style={{ fontSize: '14px' }}>{level}%</span>
                          </>
                        );
                      })()}
                    </div>

                    <div style={{ width: isTablet ? '100px' : '120px' }}>
                      <div style={{ fontSize: '14px', color: '#666' }}>
                        {device.last_event ? <TimeAgo date={device.last_event} /> : 'Never'}
                      </div>
                    </div>

                    <div style={{ width: '60px', display: 'flex', justifyContent: 'center' }}>
                      <Dropdown
                        menu={{
                          items: [
                            {
                              key: 'edit',
                              label: 'Edit Device',
                              icon: <SettingOutlined />,
                              onClick: (e) => {
                                e.domEvent.stopPropagation();
                                onDeviceClick(device.id);
                              },
                            },
                            {
                              key: 'restart',
                              label: 'Restart Device',
                              icon: <ReloadOutlined />,
                            },
                            {
                              key: 'delete',
                              label: 'Delete Device',
                              icon: <PoweroffOutlined />,
                              danger: true,
                            },
                          ],
                        }}
                      >
                        <Button
                          type="text"
                          icon={<MoreOutlined />}
                          onClick={(e) => e.stopPropagation()}
                        />
                      </Dropdown>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
});

DeviceTable.displayName = 'DeviceTable';

// ** Enhanced Client List Component
const ClientList = React.memo<{
  clients: any[];
  unassignedDevices: any[];
  selectedClientId: number | null;
  onClientSelect: (clientId: number) => void;
}>(({ clients, unassignedDevices, selectedClientId, onClientSelect }) => {
  const isMobile = useMediaQuery({ query: BREAKPOINTS.mobile });

  const getClientStatusSummary = (devices: any[]) => {
    const summary: Record<string, number> = {};
    devices.forEach(device => {
      const status = getEnhancedDeviceStatus(device);
      summary[status.status] = (summary[status.status] || 0) + 1;
    });

    const parts = [];
    if (summary['active']) parts.push(`${summary['active']} active`);
    if (summary['recently_active']) parts.push(`${summary['recently_active']} recent`);
    if (summary['idle']) parts.push(`${summary['idle']} idle`);
    if (summary['disconnected']) parts.push(`${summary['disconnected']} disconnected`);
    if (summary['inactive']) parts.push(`${summary['inactive']} inactive`);

    return parts.join(' • ') || 'No devices';
  };

  return (
    <div>
      {/* Unassigned Devices */}
      {unassignedDevices && unassignedDevices.length > 0 && (
        <div
          key="unassigned"
          style={{
            padding: isMobile ? '10px 12px' : '12px 16px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            borderBottom: '1px solid #f0f0f0',
            backgroundColor: selectedClientId === -1 ? '#fff2e8' : 'transparent',
            borderLeft: selectedClientId === -1 ? '3px solid #fa8c16' : '3px solid transparent',
            marginBottom: '4px'
          }}
          onClick={() => onClientSelect(-1)}
        >
          <div style={{ flex: 1 }}>
            <div style={{ fontWeight: 500, fontSize: isMobile ? '13px' : '14px', color: '#fa8c16' }}>
              ⚠️ Inactivated Devices
            </div>
            <div style={{ fontSize: isMobile ? '11px' : '12px', color: '#666', marginTop: '-2px' }}>
              {getClientStatusSummary(unassignedDevices)} • Need activation
            </div>
          </div>
          <div style={{ fontSize: isMobile ? '11px' : '12px', color: '#fa8c16', fontWeight: 600 }}>
            {unassignedDevices.length}
          </div>
        </div>
      )}

      {/* Regular Clients */}
      {clients.map((client) => (
        <div
          key={client.clientId}
          style={{
            padding: isMobile ? '10px 12px' : '12px 16px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            borderBottom: '1px solid #f0f0f0',
            backgroundColor: selectedClientId === client.clientId ? '#e7f1ee' : 'transparent',
            borderLeft: selectedClientId === client.clientId ? `3px solid ${PRIMARY_COLOR}` : '3px solid transparent',
            marginBottom: '4px'
          }}
          onClick={() => onClientSelect(client.clientId)}
        >
          <div style={{ flex: 1 }}>
            <div style={{ fontWeight: 500, fontSize: isMobile ? '13px' : '14px' }}>
              {client.clientName}
            </div>
            <div style={{ fontSize: isMobile ? '11px' : '12px', color: '#666', marginTop: '-2px' }}>
              {getClientStatusSummary(client.devices)}
            </div>
          </div>
          <div style={{ fontSize: isMobile ? '11px' : '12px', color: '#666', fontWeight: 500 }}>
            {client.stats.total}
          </div>
        </div>
      ))}
    </div>
  );
});

ClientList.displayName = 'ClientList';

// ** Simple Inline Filter Component
const SimpleFilters = React.memo<{
  currentFilter: 'all' | 'active' | 'recently_active' | 'idle' | 'disconnected' | 'inactive';
  onFilterChange: (filter: 'all' | 'active' | 'recently_active' | 'idle' | 'disconnected' | 'inactive') => void;
  onClearFilter: () => void;
}>(({ currentFilter, onFilterChange, onClearFilter }) => {
  const isMobile = useMediaQuery({ query: BREAKPOINTS.mobile });

  const statusOptions = [
    { label: 'All', value: 'all' as const },
    { label: 'Active', value: 'active' as const },
    { label: 'Recent', value: 'recently_active' as const },
    { label: 'Idle', value: 'idle' as const },
    { label: 'Offline', value: 'disconnected' as const },
    { label: 'Inactive', value: 'inactive' as const }
  ];

  return (
    <div style={{
      padding: isMobile ? '6px 8px' : '8px 12px',
      backgroundColor: '#fff',
      borderBottom: '1px solid #f0f0f0',
      width: '100%',
      boxSizing: 'border-box'
    }}>
      <div style={{
        fontSize: isMobile ? '10px' : '11px',
        fontWeight: 500,
        color: '#666',
        marginBottom: '6px'
      }}>
        Filter:
      </div>

      <div style={{
        display: 'flex',
        flexWrap: 'wrap',
        gap: '4px'
      }}>
        {statusOptions.map(option => (
          <Tag.CheckableTag
            key={option.value}
            checked={currentFilter === option.value}
            onChange={() => onFilterChange(option.value)}
            style={{
              padding: '1px 6px',
              borderRadius: '10px',
              fontSize: isMobile ? '9px' : '10px',
              border: currentFilter === option.value ? '1px solid #1890ff' : '1px solid #d9d9d9',
              margin: 0,
              lineHeight: '18px'
            }}
          >
            {option.label}
          </Tag.CheckableTag>
        ))}

        {currentFilter !== 'active' && (
          <Button
            type="text"
            size="small"
            onClick={onClearFilter}
            style={{
              fontSize: isMobile ? '8px' : '9px',
              color: '#666',
              padding: '0 4px',
              height: '18px',
              lineHeight: '18px'
            }}
          >
            Reset
          </Button>
        )}
      </div>
    </div>
  );
});

SimpleFilters.displayName = 'SimpleFilters';

// ** Type definitions
interface DeviceStats {
  total: number;
  active: number;
  recently_active: number;
  idle: number;
  disconnected: number;
  inactive: number;
}

interface ClientData {
  clientId: number;
  clientName: string;
  devices: any[];
  lastActiveTime: Date | null;
  stats: DeviceStats;
}

// ** Main Timeclocks Component
const Timeclocks: React.FC = () => {
  const { user } = useAuthStore();
  const { setModal, showModal } = useModalStore();
  const { Ntf } = useFeedback();

  // ** Responsive hooks using react-responsive
  const isMobile = useMediaQuery({ query: BREAKPOINTS.mobile });
  const isTablet = useMediaQuery({ query: BREAKPOINTS.tablet });
  const STYLES = React.useMemo(() => getResponsiveStyles(isMobile, isTablet), [isMobile, isTablet]);

  // ** State Management
  const [searchData, setSearchData] = React.useState({
    status: 'active' as 'all' | 'active' | 'recently_active' | 'idle' | 'disconnected' | 'inactive',
    searchQuery: '',
    sortBy: 'lastActive' as 'name' | 'lastActive'
  });

  const [selectedClientId, setSelectedClientId] = React.useState<number | null>(null);
  const [autoRefresh, setAutoRefresh] = React.useState(true);
  const [selectedAccountId, setSelectedAccountId] = React.useState<number | string>(user?.account?.id || 1);
  const [mobileDrawerOpen, setMobileDrawerOpen] = React.useState(false);

  // ** Check if user can see account selector (super admin only)
  const canSelectAccount = user?.role === 1 && user?.account?.id === 1;

  // ** Fetch accounts list (only for super admin)
  const {
    data: accounts = [],
    isLoading: accountsLoading
  } = useQuery({
    queryKey: ['accounts-list'],
    queryFn: async () => {
      const response = await api('GetSystemAccounts', {
        user_id: user?.id,
        role: user?.role,
        account_id: user?.account?.id
      }, 'POST');
      return response?.data || [];
    },
    enabled: canSelectAccount,
    staleTime: 5 * 60 * 1000 // 5 minutes
  });

  // ** React Query for data fetching
  const {
    data: devices = [],
    isLoading,
    error,
    refetch,
    isRefetching
  } = useQuery({
    queryKey: ['timeclock-devices', selectedAccountId],
    queryFn: async () => {
      if (!selectedAccountId) return [];
      const response = await api('FetchDeviceFleet', { account_id: selectedAccountId }, 'POST');
      return response?.data || [];
    },
    enabled: !!selectedAccountId,
    refetchInterval: autoRefresh ? 30000 : false,
    refetchIntervalInBackground: true,
    staleTime: 10000
  });

  // ** Process devices and group by clients
  const processedData = React.useMemo(() => {
    if (!devices.length) return {
      clients: [] as ClientData[],
      unassignedDevices: [] as any[],
      stats: { total: 0, active: 0, recently_active: 0, idle: 0, disconnected: 0, inactive: 0 } as DeviceStats
    };

    const assignedDevices = devices.filter((device: any) => device.client_id);
    const unassignedDevices = devices.filter((device: any) => !device.client_id);

    const stats: DeviceStats = devices.reduce((acc: DeviceStats, device: any) => {
      const status = getEnhancedDeviceStatus(device);
      acc.total++;

      // Safe property access
      if (status.status === 'active') acc.active++;
      else if (status.status === 'recently_active') acc.recently_active++;
      else if (status.status === 'idle') acc.idle++;
      else if (status.status === 'disconnected') acc.disconnected++;
      else if (status.status === 'inactive') acc.inactive++;

      return acc;
    }, { total: 0, active: 0, recently_active: 0, idle: 0, disconnected: 0, inactive: 0 });

    const clientsMap = assignedDevices.reduce((acc: Record<string, any>, device: any) => {
      const clientId = device.client_id;
      const clientName = device.client_name || 'Unknown Client';

      if (!acc[clientId]) {
        acc[clientId] = {
          clientId,
          clientName,
          devices: [],
          lastActiveTime: null as Date | null
        };
      }

      acc[clientId].devices.push(device);

      if (device.last_event) {
        const deviceTime = new Date(device.last_event);
        if (!acc[clientId].lastActiveTime || deviceTime > acc[clientId].lastActiveTime) {
          acc[clientId].lastActiveTime = deviceTime;
        }
      }

      return acc;
    }, {} as Record<string, any>);

    let clients: ClientData[] = Object.values(clientsMap).map((client: any) => ({
      ...client,
      stats: client.devices.reduce((acc: DeviceStats, device: any) => {
        const status = getEnhancedDeviceStatus(device);
        acc.total++;

        // Safe property access
        if (status.status === 'active') acc.active++;
        else if (status.status === 'recently_active') acc.recently_active++;
        else if (status.status === 'idle') acc.idle++;
        else if (status.status === 'disconnected') acc.disconnected++;
        else if (status.status === 'inactive') acc.inactive++;

        return acc;
      }, { total: 0, active: 0, recently_active: 0, idle: 0, disconnected: 0, inactive: 0 })
    }));

    if (searchData.status !== 'all') {
      clients = clients.filter((client: ClientData) =>
        client.devices.some((device: any) => {
          const status = getEnhancedDeviceStatus(device);
          return status.status === searchData.status;
        })
      );
    }

    if (searchData.sortBy === 'lastActive') {
      clients.sort((a: ClientData, b: ClientData) => {
        if (!a.lastActiveTime && !b.lastActiveTime) return a.clientName.localeCompare(b.clientName);
        if (!a.lastActiveTime) return 1;
        if (!b.lastActiveTime) return -1;
        return b.lastActiveTime.getTime() - a.lastActiveTime.getTime();
      });
    } else {
      clients.sort((a: ClientData, b: ClientData) => a.clientName.localeCompare(b.clientName));
    }

    return { clients, unassignedDevices, stats };
  }, [devices, searchData.status, searchData.sortBy]);

  // ** Get devices for selected client
  const selectedClientDevices = React.useMemo(() => {
    if (!selectedClientId) return devices;
    if (selectedClientId === -1) return processedData.unassignedDevices || [];
    const client = processedData.clients.find((c: ClientData) => c.clientId === selectedClientId);
    return client?.devices || [];
  }, [processedData.clients, processedData.unassignedDevices, selectedClientId, devices]);

  // ** Event Handlers
  const handleClientSelect = React.useCallback((clientId: number) => {
    const isCurrentlySelected = selectedClientId === clientId;
    setSelectedClientId(isCurrentlySelected ? null : clientId);
  }, [selectedClientId]);

  const updateStatusFilter = React.useCallback((status: 'all' | 'active' | 'recently_active' | 'idle' | 'disconnected' | 'inactive') => {
    setSearchData(current => ({ ...current, status }));
    setSelectedClientId(null);
  }, []);

  const clearFilters = React.useCallback(() => {
    setSearchData(current => ({ ...current, status: 'active' }));
    setSelectedClientId(null);
  }, []);

  const handleDeviceClick = React.useCallback((deviceId: number) => {
    const device = devices.find((d: any) => d.id === deviceId);
    if (!device) return;

    setModal({
      title: `Edit ${device.name}`,
      width: isMobile ? '95%' : '60%',
      maskClosable: false,
      content: <Device mode="edit" deviceId={deviceId} onSuccess={() => refetch()} />
    });
    showModal();
  }, [devices, setModal, showModal, refetch, isMobile]);

  const handleAddDevice = React.useCallback(() => {
    setModal({
      title: 'Add New Device',
      width: isMobile ? '95%' : '60%',
      maskClosable: false,
      content: <Device mode="add" onSuccess={() => refetch()} />
    });
    showModal();
  }, [setModal, showModal, refetch, isMobile]);

  const operationsMenu = {
    items: [
      {
        key: 'add',
        label: 'Add Device',
        icon: <IoAddCircleOutline />,
        onClick: handleAddDevice,
      },
      {
        key: 'refresh',
        label: 'Refresh Data',
        icon: <ReloadOutlined spin={isRefetching} />,
        onClick: () => refetch(),
      }
    ],
  };

  if (error) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <div style={{ marginBottom: '16px', color: '#ff4d4f' }}>
          Error loading devices: {(error as any).message}
        </div>
        <Button type="primary" onClick={() => refetch()}>
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div style={STYLES.container}>
      {/* Mobile Header with Drawer Toggle */}
      <div style={STYLES.mobileHeader}>
        <Button
          type="text"
          icon={<MenuOutlined />}
          onClick={() => setMobileDrawerOpen(true)}
        >
          Clients
        </Button>
        <Button type="primary" size="small" onClick={handleAddDevice}>
          Add Device
        </Button>
      </div>

      {/* Page Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexDirection: isMobile ? 'column' : 'row',
        gap: isMobile ? '12px' : '0',
        padding: isMobile ? '12px 16px' : '0'
      }}>
        <PageHeader
          title="Timeclocks"
          breadcrumbs={[
            { title: <HomeOutlined />, path: '/' },
            { title: 'App Devices' },
            { title: 'Timeclocks' }
          ]}
        />
        {!isMobile && (
          <Dropdown menu={operationsMenu}>
            <Button type="primary">
              Operations <HiOutlineChevronDown />
            </Button>
          </Dropdown>
        )}
      </div>

      {/* Stats Overview Bar */}
      <div style={STYLES.statsBar}>
        <div style={{
          display: 'flex',
          gap: isMobile ? '12px' : '24px',
          flexDirection: isMobile ? 'column' : 'row',
          alignItems: isMobile ? 'flex-start' : 'center',
          width: isMobile ? '100%' : 'auto'
        }}>
          <div>
            <span style={{
              fontSize: isMobile ? '18px' : '20px',
              fontWeight: 600,
              color: '#262626'
            }}>
              {processedData.stats.total}
            </span>
            <span style={{ marginLeft: '8px', color: '#8c8c8c' }}>Total Devices</span>
          </div>
          {!isMobile && <Divider type="vertical" style={{ height: '32px' }} />}
          <div style={{
            display: 'flex',
            gap: isMobile ? '8px' : '16px',
            flexWrap: 'wrap'
          }}>
            <Badge color="#52c41a" text={`${processedData.stats.active} Active`} />
            <Badge color="#faad14" text={`${processedData.stats.recently_active} Recent`} />
            <Badge color="#ff7a45" text={`${processedData.stats.idle} Idle`} />
            <Badge color="#d9534f" text={`${processedData.stats.disconnected} Disconnected`} />
            <Badge color="#666666" text={`${processedData.stats.inactive} Inactive`} />
          </div>
        </div>

        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: isMobile ? '8px' : '16px',
          flexDirection: isMobile ? 'column' : 'row',
          width: isMobile ? '100%' : 'auto'
        }}>
          {/* Account Selector (Super Admin Only) */}
          {canSelectAccount && (
            <>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{ fontSize: isMobile ? '13px' : '14px', fontWeight: 600 }}>Account:</span>
                <Select
                  value={String(selectedAccountId)}
                  onChange={(value: string) => {
                    setSelectedAccountId(Number(value));
                    setSelectedClientId(null);
                  }}
                  variant="filled"
                  style={{ width: isMobile ? 150 : 200 }}
                  loading={accountsLoading}
                  showSearch
                  size={isMobile ? 'small' : 'middle'}
                  placeholder="Select account"
                  filterOption={(input, option) =>
                    String(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {accounts.map((account: any) => (
                    <Select.Option key={account.id} value={String(account.id)} label={account.company_name}>
                      {account.company_name}
                    </Select.Option>
                  ))}
                </Select>
              </div>
              {!isMobile && <Divider type="vertical" style={{ height: '32px' }} />}
            </>
          )}

          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <span style={{ fontSize: isMobile ? '13px' : '14px', fontWeight: 600 }}>Sort by:</span>
            <Select
              value={searchData.sortBy}
              onChange={(value) => setSearchData(current => ({ ...current, sortBy: value }))}
              variant="filled"
              style={{ width: isMobile ? 100 : 120 }}
              size={isMobile ? 'small' : 'middle'}
            >
              <Select.Option value="name">Name</Select.Option>
              <Select.Option value="lastActive">Last Active</Select.Option>
            </Select>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <span style={{ fontSize: isMobile ? '13px' : '14px', fontWeight: 600 }}>Auto-refresh</span>
            <Switch
              checked={autoRefresh}
              onChange={setAutoRefresh}
              size="small"
            />
          </div>

          {isRefetching && <SyncOutlined spin style={{ color: '#faad14' }} />}
        </div>
      </div>

      {/* Main Content */}
      <div style={STYLES.mainContent}>
        {/* Mobile Drawer for Client List */}
        <Drawer
          title="Client List"
          placement="left"
          onClose={() => setMobileDrawerOpen(false)}
          open={mobileDrawerOpen}
          width={isMobile ? '90%' : '320px'}
          styles={{ body: { padding: 0 } }}
        >
          <SimpleFilters
            currentFilter={searchData.status}
            onFilterChange={updateStatusFilter}
            onClearFilter={clearFilters}
          />

          <div style={{ flex: 1, overflow: 'auto' }}>
            {isLoading ? (
              <div style={{ padding: '16px' }}>
                <Skeleton active paragraph={{ rows: 4 }} />
              </div>
            ) : (
              <ClientList
                clients={processedData.clients}
                unassignedDevices={processedData.unassignedDevices || []}
                selectedClientId={selectedClientId}
                onClientSelect={(clientId) => {
                  handleClientSelect(clientId);
                  if (isMobile) setMobileDrawerOpen(false);
                }}
              />
            )}
          </div>
        </Drawer>

        {/* Desktop Client List Sidebar */}
        {!isMobile && (
          <div style={STYLES.sidebar}>
            <SimpleFilters
              currentFilter={searchData.status}
              onFilterChange={updateStatusFilter}
              onClearFilter={clearFilters}
            />

            <div style={{ flex: 1, overflow: 'auto' }}>
              {isLoading ? (
                <div style={{ padding: '16px' }}>
                  <Skeleton active paragraph={{ rows: 4 }} />
                </div>
              ) : (
                <ClientList
                  clients={processedData.clients}
                  unassignedDevices={processedData.unassignedDevices || []}
                  selectedClientId={selectedClientId}
                  onClientSelect={handleClientSelect}
                />
              )}
            </div>
          </div>
        )}

        {/* Device Table */}
        <div style={STYLES.deviceContainer}>
          <div style={{
            padding: isMobile ? '12px 16px' : '16px 24px',
            borderBottom: '1px solid #f0f0f0',
            backgroundColor: '#fff',
            minHeight: isMobile ? '48px' : '56px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            <Breadcrumb
              style={{ fontSize: isMobile ? '12px' : '14px' }}
              items={[
                ...(canSelectAccount && selectedAccountId !== user?.account?.id ? [{
                  title: accounts.find((acc: any) => acc.id === selectedAccountId)?.company_name || 'Unknown Account'
                }] : []),
                ...(selectedClientId === -1 ? [
                  { title: 'Inactivated Devices' },
                  { title: '⚠️ Need Activation' }
                ] : selectedClientId ? [
                  { title: processedData.clients.find((c: ClientData) => c.clientId === selectedClientId)?.clientName },
                  { title: 'Devices' }
                ] : [
                  { title: 'All Devices' }
                ])
              ]}
            />

            {isMobile && (
              <Button
                type="text"
                size="small"
                icon={<ReloadOutlined spin={isRefetching} />}
                onClick={() => refetch()}
              />
            )}
          </div>

          <div style={{ flex: 1, overflow: 'auto' }}>
            <DeviceTable
              devices={selectedClientDevices}
              onDeviceClick={handleDeviceClick}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Timeclocks;
