import { useState, useCallback, useRef } from 'react';
import { LoginFormData, Role } from '@/types/auth';
import { UseLoginFormReturn } from '@/types/login-components';

interface UseLoginFormOptions {
  initialValues?: Partial<LoginFormData>;
  onSubmit: (values: LoginFormData) => Promise<void>;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  roles: Role[];
}

// Validation rules
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
const PASSWORD_MIN_LENGTH = 6;

// Validation functions
const validateEmail = (email: string): string | null => {
  if (!email) return 'Email address is required';
  if (!EMAIL_REGEX.test(email)) return 'Please enter a valid email address';
  return null;
};

const validatePassword = (password: string): string | null => {
  if (!password) return 'Password is required';
  if (password.length < PASSWORD_MIN_LENGTH) {
    return `Password must be at least ${PASSWORD_MIN_LENGTH} characters long`;
  }
  return null;
};

const validateRole = (role: any, roles: Role[]): string | null => {
  if (!role) return 'Please select an access level';
  const validRole = roles.find(r => r.id === parseInt(role) && r.active);
  if (!validRole) return 'Please select a valid access level';
  return null;
};

// Debounce function for validation
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

export const useLoginForm = ({
  initialValues = {},
  onSubmit,
  validateOnChange = false,
  validateOnBlur = true,
  roles,
}: UseLoginFormOptions): UseLoginFormReturn => {
  const [formData, setFormData] = useState<LoginFormData>({
    role: initialValues.role || '',
    email: initialValues.email || '',
    password: initialValues.password || '',
    rememberMe: initialValues.rememberMe || false,
  });

  const [errors, setErrors] = useState<Record<keyof LoginFormData, string>>({
    role: '',
    email: '',
    password: '',
    rememberMe: '',
  });

  const [touched, setTouched] = useState<Record<keyof LoginFormData, boolean>>({
    role: false,
    email: false,
    password: false,
    rememberMe: false,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitAttempted, setSubmitAttempted] = useState(false);

  // Refs for debounced validation
  const debouncedValidateRef = useRef<Record<string, Function>>({});

  // Validate a single field
  const validateField = useCallback((field: keyof LoginFormData, value?: any): string | null => {
    const fieldValue = value !== undefined ? value : formData[field];

    switch (field) {
      case 'email':
        return validateEmail(fieldValue as string);
      case 'password':
        return validatePassword(fieldValue as string);
      case 'role':
        return validateRole(fieldValue, roles);
      case 'rememberMe':
        return null; // No validation needed for checkbox
      default:
        return null;
    }
  }, [formData, roles]);

  // Validate entire form
  const validateForm = useCallback((): boolean => {
    const newErrors: Record<keyof LoginFormData, string> = {
      role: '',
      email: '',
      password: '',
      rememberMe: '',
    };

    let isValid = true;

    // Validate each field
    (Object.keys(formData) as Array<keyof LoginFormData>).forEach(field => {
      const error = validateField(field);
      if (error) {
        newErrors[field] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  }, [formData, validateField]);

  // Handle field changes
  const handleChange = useCallback((field: keyof LoginFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing (immediate feedback)
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    // Validate on change if enabled
    if (validateOnChange || (submitAttempted && touched[field])) {
      // Debounce validation to avoid excessive validation calls
      if (!debouncedValidateRef.current[field]) {
        debouncedValidateRef.current[field] = debounce((fieldName: keyof LoginFormData, fieldValue: any) => {
          const error = validateField(fieldName, fieldValue);
          setErrors(prev => ({ ...prev, [fieldName]: error || '' }));
        }, 300);
      }

      debouncedValidateRef.current[field](field, value);
    }
  }, [errors, validateOnChange, submitAttempted, touched, validateField]);

  // Handle field blur
  const handleBlur = useCallback((field: keyof LoginFormData) => {
    setTouched(prev => ({ ...prev, [field]: true }));

    // Validate on blur if enabled or if submit was attempted
    if (validateOnBlur || submitAttempted) {
      const error = validateField(field);
      setErrors(prev => ({ ...prev, [field]: error || '' }));
    }
  }, [validateOnBlur, submitAttempted, validateField]);

  // Handle form submission
  const handleSubmit = useCallback(async (): Promise<void> => {
    setSubmitAttempted(true);
    setIsSubmitting(true);

    // Mark all fields as touched
    setTouched({
      role: true,
      email: true,
      password: true,
      rememberMe: true,
    });

    try {
      // Validate form
      const isValid = validateForm();

      if (!isValid) {
        // Focus on first error field
        const firstErrorField = Object.keys(errors).find(field => errors[field as keyof LoginFormData]);
        if (firstErrorField) {
          const element = document.getElementById(`field-${firstErrorField}`);
          element?.focus();
        }
        return;
      }

      // Submit form
      await onSubmit(formData);
    } catch (error) {
      // Error handling is done by parent component
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, onSubmit, validateForm, errors]);

  // Reset form
  const handleReset = useCallback(() => {
    setFormData({
      role: initialValues.role || '',
      email: initialValues.email || '',
      password: initialValues.password || '',
      rememberMe: initialValues.rememberMe || false,
    });
    setErrors({
      role: '',
      email: '',
      password: '',
      rememberMe: '',
    });
    setTouched({
      role: false,
      email: false,
      password: false,
      rememberMe: false,
    });
    setIsSubmitting(false);
    setSubmitAttempted(false);
  }, [initialValues]);

  // Check if form is valid
  const isValid = Object.values(errors).every(error => !error) && 
                  Object.values(formData).some(value => value !== '' && value !== false);

  return {
    formData,
    errors,
    isSubmitting,
    isValid,
    handleChange,
    handleSubmit,
    handleReset,
    validateField,
    validateForm,
  };
};
