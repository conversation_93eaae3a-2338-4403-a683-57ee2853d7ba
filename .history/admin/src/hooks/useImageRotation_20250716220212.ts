import { useState, useEffect, useCallback, useRef } from 'react';
import { HeroImageData, UseImageRotationReturn } from '@/types/login-components';

interface UseImageRotationOptions {
  images: HeroImageData[];
  interval?: number;
  autoRotate?: boolean;
  preloadNext?: boolean;
  onRotate?: (currentIndex: number, nextIndex: number) => void;
  onImageLoad?: (imageId: string) => void;
  onImageError?: (imageId: string, error: Error) => void;
}

export const useImageRotation = ({
  images,
  interval = 5000,
  autoRotate = true,
  preloadNext = true,
  onRotate,
  onImageLoad,
  onImageError,
}: UseImageRotationOptions): UseImageRotationReturn => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(true);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const preloadedImages = useRef<Map<string, HTMLImageElement>>(new Map());

  const currentImage = images[currentIndex] || null;
  const error = imageErrors.size > 0 ? `Failed to load ${imageErrors.size} images` : null;

  // Preload image function
  const preloadImage = useCallback((image: HeroImageData): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (preloadedImages.current.has(image.id)) {
        resolve();
        return;
      }

      const img = new Image();

      img.onload = () => {
        preloadedImages.current.set(image.id, img);
        setLoadedImages(prev => new Set(prev).add(image.id));
        setIsLoading(false);
        onImageLoad?.(image.id);
        resolve();
      };

      img.onerror = () => {
        const error = new Error(`Failed to load image: ${image.id}`);
        setImageErrors(prev => new Set(prev).add(image.id));
        onImageError?.(image.id, error);
        reject(error);
      };

      // Use WebP if available, fallback to regular URL
      img.src = image.webpUrl || image.url;
    });
  }, [onImageLoad, onImageError]);

  // Preload all images
  const preloadImages = useCallback(async (): Promise<void> => {
    if (!images.length) return;

    setIsLoading(true);

    try {
      // Preload current image first
      if (images[currentIndex]) {
        await preloadImage(images[currentIndex]);
      }

      // Preload remaining images in background
      const remainingImages = images.filter((_, index) => index !== currentIndex);
      await Promise.allSettled(remainingImages.map(preloadImage));
    } catch (error) {
      console.error('Error preloading images:', error);
    } finally {
      setIsLoading(false);
    }
  }, [images, currentIndex, preloadImage]);

  // Navigation functions
  const nextImage = useCallback(() => {
    if (images.length <= 1) return;

    const nextIndex = (currentIndex + 1) % images.length;
    onRotate?.(currentIndex, nextIndex);
    setCurrentIndex(nextIndex);
  }, [currentIndex, images.length, onRotate]);

  const previousImage = useCallback(() => {
    if (images.length <= 1) return;

    const prevIndex = currentIndex === 0 ? images.length - 1 : currentIndex - 1;
    onRotate?.(currentIndex, prevIndex);
    setCurrentIndex(prevIndex);
  }, [currentIndex, images.length, onRotate]);

  const goToImage = useCallback((index: number) => {
    if (index < 0 || index >= images.length || index === currentIndex) return;

    onRotate?.(currentIndex, index);
    setCurrentIndex(index);
  }, [currentIndex, images.length, onRotate]);

  // Auto-rotation effect
  useEffect(() => {
    if (!autoRotate || images.length <= 1) return;

    intervalRef.current = setInterval(nextImage, interval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [autoRotate, images.length, interval, nextImage]);

  // Preload next image when current changes
  useEffect(() => {
    if (!preloadNext || images.length <= 1) return;

    const nextIndex = (currentIndex + 1) % images.length;
    const nextImage = images[nextIndex];

    if (nextImage && !loadedImages.has(nextImage.id)) {
      preloadImage(nextImage).catch(() => {
        // Silently handle preload errors
      });
    }
  }, [currentIndex, images, loadedImages, preloadNext, preloadImage]);

  // Reset state when images change
  useEffect(() => {
    setCurrentIndex(0);
    setLoadedImages(new Set());
    setImageErrors(new Set());
    setIsLoading(true);
    preloadedImages.current.clear();

    if (images.length > 0) {
      preloadImages();
    }
  }, [images, preloadImages]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      preloadedImages.current.clear();
    };
  }, []);

  return {
    currentImage,
    currentIndex,
    isLoading,
    error,
    nextImage,
    previousImage,
    goToImage,
    preloadImages,
  };
};
