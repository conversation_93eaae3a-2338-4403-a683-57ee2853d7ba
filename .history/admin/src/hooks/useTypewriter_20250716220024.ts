import { useState, useEffect, useCallback, useRef } from 'react';
import type { UseTypewriterReturn } from '../types/login-components';

interface UseTypewriterOptions {
  text: string;
  speed?: number;
  startDelay?: number;
  loop?: boolean;
  loopDelay?: number;
  onStart?: () => void;
  onComplete?: () => void;
  onCharacterTyped?: (char: string, index: number) => void;
  onLoop?: () => void;
}

export const useTypewriter = ({
  text,
  speed = 80,
  startDelay = 1000,
  loop = false,
  loopDelay = 2000,
  onStart,
  onComplete,
  onCharacterTyped,
  onLoop,
}: UseTypewriterOptions): UseTypewriterReturn => {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTyping, setIsTyping] = useState(false);
  const [isComplete, setIsComplete] = useState(false);

  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const loopTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isActiveRef = useRef(true);

  // Type next character
  const typeNextCharacter = useCallback(() => {
    if (!isActiveRef.current || currentIndex >= text.length) {
      setIsTyping(false);
      setIsComplete(true);
      onComplete?.();

      // Set up loop if enabled
      if (loop && isActiveRef.current) {
        loopTimeoutRef.current = setTimeout(() => {
          if (isActiveRef.current) {
            onLoop?.();
            setDisplayedText('');
            setCurrentIndex(0);
            setIsComplete(false);
            setIsTyping(true);
            typeNextCharacter();
          }
        }, loopDelay);
      }
      return;
    }

    const nextChar = text[currentIndex];
    setDisplayedText(prev => prev + nextChar);
    setCurrentIndex(prev => prev + 1);
    onCharacterTyped?.(nextChar, currentIndex);

    if (isActiveRef.current) {
      typingTimeoutRef.current = setTimeout(typeNextCharacter, speed);
    }
  }, [currentIndex, text, speed, onCharacterTyped, onComplete, loop, loopDelay, onLoop]);

  // Start typing
  const start = useCallback(() => {
    if (isTyping || isComplete) return;

    setIsTyping(true);
    setIsComplete(false);
    onStart?.();

    if (startDelay > 0) {
      startTimeoutRef.current = setTimeout(() => {
        if (isActiveRef.current) {
          typeNextCharacter();
        }
      }, startDelay);
    } else {
      typeNextCharacter();
    }
  }, [isTyping, isComplete, onStart, startDelay, typeNextCharacter]);

  // Stop typing
  const stop = useCallback(() => {
    isActiveRef.current = false;
    setIsTyping(false);

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }
    if (startTimeoutRef.current) {
      clearTimeout(startTimeoutRef.current);
      startTimeoutRef.current = null;
    }
    if (loopTimeoutRef.current) {
      clearTimeout(loopTimeoutRef.current);
      loopTimeoutRef.current = null;
    }
  }, []);

  // Reset typing
  const reset = useCallback(() => {
    stop();
    setDisplayedText('');
    setCurrentIndex(0);
    setIsComplete(false);
    isActiveRef.current = true;
  }, [stop]);

  // Auto-start when text changes
  useEffect(() => {
    reset();
    if (text) {
      start();
    }
  }, [text]); // Only depend on text to avoid infinite loops

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isActiveRef.current = false;
      if (typingTimeoutRef.current) clearTimeout(typingTimeoutRef.current);
      if (startTimeoutRef.current) clearTimeout(startTimeoutRef.current);
      if (loopTimeoutRef.current) clearTimeout(loopTimeoutRef.current);
    };
  }, []);

  return {
    displayedText,
    isComplete,
    isTyping,
    start,
    stop,
    reset,
  };
};
