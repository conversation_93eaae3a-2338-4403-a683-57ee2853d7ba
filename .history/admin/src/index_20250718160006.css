:root {
  --primary-color: #2E6454;
  /* font-family: system-ui, Avenir, Helvetica, Arial, sans-serif; */
  font-family: -apple-system,system-ui,BlinkMacSystemFont,"Segoe UI",Robot<PERSON>,"Helvetica Neue",Arial,sans-serif ;
}

body {
  margin: 0;
}

.text-primary {
  color: var(--primary-color) !important;

}
/* minimal fade transition */
.fade-enter {
  opacity: 0;
}
.fade-enter-active {
  opacity: 1;
  transition: opacity 150ms ease-in-out;
}
.fade-exit {
  opacity: 1;
}
.fade-exit-active {
  opacity: 0;
  transition: opacity 150ms ease-in-out;
}

/* Mobile overlay animations for appv3 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.mobile-sidenav-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.45);
  z-index: 150;
}

a {
  text-decoration: none;
  transition: all 0.3s ease-in-out;
}

/* <PERSON>rome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
  appearance: textfield;
}

.ns {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -khtml-user-select: none;
  -moz-user-drag: none;
}


.pointer {
  cursor: pointer !important;
}

.hover:hover {
  cursor: pointer;
  background-color: #ddd !important;
  border-radius: 6px !important;
}

.hover-2 {
  transition: all 0.3s ease-in-out;
  cursor: pointer;
}

.hover-2:hover {
  background-color: #fcf5e9 !important;
  border: solid 1px #ddd;
}

.ant-table-content td:hover {
  cursor: pointer !important
}

.fs-2 {
  letter-spacing: -0.04rem;
}

.fs-3 {
  letter-spacing: -0.02rem;
}

.fs-4 {
  letter-spacing: -0.03rem;
}

.table-row-bottom {
  border-bottom: 1px dotted #ddd !important;
}


.bg-light-0 {
  background-color: #f7f7f7 !important;
}

.bg-light-1 {
  background-color: #efefef !important;
}

.bg-light-2 {
  background-color: #ccc !important;
}

.bg-light-3 {
  background-color: #ddd !important;
}


.bg-warning-2 {
  background-color: #fcf5e9 !important;
}

.text-warning-2 {
  color: #D36B08 !important;
}

.bg-success-2 {
  background-color: #e1f8e7 !important;
}

.bg-danger-2 {
  background-color: #fdecea !important;
}

.bg-danger-3 {
  background-color: #f4eded !important;
}


.text-danger-2 {
  color: #69182b !important;
}


.m-width {
  width: 100%;
  max-width: 200px;
}


/* LOADING */
.loader-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999; 
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px); 
}

.blur-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loader-text {
  margin-top: 12px;
  font-size: 1.4rem;
  margin-top: 20px;
  text-align: center;
}


.mobile-scroll { 
  cursor: pointer;
  overflow: auto;
  width: 100%;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  -webkit-user-select: none;
  user-select: none;
}


/* MOBILE */
@media (max-width: 767px) {

  .m-width {
    width: 100% !important;
  }

  .fs-2 {
    letter-spacing: -0.01rem;
  }

}


.main-container {
  background-color: white;
  padding: 15px 20px;
  min-height: 360px;
  width: 100%;
  margin: 0 auto;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: rgba(17, 17, 26, 0.05) 0px 1px 0px, rgba(17, 17, 26, 0.1) 0px 0px 8px;
}

@media (max-width: 767px) {
  .main-container {
    padding: 0;
  }
}

@media (max-width: 1411px) {
  .main-container {
    padding: 24px;
  }
}

/* Fixing the ant design table header */
.ant-table-thead > tr > th {
  background: none !important;
  transition: none !important;
}
.ant-table-column-sort,
.ant-table-thead > tr > th:hover {
  background: none !important;
}

/***************************** */
/** TABLE ROWS                */
/*****************************/

.t-row {
  border-bottom: dashed 1px #ccc;
  cursor: pointer;
  margin: 5px 0 !important
 
}

.t-row div:first-child {
  /* background-color: #efefef; */
  font-size: 0.95rem !important;
  font-weight: bold;
}

.t-row div:last-child {
  font-size: 0.95rem !important;
}

@media (max-width: 767px) {

  .t-row {
    max-width: 100%;
  }

  .t-row div:first-child {
   padding: 0;
   font-size: 0.88rem !important;
  }

  .t-row div:last-child {
    padding: 0;
    font-size: 0.88rem !important;
  }

}

/*****************************/
/** NOTE BADGES              */
/*****************************/

.notes-badge {
  padding: 3px 7px;
  font-size: 13px;
  border-radius: 5px;
  margin: 5px;
}

.l-yellow {
  background-color: #f9e79f;
}

.l-green {
  background-color: #a3e4d7;
}

.l-red {
  background-color: #f5b7b1;
}

.l-gray {
  background-color: #d6dbdf;
}


.note-borders {
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
}

.note-yellow {
  border-left: solid 15px #f9e79f;
  border-right: dotted 1px #f9e79f;
  border-bottom: dotted 1px #f9e79f;
  border-top: dotted 1px #f9e79f;

}

.note-green {
  border-left: solid 15px #a3e4d7;
  border-right: dotted 1px #a3e4d7;
  border-bottom: dotted 1px #a3e4d7;
  border-top: dotted 1px #a3e4d7;

}

.note-red {
  border-left: solid 15px #f5b7b1;
  border-right: dotted 1px #f5b7b1;
  border-bottom: dotted 1px #f5b7b1;
  border-top: dotted 1px #f5b7b1;
}

.note-gray {
  border-left: solid 15px #d6dbdf;
  border-right: dotted 1px #d6dbdf;
  border-bottom: dotted 1px #d6dbdf;
  border-top: dotted 1px #d6dbdf;
}


.ant-modal {
  top: 25px !important;
  margin: 0 auto !important;
}

.ant-modal-centered .ant-modal {
  transform: none !important;
}

@media (max-width: 767px) {
  .ant-modal {
    top: 0 !important;
    margin: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
  }
}

/* Dashboard Employees Onsite */
.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none; 
}
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}



.chart-item-wrapper {
  position: relative;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6c757d;
  font-size: 14px;
}

.chart-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.chartItemContainer {
  flex: '0 0 auto';
  width: 160px;
  min-height: 140px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
  border-radius: 0;
  padding: 0;
  margin: 0;
  border-bottom-width: 5px;
  border-bottom-style: solid;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  background: transparent !important;
  box-shadow: none !important;
  text-align: center;
  /* Prevent jump on hover/click by not changing border, outline, or padding */
}

.chartItemContainer:hover,
.chartItemContainer:active,
.chartItemContainer:focus {
  /* No border, outline, or box-shadow changes to prevent jump */
  background: transparent;
}

.chartItemContainer .apexcharts-canvas {
  margin: 0 auto;
}

.chartItemContainer .apexcharts-radialbar-area {
  stroke-linecap: round;
}

.chartItemContainer .apexcharts-text {
  font-family: 'Inter', sans-serif;
}

/* Ensure chart labels are visible */
.chartItemContainer .apexcharts-datalabel-value {
  font-weight: 600 !important;
  font-size: 16px !important;
}

.chartItemContainer .apexcharts-datalabel-name {
  font-weight: 500 !important;
  font-size: 12px !important;
}

/* Responsive chart improvements */
@media (max-width: 480px) {
  .chartItemContainer {
    width: 120px;
    min-height: 120px;
  }
  
  .chartItemContainer .apexcharts-datalabel-value {
    font-size: 14px !important;
  }
  
  .chartItemContainer .apexcharts-datalabel-name {
    font-size: 11px !important;
  }
}

@media (min-width: 768px) {
  .chartItemContainer {
    width: 50%;
  }
  
  .employees-container {
    height: 300px;
  }
}

@media (min-width: 1024px) {
  .chartItemContainer {
    width: 120px;
  }
  .employees-container {
    height: 400px;
  }
}

@media (min-width: 1440px) {
  .chartItemContainer {
    width: 120px;
  }

  .employees-container {
    height: 400px;
  }
}

@media (min-width: 1600px) {
  .chartItemContainer {
    width: 160px;
  }

  .employees-container {
    height: 600px;
  }
}

@media (max-width: 370px) {
  .employees-container .employee {
    width: 80px;
    max-width: calc(100% / 4 - 8px);
    min-width: 60px;
  }

  .chartItemContainer {
    width: 100px;
  }
}

@media (max-width: 767px) {
  .chartItemContainer {
    width: 50%;
  }

  .employees-container {
    height: 100%;
    max-height: 25%;
    gap: 4px;
    padding-top: 6px;
  }

  .employees-container .employee {
    width: 65px;
    min-width: 65px;
  }

  .employees-container .employee-info {
    font-size: 0.7rem;
    line-height: 1.1rem;
  }
}


/** ANDT MENU CUSTOMIZATION */

.ant-menu {
  font-family: Inter, sans-serif;
  font-size: 14px;
  font-weight: 500;
  border-inline-end: none;
}

.ant-menu-title-content {
  font-weight: 600;
  color: #3c3c3c;
}

.ant-menu-item-group-title {
  font-weight: 600;
  font-size: 13px;
  margin-top: 12px;
  color: #3c3c3c;
}

.ant-menu-item {
  height: 30px;
  line-height: 10px;
  font-weight: 600;
  color: #3c3c3c;
}

.ant-menu-item-selected {
  background-color: transparent !important;
  font-weight: 600;
  border-radius: 8px;
}

.ant-menu-item-icon {
  font-size: 16px;
}


/* Search Input */
.search-input {
  height: 32px;
  border-radius: 8px;
  background-color: #ffffff; /* ✅ Light theme background */
  color: #333;
  border: 1px solid #d9d9d9; /* ✅ Light theme border */
  padding-left: 12px;
  font-size: 14px;
  width: 100%;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  outline: none;
  box-sizing: border-box;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  font-weight: bold !important;
  display: flex;
  align-items: center;
  line-height: 1;
}

.search-input input {
  background: transparent !important;
  border: none !important;
  outline: none !important;
  color: #333 !important; /* ✅ Dark text for light background */
  font-size: 14px !important;
  line-height: 1 !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
}

/* Dark theme search input */
[data-theme='dark'] .search-input {
  background-color: #303030;
  border-color: #4B4B4B;
}

[data-theme='dark'] .search-input input {
  color: #fff !important;
}

.search-input input::placeholder {
  color: #aaa !important;
  opacity: 1 !important;
  font-size: 13px !important;
  line-height: 1 !important;
  vertical-align: middle !important;
}

/* Force light theme for all inputs when not in dark mode */
:root:not([data-theme='dark']) .ant-input,
:root:not([data-theme='dark']) .ant-input:focus,
:root:not([data-theme='dark']) input,
:root:not([data-theme='dark']) input:focus {
  background-color: #ffffff !important;
  color: #333333 !important;
  border-color: #d9d9d9 !important;
}

:root:not([data-theme='dark']) .ant-input:focus,
:root:not([data-theme='dark']) input:focus {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1) !important;
}
}

@media (max-width: 767px) {
  .search-input {
    width: calc(100% - 15px) !important;
    max-width: 100%;
    height: 36px;
    display: flex;
    align-items: center;
  }
  
  .search-input input {
    font-size: 16px !important; /* Prevents zoom on iOS */
    line-height: 1 !important;
    height: 100% !important;
  }
  
  .search-input input::placeholder {
    font-size: 14px !important;
    line-height: 1 !important;
  }
}

@media (min-width: 768px) {
  .search-input {
    width: 500px !important;
  }
}

/** CURVE FOR LAYOUT  */

.curve-wrapper {
  position: absolute;
  top: -5px;
  left: 0;
  width: 30px;
  height: 30px;
  background-color: black;
}

.curve-top-left {
  position: absolute;
  top: 0;
  left: 0;
  width: 30px;
  height: 30px;
  background-color: #EBEBEB;
  border-top-left-radius: 10px;
  z-index: 1;
}

.curve-wrapper-right {
  position: fixed;
  top: 55px;
  right: 0;
  width: 30px;
  height: 30px;
  background-color: black;
  z-index: 99;
}

.curve-top-right {
  position: absolute;
  top: 0;
  right: 0;
  width: 30px;
  height: 30px;
  background-color: #F5F5F5;
  border-top-right-radius: 10px;
}


.logo-container {
  width: 180px
}

.pages-header {
  font-size: 1.5rem;
  font-weight: 600;
  letter-spacing: -0.08rem;
}

.pages-icon {
  margin-right: 8px;
}

@media (max-width: 767px) {

  .logo-container {
    width: auto;
  }
  
  .curve-wrapper,
  .curve-wrapper-right {
    display: none;
  }
}

/** --------------------------------------------------------------------------- */
/** TIMECARD TABLE */

.timecard-table thead tr th,
.timecard-table tbody tr td {
  padding: 5px 10px;
  font-size: 0.85rem;
}

.timecard-table tbody tr:hover {
  background-color: rgba(207, 207, 207, 0.4) !important;
  cursor: pointer !important;
}

.day-row-gradient td {
  background: linear-gradient(to bottom, #efefef 0%, transparent 100%);
  border: none !important;
}

.timecard-table .daytotal-row {
  background-color: #ddd !important;
  color: #111 !important;
  font-weight: bold
}

.timecard-table .weektotal-row {
  background-color: #3c3c3c !important;
  color: #f7f7f7 !important;
}

@media (max-width: 767px) {
  .timecard-table thead tr th,
  .timecard-table tbody tr td {
    padding: 2px 5px;
    font-size: 0.75rem;
  }
}

.employees-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-start;
  overflow-x: hidden;
  overflow-y: auto;
  gap: 8px;
  padding-top: 10px;
}

.employees-container .motion-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 8px;
  width: 100%;
}

.employees-container .employee {
  width: 100px;
  max-width: calc(100% / 6 - 8px);
  min-width: 90px;
  text-align: center;
}

.employees-container .employee:hover {
  background-color: #f0f0f0;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.employees-container .employee-info {
  line-height: 1.1rem;
  font-size: 0.75rem;
  border-radius: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Responsive chart improvements */

/* Remove gap between donut charts */
.d-flex.flex-nowrap.gap-2 {
  gap: 0 !important;
}

/* Mobile Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInLeft {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

/* Mobile Touch Target Improvements */
@media (max-width: 769px) {
  .ant-btn {
    min-height: 44px !important;
    min-width: 44px !important;
    touch-action: manipulation;
  }
  
  .ant-menu-item {
    height: 44px !important;
    line-height: 44px !important;
    padding: 0 16px !important;
    margin: 4px 8px !important;
    border-radius: 8px !important;
    touch-action: manipulation;
  }
  
  .ant-menu-submenu-title {
    height: 44px !important;
    line-height: 44px !important;
    padding: 0 16px !important;
    margin: 4px 8px !important;
    border-radius: 8px !important;
    touch-action: manipulation;
  }
  
  /* Enhanced touch areas for mobile */
  .ant-dropdown-trigger {
    min-height: 44px !important;
    touch-action: manipulation;
  }
  
  /* Improved table responsiveness on mobile */
  .ant-table-wrapper {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .ant-table {
    min-width: 600px;
  }
  
  /* Better form inputs on mobile */
  .ant-input,
  .ant-select-selector {
    min-height: 44px !important;
    font-size: 16px !important; /* Prevents zoom on iOS */
    touch-action: manipulation;
  }
  
  /* Better modal handling on mobile */
  .ant-modal {
    margin: 0 !important;
    max-width: 100% !important;
    height: 100% !important;
    top: 0 !important;
    padding-bottom: 0 !important;
  }
  
  .ant-modal-content {
    border-radius: 0 !important;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .ant-modal-body {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Enhanced mobile search */
  .mobile-search .ant-select-dropdown {
    border-radius: 12px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  }
  
  .mobile-search .ant-select-item {
    padding: 8px 12px !important;
    border-radius: 8px !important;
    margin: 4px 8px !important;
  }
  
  .mobile-search .ant-select-item:hover {
    background-color: rgba(24, 144, 255, 0.1) !important;
  }

  /* AutoComplete and Input alignment fixes */
  .nav-search-dropdown .ant-input {
    display: flex !important;
    align-items: center !important;
    line-height: 1 !important;
    height: 100% !important;
  }
  
  .nav-search-dropdown .ant-input::placeholder {
    line-height: 1 !important;
    vertical-align: middle !important;
  }
  
  .nav-search-dropdown .ant-input-affix-wrapper {
    display: flex !important;
    align-items: center !important;
    line-height: 1 !important;
  }
  
  .nav-search-dropdown .ant-input-prefix {
    display: flex !important;
    align-items: center !important;
  }

  /* Better scrollbars on mobile */
  * {
    -webkit-overflow-scrolling: touch;
  }
  
  /* Improved touch scrolling */
  .main-container {
    touch-action: pan-y pinch-zoom;
  }
  
  /* Better card layouts on mobile */
  .ant-card {
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  }
  
  .ant-card-head {
    padding: 12px 16px !important;
    min-height: 48px !important;
  }
  
  .ant-card-body {
    padding: 16px !important;
  }

  /* Responsive typography */
  h1, .ant-typography-h1 {
    font-size: 24px !important;
    line-height: 1.3 !important;
  }
  
  h2, .ant-typography-h2 {
    font-size: 20px !important;
    line-height: 1.3 !important;
  }
  
  h3, .ant-typography-h3 {
    font-size: 18px !important;
    line-height: 1.3 !important;
  }

  /* Better button spacing */
  .ant-btn + .ant-btn {
    margin-left: 8px;
  }
  
  /* Improved drawer for mobile */
  .ant-drawer-content {
    border-radius: 12px 12px 0 0 !important;
    overflow: hidden;
  }
  
  .ant-drawer-header {
    padding: 16px 20px !important;
    border-bottom: 1px solid #f0f0f0 !important;
  }
  
  .ant-drawer-body {
    padding: 20px !important;
  }

  /* Mobile-friendly pagination */
  .ant-pagination {
    text-align: center !important;
  }
  
  .ant-pagination-item,
  .ant-pagination-next,
  .ant-pagination-prev {
    min-width: 44px !important;
    height: 44px !important;
    line-height: 42px !important;
    margin: 0 4px !important;
  }
}

/* Enhanced Mobile Sidenav Overlay */
.mobile-sidenav-overlay {
  position: fixed;
  inset: 0;
  z-index: 199;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}

/* Mobile Sidenav Background Fix */
@media (max-width: 769px) {
  .mobile-sidenav-overlay .ant-layout-sider {
    background: #ffffff !important;
    backgroundColor: #ffffff !important;
    opacity: 1 !important;
    visibility: visible !important;
    height: 100vh !important;
    min-height: 100vh !important;
    max-height: 100vh !important;
  }
  
  .mobile-sidenav-overlay .ant-layout-sider-children {
    background: #ffffff !important;
    backgroundColor: #ffffff !important;
    opacity: 1 !important;
    height: 100% !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }
  
  .mobile-sidenav-overlay .ant-menu {
    background: #ffffff !important;
    backgroundColor: #ffffff !important;
    opacity: 1 !important;
    border: none !important;
    height: 100% !important;
  }
  
  .mobile-sidenav-overlay .ant-menu-item {
    background: transparent !important;
    color: #333333 !important;
  }
  
  .mobile-sidenav-overlay .ant-menu-item:hover {
    background: #f5f5f5 !important;
    color: #1890ff !important;
  }
  
  .mobile-sidenav-overlay .ant-menu-item-selected {
    background: #e6f7ff !important;
    color: #1890ff !important;
  }
  
  .mobile-sidenav-overlay .ant-menu-submenu-title {
    background: transparent !important;
    color: #333333 !important;
  }
  
  .mobile-sidenav-overlay .ant-menu-submenu-title:hover {
    background: #f5f5f5 !important;
    color: #1890ff !important;
  }
}

