// ** Router
import { Link } from 'react-router';


// ** Utils
import { PRIMARY_COLOR } from '@/utils/consts';

// ** Icons
import { RxDashboard } from 'react-icons/rx';
import {
  MdOutlineCorporateFare,
  MdOutlineModelTraining,
  MdOutlineAccountTree,
  MdOutlineAccountBalance,
} from 'react-icons/md';
import { FiUsers } from 'react-icons/fi';
import { IoMdTabletLandscape } from 'react-icons/io';
import { LuUsers, LuSmartphone } from 'react-icons/lu';
import { BsClockHistory, BsHouseDoor, BsSlack, BsFillInboxFill } from 'react-icons/bs';
import { FaWpforms, FaShuttleVan, FaFileInvoice, FaCog } from 'react-icons/fa';
import { BiChat } from 'react-icons/bi';
import { ImStatsDots } from 'react-icons/im';
import { TbReceiptTax, TbFileExport } from 'react-icons/tb';
import { HiOutlinePresentationChartBar } from "react-icons/hi";

// ** ==============================================================

const smartNav = (pathname: string) => (to: string) => {
  const isHttp = /^https?:\/\//.test(to);
  return {
    to,
    style: {
      color: pathname === to ? PRIMARY_COLOR : 'inherit', // Use inherit instead of black
      fontWeight: pathname === to ? 600 : 400,
      textDecoration: 'none',
    },
    onClick: (e: React.MouseEvent) => {
      if (!isHttp && pathname === to) e.preventDefault();
    },
  };
};

const link = (to: string, migrated = false): string => {
  const clean = to.replace(/^\/+/, '');
  if (migrated) return `/${clean}`;
  const base = import.meta.env.VITE_ADMIN_URL?.replace(/\/+$/, '') || '';
  return `${base}/${clean}`;
};

const buildItem = (
  nav: ReturnType<typeof smartNav>,
  to: string,
  label: string,
  key: string,
  icon?: React.ReactNode,
  migrated = false
) => ({
  key,
  icon,
  label: <Link {...nav(link(to, migrated))}>{label}</Link>,
});

const buildGroup = (label: string, key: string, icon: React.ReactNode, children: any[]) => ({
  key,
  icon,
  label,
  children,
});


// ** Menu items
export type MenuItem = {
  key: string;
  icon?: React.ReactNode;
  label: React.ReactNode;
  children?: MenuItem[];
  type?: 'group';
};

export const getMenuItems = (pathname: string) => {

  const nav = smartNav(pathname);

  return [
    buildGroup('Dashboard', 'dashboard', <RxDashboard />, [
      buildItem(nav, '/', 'Home', 'dashboard-home', <RxDashboard />, true),
      buildItem(nav, '/inbox', 'Inbox', 'dashboard-inbox', <BsFillInboxFill />, true),
      buildItem(nav, '/slack', '#Slack', 'dashboard-slack', <BsSlack />, true),
    ]),
    buildItem(nav, '/clients', 'Clients', 'clients', <MdOutlineCorporateFare />, true),
    buildGroup('Employees', 'employees', <LuUsers />, [
      buildItem(nav, '/employees', 'Employees', 'employees-all', <LuUsers />, true),
      buildItem(nav, '/applications', 'Apps & Documents', 'applications', <FaWpforms />, true),
      buildItem(nav, '/communications', 'Communication Center', 'communications', <BiChat />, true),
      buildItem(nav, '/trainings', 'Online Trainings', 'trainings', <MdOutlineModelTraining />, true),
      buildItem(nav, '/transportation', 'Transportation', 'transportation', <FaShuttleVan />, true),
      buildItem(nav, '/housing', 'Housing', 'housing', <BsHouseDoor />, true),
    ]),
    buildGroup('Finance & Taxes', 'finance-taxes', <MdOutlineAccountBalance />, [
      buildItem(nav, '/finance', 'Finance', 'finance', undefined, true),
      buildItem(nav, '/finance/invoices', 'Invoices', 'invoices', <FaFileInvoice />, true),
      buildItem(nav, '/finance/statistics', 'Financial Statistics', 'finance-statistics', <ImStatsDots />, true),
    ]),
    buildGroup('App & Devices', 'devices', <IoMdTabletLandscape />, [
      buildItem(nav, '/app-devices/mobile-app', 'Mobile App Management', 'mobile-app', <LuSmartphone />, true),
      buildItem(nav, '/app-devices/timeclocks', 'Timeclock Devices', 'timeclock-devices', <IoMdTabletLandscape />, true),
    ]),
    buildGroup('Reports', 'reports', <ImStatsDots />, [
      buildItem(nav, '/reports/statistics', 'System Statistics', 'reports-statistics', undefined, true),
      buildItem(nav, '/reports/payroll-reports', 'Payroll Reports', 'reports-payroll', <BsClockHistory />, true),
      buildItem(nav, '/reports/timeclock-reports', 'Timecard Reports', 'reports-timecard', <FiUsers />, true),
      buildItem(nav, '/reports/other-reports', 'Other Reports', 'reports-other', <TbFileExport />, true),
    ]),
    buildGroup('Settings', 'settings', <FaCog />, [
      buildItem(nav, '/settings/payroll-taxes', 'Payroll Tax Forms & W-2s', 'payroll-taxes', <TbReceiptTax />),
      buildItem(nav, '/settings/system-logs', 'System Logs', 'system-logs', <BsClockHistory />),
      buildItem(nav, '/settings/accounts', 'Accounts', 'accounts', <MdOutlineAccountTree />),
      buildItem(nav, '/settings/system-users', 'System Users', 'system-users', <FiUsers />, true),
      buildItem(nav, '/settings/system-settings', 'System Settings', 'system-settings', <FaCog />),
      buildItem(nav, '/settings/system-monitor', 'System Monitor', 'system-monitor', <HiOutlinePresentationChartBar />, true),
    ]),
  ];
};
