import React, { useState, useCallback } from 'react';
import type { LoginFormComponentProps, FormFieldComponentProps } from '../../types/login-components';
import type { LoginFormData, Role } from '../../types/auth';
import {
  MailIcon,
  LockClosedIcon,
  EyeIcon,
  EyeSlashIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';

// Form Field Component
const FormField: React.FC<FormFieldComponentProps> = ({
  label,
  name,
  type = 'text',
  placeholder,
  required = false,
  disabled = false,
  value,
  onChange,
  onBlur,
  onFocus,
  error,
  icon,
  suffix,
  className = '',
  style,
  autoComplete,
  'aria-label': ariaLabel,
  'aria-describedby': ariaDescribedBy,
}) => {
  const fieldId = `field-${name}`;
  const errorId = `${fieldId}-error`;

  return (
    <div className={`space-y-2 ${className}`} style={style}>
      <label
        htmlFor={fieldId}
        className="block text-sm font-medium text-slate-700"
      >
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>

      <div className="relative">
        {icon && (
          <div style={{
            position: 'absolute',
            left: '12px',
            top: '50%',
            transform: 'translateY(-50%)',
            color: 'var(--slate-400)',
            pointerEvents: 'none'
          }}>
            {icon}
          </div>
        )}

        <input
          id={fieldId}
          name={name}
          type={type}
          value={value || ''}
          onChange={(e) => onChange?.(e.target.value)}
          onBlur={onBlur}
          onFocus={onFocus}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          autoComplete={autoComplete}
          aria-label={ariaLabel || label}
          aria-describedby={error ? errorId : ariaDescribedBy}
          aria-invalid={error ? 'true' : 'false'}
          className={`vea-input ${error ? 'error' : ''}`}
          style={{
            paddingLeft: icon ? '40px' : '16px',
            paddingRight: suffix ? '40px' : '16px',
            opacity: disabled ? 0.5 : 1,
            cursor: disabled ? 'not-allowed' : 'text',
          }}
        />

        {suffix && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {suffix}
          </div>
        )}
      </div>

      {error && (
        <p id={errorId} className="vea-text-error" role="alert">
          {error}
        </p>
      )}
    </div>
  );
};

// Select Field Component
const SelectField: React.FC<{
  label: string;
  name: string;
  value: any;
  onChange: (value: any) => void;
  options: Array<{ label: string; value: any }>;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  icon?: React.ReactNode;
}> = ({
  label,
  name,
  value,
  onChange,
  options,
  placeholder,
  required = false,
  disabled = false,
  error,
  icon,
}) => {
    const fieldId = `field-${name}`;
    const errorId = `${fieldId}-error`;

    return (
      <div className="space-y-2">
        <label
          htmlFor={fieldId}
          className="block text-sm font-medium text-slate-700"
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>

        <div className="relative">
          {icon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 z-10">
              {icon}
            </div>
          )}

          <select
            id={fieldId}
            name={name}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            disabled={disabled}
            required={required}
            aria-label={label}
            aria-describedby={error ? errorId : undefined}
            aria-invalid={error ? 'true' : 'false'}
            className={`vea-select ${error ? 'error' : ''}`}
            style={{
              paddingLeft: icon ? '40px' : '16px',
              opacity: disabled ? 0.5 : 1,
              cursor: disabled ? 'not-allowed' : 'pointer',
            }}
          >
            {placeholder && (
              <option value="" disabled>
                {placeholder}
              </option>
            )}
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>

          <ChevronDownIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400 pointer-events-none" />
        </div>

        {error && (
          <p id={errorId} className="vea-text-error" role="alert">
            {error}
          </p>
        )}
      </div>
    );
  };

// Password Field Component
const PasswordField: React.FC<{
  label: string;
  name: string;
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  onFocus?: () => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  autoComplete?: string;
}> = ({
  label,
  name,
  value,
  onChange,
  onBlur,
  onFocus,
  placeholder,
  required = false,
  disabled = false,
  error,
  autoComplete,
}) => {
    const [showPassword, setShowPassword] = useState(false);

    const togglePasswordVisibility = useCallback(() => {
      setShowPassword(prev => !prev);
    }, []);

    return (
      <FormField
        label={label}
        name={name}
        type={showPassword ? 'text' : 'password'}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        onFocus={onFocus}
        placeholder={placeholder}
        required={required}
        disabled={disabled}
        error={error}
        autoComplete={autoComplete}
        icon={<LockClosedIcon className="w-5 h-5" />}
        suffix={
          <button
            type="button"
            onClick={togglePasswordVisibility}
            className="text-slate-400 hover:text-slate-600 focus:outline-none focus:text-slate-600 transition-colors duration-200"
            aria-label={showPassword ? 'Hide password' : 'Show password'}
          >
            {showPassword ? (
              <EyeSlashIcon className="w-5 h-5" />
            ) : (
              <EyeIcon className="w-5 h-5" />
            )}
          </button>
        }
      />
    );
  };

// Checkbox Field Component
const CheckboxField: React.FC<{
  label: string;
  name: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
}> = ({ label, name, checked, onChange, disabled = false }) => {
  const fieldId = `field-${name}`;

  return (
    <div className="flex items-center space-x-3">
      <input
        id={fieldId}
        name={name}
        type="checkbox"
        checked={checked}
        onChange={(e) => onChange(e.target.checked)}
        disabled={disabled}
        className={`
          vea-checkbox vea-touch-target
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
      />
      <label
        htmlFor={fieldId}
        className={`
          text-sm text-slate-700 select-none
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
      >
        {label}
      </label>
    </div>
  );
};

// Main Login Form Component
const LoginForm: React.FC<LoginFormComponentProps> = ({
  onSubmit,
  loading,
  error,
  roles,
  initialValues = {},
  className = '',
  style,
  autoComplete = true,
  validateOnChange = false,
  validateOnBlur = true,
}) => {
  const [formData, setFormData] = useState<LoginFormData>({
    role: initialValues.role || '',
    email: initialValues.email || '',
    password: initialValues.password || '',
    rememberMe: initialValues.rememberMe || false,
  });

  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Validation functions
  const validateEmail = (email: string): string | null => {
    if (!email) return 'Email is required';
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) return 'Please enter a valid email address';
    return null;
  };

  const validatePassword = (password: string): string | null => {
    if (!password) return 'Password is required';
    if (password.length < 6) return 'Password must be at least 6 characters';
    return null;
  };

  const validateRole = (role: any): string | null => {
    if (!role) return 'Please select an access level';
    return null;
  };

  // Handle field changes
  const handleFieldChange = useCallback((field: keyof LoginFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    if (validateOnChange) {
      let error: string | null = null;
      switch (field) {
        case 'email':
          error = validateEmail(value);
          break;
        case 'password':
          error = validatePassword(value);
          break;
        case 'role':
          error = validateRole(value);
          break;
      }

      setFieldErrors(prev => ({
        ...prev,
        [field]: error || '',
      }));
    }
  }, [validateOnChange]);

  // Handle field blur
  const handleFieldBlur = useCallback((field: keyof LoginFormData) => {
    setTouched(prev => ({ ...prev, [field]: true }));

    if (validateOnBlur) {
      let error: string | null = null;
      const value = formData[field];

      switch (field) {
        case 'email':
          error = validateEmail(value as string);
          break;
        case 'password':
          error = validatePassword(value as string);
          break;
        case 'role':
          error = validateRole(value);
          break;
      }

      setFieldErrors(prev => ({
        ...prev,
        [field]: error || '',
      }));
    }
  }, [formData, validateOnBlur]);

  // Handle form submission
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate all fields
    const errors: Record<string, string> = {};

    const emailError = validateEmail(formData.email);
    if (emailError) errors.email = emailError;

    const passwordError = validatePassword(formData.password);
    if (passwordError) errors.password = passwordError;

    const roleError = validateRole(formData.role);
    if (roleError) errors.role = roleError;

    setFieldErrors(errors);

    // Mark all fields as touched
    setTouched({
      email: true,
      password: true,
      role: true,
      rememberMe: true,
    });

    // If there are errors, don't submit
    if (Object.keys(errors).length > 0) {
      return;
    }

    // Submit the form
    try {
      await onSubmit(formData);
    } catch (err) {
      // Error handling is done by parent component
    }
  }, [formData, onSubmit]);

  const roleOptions = roles
    .filter(role => role.active)
    .map(role => ({
      label: role.short_name,
      value: role.id,
    }));

  return (
    <form
      onSubmit={handleSubmit}
      className={`space-y-6 ${className}`}
      style={style}
      noValidate={!autoComplete}
    >
      {/* Access Level Dropdown */}
      <SelectField
        label="Access Level"
        name="role"
        value={formData.role}
        onChange={(value) => handleFieldChange('role', parseInt(value))}
        options={roleOptions}
        placeholder="Select your access level"
        required
        error={touched.role ? fieldErrors.role : ''}
      />

      {/* Email Field */}
      <FormField
        label="Email Address"
        name="email"
        type="email"
        value={formData.email}
        onChange={(value) => handleFieldChange('email', value)}
        onBlur={() => handleFieldBlur('email')}
        placeholder="Enter your email address"
        required
        autoComplete={autoComplete ? 'email' : 'off'}
        error={touched.email ? fieldErrors.email : ''}
        icon={<MailIcon className="w-5 h-5" />}
      />

      {/* Password Field */}
      <PasswordField
        label="Password"
        name="password"
        value={formData.password}
        onChange={(value) => handleFieldChange('password', value)}
        onBlur={() => handleFieldBlur('password')}
        placeholder="Enter your password"
        required
        autoComplete={autoComplete ? 'current-password' : 'off'}
        error={touched.password ? fieldErrors.password : ''}
      />

      {/* Remember Me Checkbox */}
      <CheckboxField
        label="Remember me for 7 days"
        name="rememberMe"
        checked={formData.rememberMe}
        onChange={(checked) => handleFieldChange('rememberMe', checked)}
        disabled={loading}
      />

      {/* General Error Message */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="vea-text-error text-center">{error}</p>
        </div>
      )}

      {/* Submit Button */}
      <button
        type="submit"
        disabled={loading}
        className={`
          vea-button-primary vea-touch-target
          ${loading ? 'vea-loading' : ''}
        `}
      >
        {loading ? (
          <div className="flex items-center justify-center space-x-2">
            <div className="vea-spinner" />
            <span>Signing in...</span>
          </div>
        ) : (
          'Sign In'
        )}
      </button>
    </form>
  );
};

export default React.memo(LoginForm);
