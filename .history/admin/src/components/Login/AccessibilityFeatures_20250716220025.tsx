import React, { useEffect, useRef, useCallback } from 'react';
import type { FocusTrapProps, SkipLinkProps, ScreenReaderOnlyProps } from '../../types/login-components';

// Focus Trap Component for Modal-like Behavior
export const FocusTrap: React.FC<FocusTrapProps> = ({
  active,
  children,
  initialFocus,
  returnFocus = true,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const previousActiveElementRef = useRef<HTMLElement | null>(null);

  // Get all focusable elements within the container
  const getFocusableElements = useCallback((): HTMLElement[] => {
    if (!containerRef.current) return [];

    const focusableSelectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]',
    ].join(', ');

    return Array.from(containerRef.current.querySelectorAll(focusableSelectors));
  }, []);

  // Handle keydown events for focus trapping
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!active || event.key !== 'Tab') return;

    const focusableElements = getFocusableElements();
    if (focusableElements.length === 0) return;

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    if (event.shiftKey) {
      // Shift + Tab (backward)
      if (document.activeElement === firstElement) {
        event.preventDefault();
        lastElement.focus();
      }
    } else {
      // Tab (forward)
      if (document.activeElement === lastElement) {
        event.preventDefault();
        firstElement.focus();
      }
    }
  }, [active, getFocusableElements]);

  // Set up focus trap when active
  useEffect(() => {
    if (!active) return;

    // Store the previously focused element
    previousActiveElementRef.current = document.activeElement as HTMLElement;

    // Focus initial element or first focusable element
    const focusableElements = getFocusableElements();
    if (focusableElements.length > 0) {
      const initialElement = initialFocus
        ? containerRef.current?.querySelector(initialFocus) as HTMLElement
        : focusableElements[0];

      if (initialElement) {
        initialElement.focus();
      }
    }

    // Add event listener
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);

      // Return focus to previously focused element
      if (returnFocus && previousActiveElementRef.current) {
        previousActiveElementRef.current.focus();
      }
    };
  }, [active, initialFocus, returnFocus, handleKeyDown, getFocusableElements]);

  return (
    <div ref={containerRef} className="focus-trap-container">
      {children}
    </div>
  );
};

// Enhanced Skip Link Component
export const SkipLink: React.FC<SkipLinkProps> = ({
  href,
  children,
  className = '',
  style,
}) => {
  const handleClick = useCallback((event: React.MouseEvent) => {
    event.preventDefault();

    const target = document.querySelector(href);
    if (target) {
      // Make target focusable if it isn't already
      if (!target.hasAttribute('tabindex')) {
        target.setAttribute('tabindex', '-1');
      }

      (target as HTMLElement).focus();

      // Scroll to target
      target.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, [href]);

  return (
    <a
      href={href}
      onClick={handleClick}
      className={`
        absolute -top-10 left-4 z-50 px-4 py-2 
        bg-vea-green text-white font-medium rounded-lg
        transform -translate-y-full opacity-0
        focus:translate-y-0 focus:opacity-100
        transition-all duration-200 ease-out
        focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-vea-green
        ${className}
      `}
      style={style}
    >
      {children}
    </a>
  );
};

// Screen Reader Only Text Component
export const ScreenReaderOnly: React.FC<ScreenReaderOnlyProps> = ({
  children,
  className = '',
  style,
}) => {
  return (
    <span
      className={`
        absolute w-px h-px p-0 -m-px overflow-hidden 
        whitespace-nowrap border-0 clip-rect-0
        ${className}
      `}
      style={style}
    >
      {children}
    </span>
  );
};

// Live Region for Dynamic Content Announcements
export const LiveRegion: React.FC<{
  children: React.ReactNode;
  level?: 'polite' | 'assertive';
  atomic?: boolean;
  className?: string;
}> = ({
  children,
  level = 'polite',
  atomic = false,
  className = '',
}) => {
    return (
      <div
        aria-live={level}
        aria-atomic={atomic}
        className={`sr-only ${className}`}
      >
        {children}
      </div>
    );
  };

// Accessible Form Field with Enhanced Labels and Descriptions
export const AccessibleFormField: React.FC<{
  id: string;
  label: string;
  children: React.ReactNode;
  description?: string;
  error?: string;
  required?: boolean;
  className?: string;
}> = ({
  id,
  label,
  children,
  description,
  error,
  required = false,
  className = '',
}) => {
    const descriptionId = `${id}-description`;
    const errorId = `${id}-error`;

    return (
      <div className={`space-y-2 ${className}`}>
        <label
          htmlFor={id}
          className="block text-sm font-medium text-slate-700"
        >
          {label}
          {required && (
            <>
              <span className="text-red-500 ml-1" aria-hidden="true">*</span>
              <ScreenReaderOnly>required</ScreenReaderOnly>
            </>
          )}
        </label>

        {description && (
          <p id={descriptionId} className="text-sm text-slate-600">
            {description}
          </p>
        )}

        <div className="relative">
          {React.cloneElement(children as React.ReactElement, {
            id,
            'aria-describedby': [
              description ? descriptionId : '',
              error ? errorId : '',
            ].filter(Boolean).join(' ') || undefined,
            'aria-invalid': !!error,
            'aria-required': required,
          })}
        </div>

        {error && (
          <p id={errorId} className="text-sm text-red-600" role="alert">
            <ScreenReaderOnly>Error: </ScreenReaderOnly>
            {error}
          </p>
        )}
      </div>
    );
  };

// High Contrast Mode Detection and Styling
export const HighContrastProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const [isHighContrast, setIsHighContrast] = React.useState(false);

  React.useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    setIsHighContrast(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setIsHighContrast(e.matches);
    };

    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    } else {
      mediaQuery.addListener(handleChange);
      return () => mediaQuery.removeListener(handleChange);
    }
  }, []);

  return (
    <div className={isHighContrast ? 'high-contrast-mode' : ''}>
      {children}
    </div>
  );
};

// Reduced Motion Provider
export const ReducedMotionProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const [prefersReducedMotion, setPrefersReducedMotion] = React.useState(false);

  React.useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches);
    };

    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    } else {
      mediaQuery.addListener(handleChange);
      return () => mediaQuery.removeListener(handleChange);
    }
  }, []);

  return (
    <div className={prefersReducedMotion ? 'reduce-motion' : ''}>
      {children}
    </div>
  );
};

// Keyboard Navigation Helper
export const useKeyboardNavigation = (
  containerRef: React.RefObject<HTMLElement>,
  options: {
    enableArrowKeys?: boolean;
    enableHomeEnd?: boolean;
    enableTypeAhead?: boolean;
    onNavigate?: (element: HTMLElement) => void;
  } = {}
) => {
  const {
    enableArrowKeys = true,
    enableHomeEnd = true,
    enableTypeAhead = false,
    onNavigate,
  } = options;

  const typeAheadRef = useRef('');
  const typeAheadTimeoutRef = useRef<NodeJS.Timeout>();

  const getFocusableElements = useCallback((): HTMLElement[] => {
    if (!containerRef.current) return [];

    const focusableSelectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])',
      '[role="button"]:not([disabled])',
      '[role="menuitem"]:not([disabled])',
    ].join(', ');

    return Array.from(containerRef.current.querySelectorAll(focusableSelectors));
  }, []);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    const focusableElements = getFocusableElements();
    if (focusableElements.length === 0) return;

    const currentIndex = focusableElements.indexOf(document.activeElement as HTMLElement);
    let nextIndex = currentIndex;

    switch (event.key) {
      case 'ArrowDown':
        if (enableArrowKeys) {
          event.preventDefault();
          nextIndex = currentIndex < focusableElements.length - 1 ? currentIndex + 1 : 0;
        }
        break;

      case 'ArrowUp':
        if (enableArrowKeys) {
          event.preventDefault();
          nextIndex = currentIndex > 0 ? currentIndex - 1 : focusableElements.length - 1;
        }
        break;

      case 'Home':
        if (enableHomeEnd) {
          event.preventDefault();
          nextIndex = 0;
        }
        break;

      case 'End':
        if (enableHomeEnd) {
          event.preventDefault();
          nextIndex = focusableElements.length - 1;
        }
        break;

      default:
        if (enableTypeAhead && event.key.length === 1) {
          // Clear previous timeout
          if (typeAheadTimeoutRef.current) {
            clearTimeout(typeAheadTimeoutRef.current);
          }

          // Add character to search string
          typeAheadRef.current += event.key.toLowerCase();

          // Find matching element
          const matchingElement = focusableElements.find(element =>
            element.textContent?.toLowerCase().startsWith(typeAheadRef.current)
          );

          if (matchingElement) {
            nextIndex = focusableElements.indexOf(matchingElement);
          }

          // Clear search string after delay
          typeAheadTimeoutRef.current = setTimeout(() => {
            typeAheadRef.current = '';
          }, 1000);
        }
        break;
    }

    if (nextIndex !== currentIndex && focusableElements[nextIndex]) {
      focusableElements[nextIndex].focus();
      onNavigate?.(focusableElements[nextIndex]);
    }
  }, [getFocusableElements, enableArrowKeys, enableHomeEnd, enableTypeAhead, onNavigate]);

  React.useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener('keydown', handleKeyDown);

    return () => {
      container.removeEventListener('keydown', handleKeyDown);
      if (typeAheadTimeoutRef.current) {
        clearTimeout(typeAheadTimeoutRef.current);
      }
    };
  }, [handleKeyDown]);
};
