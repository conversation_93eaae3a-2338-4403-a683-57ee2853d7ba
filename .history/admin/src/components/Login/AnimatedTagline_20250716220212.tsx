import React, { useState, useEffect, useCallback, useRef } from 'react';
import { TypewriterEffectProps, AnimatedTaglineProps } from '@/types/login-components';

// Default configuration for typewriter effect
const DEFAULT_CONFIG = {
  speed: 80, // milliseconds per character
  startDelay: 1000, // delay before starting
  cursorChar: '|',
  showCursor: true,
  cursorBlinkSpeed: 1000, // milliseconds
};

const TypewriterEffect: React.FC<TypewriterEffectProps> = ({
  text,
  speed = DEFAULT_CONFIG.speed,
  startDelay = DEFAULT_CONFIG.startDelay,
  cursorChar = DEFAULT_CONFIG.cursorChar,
  showCursor = DEFAULT_CONFIG.showCursor,
  cursorBlinkSpeed = DEFAULT_CONFIG.cursorBlinkSpeed,
  className = '',
  onStart,
  onComplete,
  onCharacterTyped,
}) => {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTyping, setIsTyping] = useState(false);
  const [showCursorState, setShowCursorState] = useState(true);
  const [isComplete, setIsComplete] = useState(false);

  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const cursorTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Cursor blinking effect
  useEffect(() => {
    if (!showCursor) return;

    const blinkCursor = () => {
      setShowCursorState(prev => !prev);
      cursorTimeoutRef.current = setTimeout(blinkCursor, cursorBlinkSpeed);
    };

    cursorTimeoutRef.current = setTimeout(blinkCursor, cursorBlinkSpeed);

    return () => {
      if (cursorTimeoutRef.current) {
        clearTimeout(cursorTimeoutRef.current);
      }
    };
  }, [showCursor, cursorBlinkSpeed]);

  // Typewriter effect
  const typeNextCharacter = useCallback(() => {
    if (currentIndex >= text.length) {
      setIsTyping(false);
      setIsComplete(true);
      onComplete?.();
      return;
    }

    const nextChar = text[currentIndex];
    setDisplayedText(prev => prev + nextChar);
    setCurrentIndex(prev => prev + 1);
    onCharacterTyped?.(nextChar, currentIndex);

    typingTimeoutRef.current = setTimeout(typeNextCharacter, speed);
  }, [currentIndex, text, speed, onCharacterTyped, onComplete]);

  // Start typing effect
  const startTyping = useCallback(() => {
    setIsTyping(true);
    onStart?.();
    typeNextCharacter();
  }, [typeNextCharacter, onStart]);

  // Initialize typing effect
  useEffect(() => {
    // Reset state when text changes
    setDisplayedText('');
    setCurrentIndex(0);
    setIsTyping(false);
    setIsComplete(false);

    // Clear existing timeouts
    if (typingTimeoutRef.current) clearTimeout(typingTimeoutRef.current);
    if (startTimeoutRef.current) clearTimeout(startTimeoutRef.current);

    // Start typing after delay
    startTimeoutRef.current = setTimeout(startTyping, startDelay);

    return () => {
      if (typingTimeoutRef.current) clearTimeout(typingTimeoutRef.current);
      if (startTimeoutRef.current) clearTimeout(startTimeoutRef.current);
    };
  }, [text, startDelay, startTyping]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) clearTimeout(typingTimeoutRef.current);
      if (cursorTimeoutRef.current) clearTimeout(cursorTimeoutRef.current);
      if (startTimeoutRef.current) clearTimeout(startTimeoutRef.current);
    };
  }, []);

  return (
    <span className={`inline-block ${className}`}>
      <span className="text-shadow-soft">
        {displayedText}
      </span>
      {showCursor && (
        <span
          className={`inline-block ml-1 transition-opacity duration-100 ${showCursorState ? 'opacity-100' : 'opacity-0'
            }`}
          aria-hidden="true"
        >
          {cursorChar}
        </span>
      )}
    </span>
  );
};

const AnimatedTagline: React.FC<AnimatedTaglineProps> = ({
  taglines = ['The Smarter Way to Manage Staffing.'],
  rotationInterval = 8000,
  typewriterConfig = {},
  className = '',
  style,
}) => {
  const [currentTaglineIndex, setCurrentTaglineIndex] = useState(0);
  const [key, setKey] = useState(0); // Force re-render of TypewriterEffect
  const rotationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const currentTagline = taglines[currentTaglineIndex] || taglines[0];

  // Rotate taglines if multiple are provided
  useEffect(() => {
    if (taglines.length <= 1) return;

    const rotateTagline = () => {
      setCurrentTaglineIndex(prev => (prev + 1) % taglines.length);
      setKey(prev => prev + 1); // Force TypewriterEffect to restart
    };

    rotationTimeoutRef.current = setTimeout(rotateTagline, rotationInterval);

    return () => {
      if (rotationTimeoutRef.current) {
        clearTimeout(rotationTimeoutRef.current);
      }
    };
  }, [currentTaglineIndex, taglines.length, rotationInterval]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (rotationTimeoutRef.current) {
        clearTimeout(rotationTimeoutRef.current);
      }
    };
  }, []);

  const handleTypewriterComplete = useCallback(() => {
    // If there are multiple taglines, set up rotation
    if (taglines.length > 1) {
      rotationTimeoutRef.current = setTimeout(() => {
        setCurrentTaglineIndex(prev => (prev + 1) % taglines.length);
        setKey(prev => prev + 1);
      }, rotationInterval - (typewriterConfig.speed || DEFAULT_CONFIG.speed) * currentTagline.length);
    }
  }, [taglines.length, rotationInterval, typewriterConfig.speed, currentTagline.length]);

  return (
    <div
      className={`vea-animated-text vea-hero-text ${className}`}
      style={style}
      role="banner"
      aria-live="polite"
      aria-label="VEA Timeclock tagline"
    >
      <TypewriterEffect
        key={key}
        text={currentTagline}
        speed={typewriterConfig.speed || DEFAULT_CONFIG.speed}
        startDelay={typewriterConfig.startDelay || DEFAULT_CONFIG.startDelay}
        cursorChar={typewriterConfig.cursorChar || DEFAULT_CONFIG.cursorChar}
        showCursor={typewriterConfig.showCursor ?? DEFAULT_CONFIG.showCursor}
        cursorBlinkSpeed={typewriterConfig.cursorBlinkSpeed || DEFAULT_CONFIG.cursorBlinkSpeed}
        className={typewriterConfig.className || ''}
        onStart={typewriterConfig.onStart}
        onComplete={handleTypewriterComplete}
        onCharacterTyped={typewriterConfig.onCharacterTyped}
      />
    </div>
  );
};

export default React.memo(AnimatedTagline);
