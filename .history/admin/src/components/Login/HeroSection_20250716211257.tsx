import React, { useState, useEffect, useCallback } from 'react';
import { HeroSectionComponentProps, HeroImageData } from '@/types/login-components';
import { Account } from '@/types/auth';
import { CDN_URL } from '@/utils/consts';
import { OptimizedImage, useLazyLoading, measureLoginPerformance, preloadResources } from '@/utils/loginPerformance';

// Professional workplace images for hero section
const DEFAULT_HERO_IMAGES: HeroImageData[] = [
  {
    id: 'office-1',
    url: 'https://images.unsplash.com/photo-*************-37526070297c?w=1200&h=800&fit=crop&crop=center',
    webpUrl: 'https://images.unsplash.com/photo-*************-37526070297c?w=1200&h=800&fit=crop&crop=center&fm=webp',
    alt: 'Modern office workspace with professionals collaborating',
  },
  {
    id: 'office-2',
    url: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=1200&h=800&fit=crop&crop=center',
    webpUrl: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=1200&h=800&fit=crop&crop=center&fm=webp',
    alt: 'Professional team meeting in conference room',
  },
  {
    id: 'office-3',
    url: 'https://images.unsplash.com/photo-*************-e3b97375f902?w=1200&h=800&fit=crop&crop=center',
    webpUrl: 'https://images.unsplash.com/photo-*************-e3b97375f902?w=1200&h=800&fit=crop&crop=center&fm=webp',
    alt: 'Diverse team working together in modern office',
  },
  {
    id: 'office-4',
    url: 'https://images.unsplash.com/photo-1556761175-b413da4baf72?w=1200&h=800&fit=crop&crop=center',
    webpUrl: 'https://images.unsplash.com/photo-1556761175-b413da4baf72?w=1200&h=800&fit=crop&crop=center&fm=webp',
    alt: 'Professional workspace with technology and collaboration',
  },
  {
    id: 'office-5',
    url: 'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?w=1200&h=800&fit=crop&crop=center',
    webpUrl: 'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?w=1200&h=800&fit=crop&crop=center&fm=webp',
    alt: 'Business professionals in productive work environment',
  },
  {
    id: 'office-6',
    url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=1200&h=800&fit=crop&crop=center',
    webpUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=1200&h=800&fit=crop&crop=center&fm=webp',
    alt: 'Modern staffing and workforce management office',
  },
];

const ROTATION_INTERVAL = 5000; // 5 seconds
const FADE_DURATION = 1000; // 1 second

interface HeroImageProps {
  image: HeroImageData;
  isActive: boolean;
  onLoad: () => void;
  onError: (error: Error) => void;
}

const HeroImage: React.FC<HeroImageProps> = ({ image, isActive, onLoad, onError }) => {
  const { ref, isIntersecting } = useLazyLoading(0.1, '100px');

  return (
    <div
      ref={ref as React.RefObject<HTMLDivElement>}
      className={`
        absolute inset-0 transition-opacity duration-1000 ease-in-out
        ${isActive ? 'opacity-100' : 'opacity-0'}
      `}
    >
      {(isIntersecting || isActive) && (
        <OptimizedImage
          src={image.url}
          webpSrc={image.webpUrl}
          alt={image.alt}
          className="vea-hero-image"
          onLoad={onLoad}
          onError={onError}
          loading={isActive ? 'eager' : 'lazy'}
          decoding="async"
          quality={85}
        />
      )}
    </div>
  );
};

const HeroSection: React.FC<HeroSectionComponentProps> = ({
  account,
  className = '',
  style,
  onImageLoad,
  onImageError
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [images, setImages] = useState<HeroImageData[]>([]);
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());

  // Initialize images based on account wallpapers or default images
  useEffect(() => {
    if (account?.wallpapers?.length) {
      const accountImages: HeroImageData[] = account.wallpapers.map((wallpaper, index) => ({
        id: `account-${index}`,
        url: `${CDN_URL}/accounts/${account.id}/wallpapers/${wallpaper}`,
        alt: `${account.system_name || 'VEA Timeclock'} workspace image ${index + 1}`,
      }));
      setImages(accountImages);
    } else {
      setImages(DEFAULT_HERO_IMAGES);
    }
  }, [account]);

  // Image rotation logic
  useEffect(() => {
    if (images.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, ROTATION_INTERVAL);

    return () => clearInterval(interval);
  }, [images.length]);

  // Preload next image
  useEffect(() => {
    if (images.length <= 1) return;

    const nextIndex = (currentImageIndex + 1) % images.length;
    const nextImage = images[nextIndex];

    if (nextImage && !loadedImages.has(nextImage.id)) {
      const img = new Image();
      img.src = nextImage.webpUrl || nextImage.url;
    }
  }, [currentImageIndex, images, loadedImages]);

  const handleImageLoad = useCallback((imageId: string) => {
    setLoadedImages(prev => new Set(prev).add(imageId));
    onImageLoad?.(imageId);
  }, [onImageLoad]);

  const handleImageError = useCallback((imageId: string, error: Error) => {
    setImageErrors(prev => new Set(prev).add(imageId));
    onImageError?.(imageId, error);
  }, [onImageError]);

  if (!images.length) {
    return (
      <div className={`vea-hero-section bg-vea-green ${className}`} style={style}>
        <div className="vea-hero-overlay" />
        <div className="vea-hero-shadow" />
      </div>
    );
  }

  return (
    <div className={`vea-hero-section ${className}`} style={style}>
      {/* Image Container */}
      <div className="relative w-full h-full overflow-hidden">
        {images.map((image, index) => (
          <HeroImage
            key={image.id}
            image={image}
            isActive={index === currentImageIndex}
            onLoad={() => handleImageLoad(image.id)}
            onError={(error) => handleImageError(image.id, error)}
          />
        ))}
      </div>

      {/* Dark Gradient Overlay */}
      <div className="vea-hero-overlay" />

      {/* Right Shadow for Text Readability */}
      <div className="vea-hero-shadow" />

      {/* Loading Indicator for First Image */}
      {!loadedImages.has(images[currentImageIndex]?.id) && (
        <div className="absolute inset-0 flex items-center justify-center bg-vea-green">
          <div className="vea-spinner" />
        </div>
      )}

      {/* Image Navigation Dots (Optional) */}
      {images.length > 1 && (
        <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2 z-30">
          {images.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentImageIndex(index)}
              className={`
                w-2 h-2 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-black/20
                ${index === currentImageIndex
                  ? 'bg-white scale-125'
                  : 'bg-white/50 hover:bg-white/75'
                }
              `}
              aria-label={`Go to image ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default React.memo(HeroSection);
