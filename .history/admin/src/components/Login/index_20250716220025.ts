// Login Components Exports
export { default as HeroSection } from './HeroSection';
export { default as AnimatedTagline } from './AnimatedTagline';
export { default as LoginForm } from './LoginForm';
export { default as OptimizedImage } from './OptimizedImage';
export {
  LoginLayout,
  SplitLayout,
  FormContainer,
  useResponsive,
  MediaQuery,
  ResponsiveContainer,
  TouchButton,
  SkipLink,
  ScreenReaderOnly
} from './LoginLayout';
export {
  ErrorMessage,
  LoadingSpinner,
  ValidationMessage,
  FormErrorSummary,
  SuccessMessage,
  LoadingOverlay,
  ErrorBoundary
} from './ErrorHandling';

// Re-export hooks
export { useImageRotation } from '@/hooks/useImageRotation';
export { useTypewriter } from '@/hooks/useTypewriter';
export { useLoginForm } from '@/hooks/useLoginForm';
export {
  useResponsive as useResponsiveHook,
  useMediaQuery,
  useTouchDevice,
  useOrientation,
  useSafeArea,
  useReducedMotion,
  useHighContrast
} from '@/hooks/useResponsive';

// Re-export types
export type {
  HeroSectionComponentProps,
  AnimatedTaglineProps,
  LoginFormComponentProps,
  LoginLayoutProps,
  SplitLayoutProps,
  FormContainerProps,
  ErrorMessageProps,
  LoadingSpinnerProps,
  ValidationMessageProps
} from '@/types/login-components';

export type {
  LoginFormData,
  LoginFormErrors,
  AuthenticationResponse,
  User,
  Account,
  Role,
  LoginComponentProps
} from '@/types/auth';
