import React, { useState, useCallback } from 'react';
import { generateWebPUrl } from '../../utils/loginPerformance';

// Image optimization options interface
export interface OptimizedImageOptions {
  src: string;
  webpSrc?: string;
  alt: string;
  width?: number;
  height?: number;
  quality?: number;
  loading?: 'lazy' | 'eager';
  decoding?: 'async' | 'sync' | 'auto';
  sizes?: string;
  srcSet?: string;
}

// Optimized Image Component props interface
export interface OptimizedImageProps extends OptimizedImageOptions {
  className?: string;
  style?: React.CSSProperties;
  onLoad?: () => void;
  onError?: (error: Error) => void;
}

// Optimized Image Component with WebP support and lazy loading
export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  webpSrc,
  alt,
  width,
  height,
  quality = 80,
  loading = 'lazy',
  decoding = 'async',
  sizes,
  srcSet,
  className = '',
  style,
  onLoad,
  onError,
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleLoad = useCallback(() => {
    setImageLoaded(true);
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback(() => {
    setImageError(true);
    onError?.(new Error(`Failed to load image: ${src}`));
  }, [src, onError]);

  // Generate WebP source if not provided
  const webpSource = webpSrc || generateWebPUrl(src, quality);

  return (
    <picture className={className} style={style}>
      {/* WebP source for modern browsers */}
      <source
        srcSet={webpSource}
        type="image/webp"
        sizes={sizes}
      />

      {/* Fallback for older browsers */}
      <img
        src={src}
        srcSet={srcSet}
        alt={alt}
        width={width}
        height={height}
        loading={loading}
        decoding={decoding}
        sizes={sizes}
        onLoad={handleLoad}
        onError={handleError}
        style={{
          transition: 'opacity 0.3s ease',
          opacity: imageLoaded ? 1 : 0,
          backgroundColor: imageError ? '#e2e8f0' : 'transparent',
        }}
      />
    </picture>
  );
};

export default OptimizedImage;
