import React, { useState, useCallback } from 'react';
import { OptimizedImageProps, generateWebPUrl } from '@/utils/loginPerformance';

// Optimized Image Component with WebP support and lazy loading
export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  webpSrc,
  alt,
  width,
  height,
  quality = 80,
  loading = 'lazy',
  decoding = 'async',
  sizes,
  srcSet,
  className = '',
  style,
  onLoad,
  onError,
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleLoad = useCallback(() => {
    setImageLoaded(true);
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback(() => {
    setImageError(true);
    onError?.(new Error(`Failed to load image: ${src}`));
  }, [src, onError]);

  // Generate WebP source if not provided
  const webpSource = webpSrc || generateWebPUrl(src, quality);

  return (
    <picture className={className} style={style}>
      {/* WebP source for modern browsers */}
      <source 
        srcSet={webpSource} 
        type="image/webp"
        sizes={sizes}
      />
      
      {/* Fallback for older browsers */}
      <img
        src={src}
        srcSet={srcSet}
        alt={alt}
        width={width}
        height={height}
        loading={loading}
        decoding={decoding}
        sizes={sizes}
        onLoad={handleLoad}
        onError={handleError}
        className={`
          transition-opacity duration-300
          ${imageLoaded ? 'opacity-100' : 'opacity-0'}
          ${imageError ? 'bg-slate-200' : ''}
        `}
      />
    </picture>
  );
};

export default OptimizedImage;
