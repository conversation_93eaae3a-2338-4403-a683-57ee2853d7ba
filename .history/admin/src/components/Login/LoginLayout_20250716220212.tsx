import React from 'react';
import { LoginLayoutProps, SplitLayoutProps, FormContainerProps } from '@/types/login-components';

// Main Login Layout Container
const LoginLayout: React.FC<LoginLayoutProps> = ({
  children,
  className = '',
  style,
  maxWidth = '1400px',
  centered = true,
  responsive = true,
}) => {
  return (
    <div
      className={`
        ${responsive ? 'vea-container' : ''}
        ${centered ? 'mx-auto' : ''}
        ${className}
      `}
      style={{
        maxWidth: responsive ? undefined : maxWidth,
        ...style,
      }}
    >
      {children}
    </div>
  );
};

// Split Layout for Hero and Form Sections
const SplitLayout: React.FC<SplitLayoutProps> = ({
  leftPanel,
  rightPanel,
  leftWidth = '60%',
  rightWidth = '40%',
  mobileStack = true,
  className = '',
  style,
}) => {
  return (
    <div
      className={`
        flex min-h-screen-safe
        ${mobileStack ? 'flex-col lg:flex-row' : 'flex-row'}
        ${className}
      `}
      style={style}
    >
      {/* Left Panel - Hero Section */}
      <div
        className={`
          ${mobileStack ? 'h-[30vh] lg:h-screen' : 'h-screen'}
          ${mobileStack ? 'w-full lg:flex-1' : 'flex-1'}
          relative overflow-hidden
        `}
        style={{
          width: mobileStack ? undefined : leftWidth,
        }}
      >
        {leftPanel}
      </div>

      {/* Right Panel - Form Section */}
      <div
        className={`
          ${mobileStack ? 'flex-1 lg:flex-none' : 'flex-none'}
          ${mobileStack ? 'min-h-[70vh] lg:h-screen' : 'h-screen'}
          bg-slate-50 relative
        `}
        style={{
          width: mobileStack ? undefined : rightWidth,
        }}
      >
        {rightPanel}
      </div>
    </div>
  );
};

// Form Container with Logo and Styling
const FormContainer: React.FC<FormContainerProps> = ({
  children,
  title,
  subtitle,
  logo,
  footer,
  maxWidth = '400px',
  padding = '2rem',
  className = '',
  style,
}) => {
  return (
    <div className={`vea-form-container ${className}`} style={style}>
      <div
        className="vea-form-card vea-slide-up"
        style={{
          maxWidth,
          padding,
        }}
      >
        {/* Logo Section */}
        {logo && (
          <div className="flex justify-center mb-8">
            {logo}
          </div>
        )}

        {/* Title and Subtitle */}
        {(title || subtitle) && (
          <div className="text-center mb-8">
            {title && (
              <h1 className="vea-heading-primary">
                {title}
              </h1>
            )}
            {subtitle && (
              <p className="vea-text-secondary mt-2">
                {subtitle}
              </p>
            )}
          </div>
        )}

        {/* Form Content */}
        <div className="space-y-6">
          {children}
        </div>

        {/* Footer */}
        {footer && (
          <div className="mt-8 pt-6 border-t border-slate-200">
            {footer}
          </div>
        )}
      </div>
    </div>
  );
};

// Responsive Container Hook
export const useResponsive = () => {
  const [windowSize, setWindowSize] = React.useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1200,
    height: typeof window !== 'undefined' ? window.innerHeight : 800,
  });

  React.useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const isMobile = windowSize.width < 768;
  const isTablet = windowSize.width >= 768 && windowSize.width < 1024;
  const isDesktop = windowSize.width >= 1024;

  const breakpoint = React.useMemo(() => {
    if (windowSize.width < 475) return 'xs';
    if (windowSize.width < 640) return 'sm';
    if (windowSize.width < 768) return 'md';
    if (windowSize.width < 1024) return 'lg';
    if (windowSize.width < 1280) return 'xl';
    return '2xl';
  }, [windowSize.width]);

  return {
    isMobile,
    isTablet,
    isDesktop,
    breakpoint,
    width: windowSize.width,
    height: windowSize.height,
  };
};

// Media Query Component
export const MediaQuery: React.FC<{
  query: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ query, children, fallback = null }) => {
  const [matches, setMatches] = React.useState(false);

  React.useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia(query);
    setMatches(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setMatches(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [query]);

  return matches ? <>{children}</> : <>{fallback}</>;
};

// Mobile-First Responsive Component
export const ResponsiveContainer: React.FC<{
  children: React.ReactNode;
  mobile?: React.ReactNode;
  tablet?: React.ReactNode;
  desktop?: React.ReactNode;
  className?: string;
}> = ({ children, mobile, tablet, desktop, className = '' }) => {
  const { isMobile, isTablet, isDesktop } = useResponsive();

  if (isMobile && mobile) return <div className={className}>{mobile}</div>;
  if (isTablet && tablet) return <div className={className}>{tablet}</div>;
  if (isDesktop && desktop) return <div className={className}>{desktop}</div>;

  return <div className={className}>{children}</div>;
};

// Touch-Friendly Button Component
export const TouchButton: React.FC<{
  children: React.ReactNode;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  className?: string;
}> = ({
  children,
  onClick,
  type = 'button',
  disabled = false,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  className = '',
}) => {
    const baseClasses = 'vea-touch-target transition-smooth focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

    const variantClasses = {
      primary: 'vea-button-primary',
      secondary: 'bg-white border-2 border-vea-green text-vea-green hover:bg-vea-green hover:text-white',
      ghost: 'bg-transparent text-vea-green hover:bg-vea-green/10',
    };

    const sizeClasses = {
      sm: 'h-10 px-4 text-sm',
      md: 'h-12 px-6 text-base',
      lg: 'h-14 px-8 text-lg',
    };

    return (
      <button
        type={type}
        onClick={onClick}
        disabled={disabled}
        className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${fullWidth ? 'w-full' : ''}
        ${className}
      `}
      >
        {children}
      </button>
    );
  };

// Accessibility Skip Link
export const SkipLink: React.FC<{
  href: string;
  children: React.ReactNode;
}> = ({ href, children }) => {
  return (
    <a
      href={href}
      className="
        sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 
        bg-vea-green text-white px-4 py-2 rounded-lg z-50
        focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-vea-green
      "
    >
      {children}
    </a>
  );
};

// Screen Reader Only Text
export const ScreenReaderOnly: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  return <span className="sr-only">{children}</span>;
};

export { LoginLayout, SplitLayout, FormContainer };
