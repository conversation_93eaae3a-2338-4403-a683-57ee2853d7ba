import React from 'react';
import { 
  ExclamationTriangleIcon, 
  XMarkIcon, 
  InformationCircleIcon,
  CheckCircleIcon 
} from '@heroicons/react/24/outline';
import { ErrorMessageProps, LoadingSpinnerProps, ValidationMessageProps } from '@/types/login-components';

// Error Message Component
export const ErrorMessage: React.FC<ErrorMessageProps> = ({
  message,
  type = 'error',
  dismissible = false,
  icon,
  onDismiss,
  className = '',
  style,
}) => {
  const typeStyles = {
    error: 'bg-red-50 border-red-200 text-red-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    info: 'bg-blue-50 border-blue-200 text-blue-800',
  };

  const defaultIcons = {
    error: <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />,
    warning: <ExclamationTriangleIcon className="w-5 h-5 text-yellow-500" />,
    info: <InformationCircleIcon className="w-5 h-5 text-blue-500" />,
  };

  return (
    <div 
      className={`
        flex items-start space-x-3 p-4 border rounded-lg
        ${typeStyles[type]}
        ${className}
      `}
      style={style}
      role="alert"
      aria-live="polite"
    >
      {/* Icon */}
      <div className="flex-shrink-0">
        {icon || defaultIcons[type]}
      </div>

      {/* Message */}
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium">{message}</p>
      </div>

      {/* Dismiss Button */}
      {dismissible && onDismiss && (
        <button
          type="button"
          onClick={onDismiss}
          className="
            flex-shrink-0 p-1 rounded-md hover:bg-black/5 
            focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current
            transition-colors duration-200
          "
          aria-label="Dismiss message"
        >
          <XMarkIcon className="w-4 h-4" />
        </button>
      )}
    </div>
  );
};

// Loading Spinner Component
export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'medium',
  color = 'currentColor',
  text,
  overlay = false,
  className = '',
  style,
}) => {
  const sizeClasses = {
    small: 'w-4 h-4',
    medium: 'w-6 h-6',
    large: 'w-8 h-8',
  };

  const spinner = (
    <div
      className={`
        animate-spin rounded-full border-2 border-transparent
        ${sizeClasses[size]}
        ${className}
      `}
      style={{
        borderTopColor: color,
        borderRightColor: color,
        ...style,
      }}
      role="status"
      aria-label={text || 'Loading'}
    />
  );

  if (overlay) {
    return (
      <div className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 shadow-strong flex flex-col items-center space-y-4">
          {spinner}
          {text && (
            <p className="text-sm text-slate-600 font-medium">{text}</p>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-2">
      {spinner}
      {text && (
        <span className="text-sm text-slate-600">{text}</span>
      )}
    </div>
  );
};

// Validation Message Component
export const ValidationMessage: React.FC<ValidationMessageProps> = ({
  message,
  type,
  visible,
  className = '',
  style,
}) => {
  if (!visible || !message) return null;

  const typeStyles = {
    error: 'text-red-600',
    success: 'text-green-600',
    warning: 'text-yellow-600',
  };

  const icons = {
    error: <ExclamationTriangleIcon className="w-4 h-4" />,
    success: <CheckCircleIcon className="w-4 h-4" />,
    warning: <ExclamationTriangleIcon className="w-4 h-4" />,
  };

  return (
    <div 
      className={`
        flex items-center space-x-2 text-sm font-medium
        ${typeStyles[type]}
        ${className}
      `}
      style={style}
      role={type === 'error' ? 'alert' : 'status'}
      aria-live="polite"
    >
      {icons[type]}
      <span>{message}</span>
    </div>
  );
};

// Form Error Summary Component
export const FormErrorSummary: React.FC<{
  errors: Record<string, string>;
  visible: boolean;
  title?: string;
  className?: string;
}> = ({
  errors,
  visible,
  title = 'Please correct the following errors:',
  className = '',
}) => {
  const errorList = Object.entries(errors).filter(([_, error]) => error);

  if (!visible || errorList.length === 0) return null;

  return (
    <div 
      className={`
        bg-red-50 border border-red-200 rounded-lg p-4
        ${className}
      `}
      role="alert"
      aria-live="polite"
    >
      <div className="flex items-start space-x-3">
        <ExclamationTriangleIcon className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
        <div className="flex-1">
          <h3 className="text-sm font-medium text-red-800 mb-2">
            {title}
          </h3>
          <ul className="text-sm text-red-700 space-y-1">
            {errorList.map(([field, error]) => (
              <li key={field} className="flex items-start space-x-2">
                <span className="w-1 h-1 bg-red-500 rounded-full mt-2 flex-shrink-0" />
                <span>{error}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

// Success Message Component
export const SuccessMessage: React.FC<{
  message: string;
  visible: boolean;
  onDismiss?: () => void;
  className?: string;
}> = ({
  message,
  visible,
  onDismiss,
  className = '',
}) => {
  if (!visible) return null;

  return (
    <div 
      className={`
        bg-green-50 border border-green-200 rounded-lg p-4
        ${className}
      `}
      role="alert"
      aria-live="polite"
    >
      <div className="flex items-start space-x-3">
        <CheckCircleIcon className="w-5 h-5 text-green-500 flex-shrink-0" />
        <div className="flex-1">
          <p className="text-sm font-medium text-green-800">{message}</p>
        </div>
        {onDismiss && (
          <button
            type="button"
            onClick={onDismiss}
            className="
              flex-shrink-0 p-1 rounded-md text-green-500 hover:bg-green-100
              focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500
              transition-colors duration-200
            "
            aria-label="Dismiss success message"
          >
            <XMarkIcon className="w-4 h-4" />
          </button>
        )}
      </div>
    </div>
  );
};

// Loading Overlay Component
export const LoadingOverlay: React.FC<{
  visible: boolean;
  message?: string;
  className?: string;
}> = ({
  visible,
  message = 'Loading...',
  className = '',
}) => {
  if (!visible) return null;

  return (
    <div 
      className={`
        absolute inset-0 bg-white/80 backdrop-blur-sm 
        flex items-center justify-center z-10
        ${className}
      `}
      role="status"
      aria-live="polite"
      aria-label={message}
    >
      <div className="flex flex-col items-center space-y-4">
        <LoadingSpinner size="large" color="#2D5A3D" />
        <p className="text-sm font-medium text-slate-600">{message}</p>
      </div>
    </div>
  );
};

// Error Boundary Component
export class ErrorBoundary extends React.Component<
  {
    children: React.ReactNode;
    fallback?: React.ReactNode;
    onError?: (error: Error, errorInfo: any) => void;
  },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Login Error Boundary caught an error:', error, errorInfo);
    this.props.onError?.(error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen flex items-center justify-center bg-slate-50">
          <div className="max-w-md w-full mx-4">
            <ErrorMessage
              type="error"
              message="Something went wrong. Please refresh the page and try again."
              icon={<ExclamationTriangleIcon className="w-6 h-6 text-red-500" />}
            />
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
