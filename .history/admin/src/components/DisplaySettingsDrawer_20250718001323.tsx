"use client"

import React, { useState } from "react"
import { <PERSON>er, Switch, Typo<PERSON>, Divider, Space, Button, Radio, Card } from "antd"
import {
  MoonOutlined,
  SunOutlined,
  DesktopOutlined,
  BgColorsOutlined,
  EyeOutlined,
  LayoutOutlined,
  <PERSON>boltOutlined
} from "@ant-design/icons"
import { useThemeStore } from "@/store/themeStore"

const { Title, Text } = Typography

interface DisplaySettingsDrawerProps {
  open: boolean
  onClose: () => void
}

export const DisplaySettingsDrawer: React.FC<DisplaySettingsDrawerProps> = ({
  open,
  onClose,
}) => {
  const { mytheme, toggleTheme } = useThemeStore()
  
  // Layout switching state
  const [selectedLayout, setSelectedLayout] = useState(() => {
    return localStorage.getItem('vea-admin-layout') || 'app'
  })

  const handleThemeChange = (checked: boolean) => {
    toggleTheme()
  }

  const handleLayoutChange = (e: any) => {
    const newLayout = e.target.value
    setSelectedLayout(newLayout)
  }

  const handleApplyLayout = () => {
    // Store the preference in localStorage
    localStorage.setItem('vea-admin-layout', selectedLayout)
    
    // Reload the page to apply the new layout
    window.location.reload()
  }

  const layoutOptions = [
    {
      key: 'app',
      title: 'Classic Layout',
      description: 'Original VEA Timeclock admin layout with familiar navigation',
      icon: <LayoutOutlined style={{ fontSize: 16 }} />,
    },
    {
      key: 'appv3',
      title: 'Modern Layout',
      description: 'New modern theme with enhanced UI and improved experience',
      icon: <ThunderboltOutlined style={{ fontSize: 16 }} />,
    }
  ]

  const currentLayout = localStorage.getItem('vea-admin-layout') || 'app'

  return (
    <Drawer
      title={
        <div className="d-flex align-items-center" style={{ gap: 12 }}>
          <div
            className="d-flex align-items-center justify-content-center"
            style={{
              width: 32,
              height: 32,
              borderRadius: 8,
              background: mytheme === "dark" 
                ? "rgba(46, 100, 84, 0.15)" 
                : "rgba(46, 100, 84, 0.1)",
            }}
          >
            <Eye size={16} style={{ color: "#2E6454" }} />
          </div>
          <span style={{ fontSize: 16, fontWeight: 600 }}>Display Settings</span>
        </div>
      }
      placement="right"
      onClose={onClose}
      open={open}
      width={400}
      styles={{
        body: {
          padding: 0,
        },
        header: {
          borderBottom: `1px solid ${mytheme === "dark" ? "#404040" : "#e5e7eb"}`,
          padding: "16px 24px",
        },
      }}
    >
      <div style={{ padding: "24px" }}>
        {/* Theme Section */}
        <div style={{ marginBottom: 32 }}>
          <div className="d-flex align-items-center" style={{ gap: 8, marginBottom: 16 }}>
            <Palette size={18} style={{ color: mytheme === "dark" ? "#9ca3af" : "#6b7280" }} />
            <Title level={5} style={{ margin: 0, fontSize: 14, fontWeight: 600 }}>
              Theme Preference
            </Title>
          </div>
          
          <div
            style={{
              background: mytheme === "dark" ? "#303030" : "#f8fafc",
              borderRadius: 12,
              padding: "16px",
              border: `1px solid ${mytheme === "dark" ? "#404040" : "#e5e7eb"}`,
            }}
          >
            <div className="d-flex align-items-center justify-content-between" style={{ marginBottom: 12 }}>
              <div className="d-flex align-items-center" style={{ gap: 12 }}>
                <div
                  className="d-flex align-items-center justify-content-center"
                  style={{
                    width: 28,
                    height: 28,
                    borderRadius: 6,
                    background: mytheme === "dark" ? "#1A1A1A" : "#ffffff",
                    border: `1px solid ${mytheme === "dark" ? "#555" : "#d1d5db"}`,
                  }}
                >
                  {mytheme === "dark" ? (
                    <Moon size={14} style={{ color: "#e5e7eb" }} />
                  ) : (
                    <Sun size={14} style={{ color: "#f59e0b" }} />
                  )}
                </div>
                <div>
                  <Text strong style={{ fontSize: 14, display: "block" }}>
                    {mytheme === "dark" ? "Dark Mode" : "Light Mode"}
                  </Text>
                  <Text
                    type="secondary"
                    style={{
                      fontSize: 12,
                      color: mytheme === "dark" ? "#9ca3af" : "#6b7280",
                    }}
                  >
                    {mytheme === "dark" 
                      ? "Easy on the eyes in low light" 
                      : "Clean and bright interface"}
                  </Text>
                </div>
              </div>
              <Switch
                checked={mytheme === "dark"}
                onChange={handleThemeChange}
                style={{
                  backgroundColor: mytheme === "dark" ? "#2E6454" : undefined,
                }}
              />
            </div>
          </div>
        </div>

        <Divider style={{ margin: "24px 0" }} />

        {/* Layout Theme Switcher */}
        <div style={{ marginBottom: 32 }}>
          <div className="d-flex align-items-center" style={{ gap: 8, marginBottom: 16 }}>
            <Layout size={18} style={{ color: mytheme === "dark" ? "#9ca3af" : "#6b7280" }} />
            <Title level={5} style={{ margin: 0, fontSize: 14, fontWeight: 600 }}>
              Layout Theme
            </Title>
          </div>
          
          <Text
            type="secondary"
            style={{
              fontSize: 12,
              color: mytheme === "dark" ? "#9ca3af" : "#6b7280",
              display: "block",
              marginBottom: 16,
            }}
          >
            Choose between different layout themes for the admin panel
          </Text>

          <Radio.Group 
            value={selectedLayout} 
            onChange={handleLayoutChange}
            style={{ width: '100%' }}
          >
            <Space direction="vertical" style={{ width: '100%' }} size="small">
              {layoutOptions.map((option) => (
                <div
                  key={option.key}
                  style={{
                    background: mytheme === "dark" ? "#303030" : "#f8fafc",
                    borderRadius: 8,
                    padding: "12px",
                    border: selectedLayout === option.key 
                      ? `2px solid #2E6454` 
                      : `1px solid ${mytheme === "dark" ? "#404040" : "#e5e7eb"}`,
                    cursor: 'pointer',
                    transition: "all 200ms ease",
                  }}
                  onClick={() => setSelectedLayout(option.key)}
                >
                  <Radio value={option.key} style={{ width: '100%' }}>
                    <div className="d-flex align-items-center" style={{ gap: 8 }}>
                      <div
                        className="d-flex align-items-center justify-content-center"
                        style={{
                          width: 24,
                          height: 24,
                          borderRadius: 4,
                          background: selectedLayout === option.key 
                            ? "rgba(46, 100, 84, 0.15)" 
                            : mytheme === "dark" ? "#1A1A1A" : "#ffffff",
                          border: `1px solid ${mytheme === "dark" ? "#555" : "#d1d5db"}`,
                        }}
                      >
                        {React.cloneElement(option.icon, {
                          style: { 
                            color: selectedLayout === option.key 
                              ? "#2E6454" 
                              : mytheme === "dark" ? "#9ca3af" : "#6b7280" 
                          }
                        })}
                      </div>
                      <div>
                        <Text strong style={{ fontSize: 13, display: "block" }}>
                          {option.title}
                        </Text>
                        <Text
                          type="secondary"
                          style={{
                            fontSize: 11,
                            color: mytheme === "dark" ? "#9ca3af" : "#6b7280",
                          }}
                        >
                          {option.description}
                        </Text>
                      </div>
                    </div>
                  </Radio>
                </div>
              ))}
            </Space>
          </Radio.Group>

          {selectedLayout !== currentLayout && (
            <div style={{ marginTop: 12, textAlign: "center" }}>
              <Button 
                type="primary" 
                size="small"
                onClick={handleApplyLayout}
                style={{
                  background: "#2E6454",
                  borderColor: "#2E6454",
                  borderRadius: 6,
                  fontSize: 12,
                  height: 32,
                }}
              >
                Apply Layout & Reload
              </Button>
            </div>
          )}
        </div>

        <Divider style={{ margin: "24px 0" }} />

        {/* Theme Preview */}
        <div style={{ marginBottom: 24 }}>
          <Title level={5} style={{ margin: "0 0 16px 0", fontSize: 14, fontWeight: 600 }}>
            Preview
          </Title>
          
          <div
            style={{
              borderRadius: 8,
              border: `1px solid ${mytheme === "dark" ? "#404040" : "#e5e7eb"}`,
              overflow: "hidden",
            }}
          >
            {/* Mini Header Preview */}
            <div
              style={{
                height: 32,
                background: "#1A1A1A",
                display: "flex",
                alignItems: "center",
                padding: "0 12px",
                gap: 8,
              }}
            >
              <div
                style={{
                  width: 16,
                  height: 16,
                  borderRadius: 4,
                  background: "linear-gradient(135deg, #2E6454 0%, #1F4A3A 100%)",
                }}
              />
              <div
                style={{
                  fontSize: 10,
                  color: "#ffffff",
                  fontWeight: 500,
                }}
              >
                VEA Timeclock
              </div>
            </div>
            
            {/* Mini Content Preview */}
            <div
              style={{
                height: 60,
                background: mytheme === "dark" ? "#1A1A1A" : "#f8fafc",
                padding: "12px",
                display: "flex",
                flexDirection: "column",
                gap: 6,
              }}
            >
              <div
                style={{
                  height: 8,
                  background: mytheme === "dark" ? "#404040" : "#d1d5db",
                  borderRadius: 4,
                  width: "70%",
                }}
              />
              <div
                style={{
                  height: 6,
                  background: mytheme === "dark" ? "#555" : "#e5e7eb",
                  borderRadius: 3,
                  width: "50%",
                }}
              />
              <div
                style={{
                  height: 6,
                  background: mytheme === "dark" ? "#555" : "#e5e7eb",
                  borderRadius: 3,
                  width: "60%",
                }}
              />
            </div>
          </div>
        </div>

        {/* Auto Theme Option (Future Enhancement) */}
        <div
          style={{
            background: mytheme === "dark" ? "#303030" : "#f8fafc",
            borderRadius: 12,
            padding: "16px",
            border: `1px solid ${mytheme === "dark" ? "#404040" : "#e5e7eb"}`,
            opacity: 0.6,
          }}
        >
          <div className="d-flex align-items-center justify-content-between">
            <div className="d-flex align-items-center" style={{ gap: 12 }}>
              <div
                className="d-flex align-items-center justify-content-center"
                style={{
                  width: 28,
                  height: 28,
                  borderRadius: 6,
                  background: mytheme === "dark" ? "#1A1A1A" : "#ffffff",
                  border: `1px solid ${mytheme === "dark" ? "#555" : "#d1d5db"}`,
                }}
              >
                <Monitor size={14} style={{ color: mytheme === "dark" ? "#9ca3af" : "#6b7280" }} />
              </div>
              <div>
                <Text strong style={{ fontSize: 14, display: "block" }}>
                  Auto Theme
                </Text>
                <Text
                  type="secondary"
                  style={{
                    fontSize: 12,
                    color: mytheme === "dark" ? "#9ca3af" : "#6b7280",
                  }}
                >
                  Follow system preference
                </Text>
              </div>
            </div>
            <Switch disabled />
          </div>
        </div>

        <div style={{ marginTop: 24, textAlign: "center" }}>
          <Text
            type="secondary"
            style={{
              fontSize: 11,
              color: mytheme === "dark" ? "#6b7280" : "#9ca3af",
            }}
          >
            Theme changes apply immediately • Layout changes require reload
          </Text>
        </div>
      </div>
    </Drawer>
  )
}
