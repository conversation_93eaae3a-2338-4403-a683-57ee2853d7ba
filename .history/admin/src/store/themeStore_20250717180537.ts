// src/store/themeStore.ts
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { ThemeMode, LayoutOptions, ThemeConfig } from '@/types/theme'
import { PRIMARY_COLOR } from '@/types/theme'
import { getCurrentThemeFolder } from '@/utils/themeConfig'

type ThemeState = {
  // Theme configuration
  themeConfig: ThemeConfig;
  setThemeConfig: (config: Partial<ThemeConfig>) => void;

  // Legacy support (will be deprecated)
  mytheme: ThemeMode;
  toggleTheme: () => void;

  // Theme mode management
  themeMode: ThemeMode;
  setThemeMode: (mode: ThemeMode) => void;

  // Layout management
  layoutOptions: LayoutOptions;
  setLayoutOptions: (options: Partial<LayoutOptions>) => void;

  // Sidebar state
  collapsed: boolean;
  setCollapsed: (collapsed: boolean) => void;

  // Loading state
  mainLoading?: boolean;
  setMainLoading?: (loading: boolean) => void;

  // Hydration state
  _hasHydrated: boolean;
  setHasHydrated: (hasHydrated: boolean) => void;

  // Utility functions
  getCurrentThemeColor: () => string;
  getCurrentThemeFolder: () => string;
  resetToDefaults: () => void;
}

// Default configuration
const DEFAULT_THEME_CONFIG: ThemeConfig = {
  mode: 'light',
  layout: {
    compactSidebar: false,
    fixedHeader: true,
    showBreadcrumbs: false,
    sidebarWidth: 240,
  },
};

export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      // Theme configuration
      themeConfig: DEFAULT_THEME_CONFIG,
      setThemeConfig: (config: Partial<ThemeConfig>) =>
        set((state) => ({
          themeConfig: { ...state.themeConfig, ...config },
          // Update individual properties for backward compatibility
          mytheme: config.mode || state.themeConfig.mode,
          themeMode: config.mode || state.themeConfig.mode,
          layoutOptions: config.layout ? { ...state.layoutOptions, ...config.layout } : state.layoutOptions,
        })),

      // Legacy support
      mytheme: DEFAULT_THEME_CONFIG.mode,
      toggleTheme: () => {
        const currentMode = get().themeMode;
        const newMode: ThemeMode = currentMode === 'light' ? 'dark' : 'light';
        get().setThemeMode(newMode);
      },

      // Theme mode management
      themeMode: DEFAULT_THEME_CONFIG.mode,
      setThemeMode: (mode: ThemeMode) =>
        set((state) => ({
          themeMode: mode,
          mytheme: mode, // Keep legacy property in sync
          themeConfig: { ...state.themeConfig, mode },
        })),

      // Layout management
      layoutOptions: DEFAULT_THEME_CONFIG.layout,
      setLayoutOptions: (options: Partial<LayoutOptions>) =>
        set((state) => ({
          layoutOptions: { ...state.layoutOptions, ...options },
          themeConfig: { ...state.themeConfig, layout: { ...state.layoutOptions, ...options } },
        })),

      // Sidebar state
      collapsed: false,
      setCollapsed: (collapsed: boolean) => {
        const currentState = get();

        console.log('🔄 Sidebar state change:', {
          from: currentState.collapsed,
          to: collapsed,
          hasHydrated: currentState._hasHydrated,
          timestamp: new Date().toISOString()
        });

        set({ collapsed });
      },

      // Loading state
      mainLoading: false,
      setMainLoading: (loading: boolean) => set({ mainLoading: loading }),

      // Hydration state
      _hasHydrated: false,
      setHasHydrated: (hasHydrated: boolean) => set({ _hasHydrated: hasHydrated }),

      // Utility functions
      getCurrentThemeColor: () => {
        return PRIMARY_COLOR;
      },

      getCurrentThemeFolder: () => {
        return getCurrentThemeFolder();
      },

      resetToDefaults: () =>
        set({
          themeConfig: DEFAULT_THEME_CONFIG,
          mytheme: DEFAULT_THEME_CONFIG.mode,
          themeMode: DEFAULT_THEME_CONFIG.mode,
          layoutOptions: DEFAULT_THEME_CONFIG.layout,
        }),
    }),
    {
      name: 'theme-storage',
      partialize: (state) => ({
        themeConfig: state.themeConfig,
        themeMode: state.themeMode,
        layoutOptions: state.layoutOptions,
        collapsed: state.collapsed,
      }),
      onRehydrateStorage: () => (state) => {
        if (process.env.NODE_ENV === 'development') {
          console.log('🔄 Zustand store rehydrated:', state);
        }
        if (state) {
          state.setHasHydrated(true);
          if (process.env.NODE_ENV === 'development') {
            console.log('💾 Restored sidebar collapsed state:', state.collapsed);
          }
        }
      },
    }
  )
)

// Re-export types for better compatibility
export type { ThemeMode, LayoutOptions, ThemeConfig } from '@/types/theme';
export { PRIMARY_COLOR } from '@/types/theme';
export { getCurrentThemeFolder, THEME_CONFIG } from '@/utils/themeConfig';
