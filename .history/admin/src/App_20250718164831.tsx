import React from 'react';
import { RouterProvider } from 'react-router';
import { ConfigProvider, theme as antdTheme } from 'antd';
import { HelmetProvider } from 'react-helmet-async';
import { StylesContext } from './context';
import { router } from './routes/routes';
import { useThemeStore } from './store/themeStore';
import Loader from '@/layouts/Loader';
import './App.css';


export const COLOR = {
  50: '#e7f1ee',
  100: '#c8ded4',
  200: '#a6cab8',
  300: '#82b59b',
  400: '#65a386',
  500: '#2E6454', // primary
  600: '#275648',
  700: '#20483c',
  800: '#183a30',
  900: '#102b23',
  borderColor: '#E7EAF3B2',
};

// CSS
import '@/assets/bootstrap/bootstrap.min.css';


function App() {

  // Define the type for your theme store state
  type ThemeStoreState = {
    mytheme: string;
  };

  const mytheme = useThemeStore((s: ThemeStoreState) => s.mytheme);

  return (
    <HelmetProvider>
      <ConfigProvider
        theme={{
          token: {
            colorPrimary: COLOR['500'],
            borderRadius: 6,
            fontFamily: 'Inter, system-ui, sans-serif',
          },
          components: {
            Menu: {
              itemBg: 'transparent',
              itemSelectedColor: '#303030',
              itemHeight: 30,
              groupTitleFontSize: 13,
              fontSize: 15,
            },
            Button: {
              colorLink: COLOR['500'],
              colorLinkActive: COLOR['700'],
              colorLinkHover: COLOR['300'],
            },
            Calendar: {
              colorBgContainer: 'none',
            },
            Card: {
              colorBorderSecondary: COLOR['100'],
            },
            Carousel: {
              colorBgContainer: COLOR['800'],
              dotWidth: 8,
            },
            Progress: {
              colorPrimary: COLOR['900'],
              remainingColor: COLOR['50'],
              colorText: COLOR['900'],
              colorTextDescription: COLOR['500'],
              colorSuccess: COLOR['500'],
            },
            Rate: {
              colorFillContent: COLOR['100'],
              colorText: COLOR['600'],
            },
            Input: {
              colorFillTertiary: '#f5f5f5',
              colorFillSecondary: '#fafafa',
              colorBorder: 'transparent',
              activeBorderColor: 'transparent',
              hoverBorderColor: 'transparent',
            },
            Select: {
              colorFillTertiary: '#f5f5f5',
              colorFillSecondary: '#fafafa',
              colorBorder: 'transparent',
              activeBorderColor: 'transparent',
              hoverBorderColor: 'transparent',
            },
            Segmented: {
              colorBgLayout: COLOR['100'],
              borderRadius: 6,
              colorTextLabel: '#000000',
            },
            Table: {
              borderColor: COLOR['100'],
              colorBgContainer: 'none',
              headerBg: '#fff', // ✅ Explicit light color
              headerColor: '#000',
              rowHoverBg: COLOR['50'],
            },
            Tabs: {
              colorBorderSecondary: COLOR['100'],
            },
            Timeline: {
              dotBg: 'none',
            },
            Typography: {
              colorLink: COLOR['500'],
              colorLinkActive: COLOR['700'],
              colorLinkHover: COLOR['300'],
              linkHoverDecoration: 'underline',
            },
          },
          algorithm:
            mytheme === 'dark'
              ? antdTheme.darkAlgorithm
              : antdTheme.defaultAlgorithm,
        }}
      >
        <StylesContext.Provider
          value={{
            rowProps: {
              gutter: [
                { xs: 8, sm: 16, md: 24, lg: 32 },
                { xs: 8, sm: 16, md: 24, lg: 32 },
              ],
            },
            carouselProps: {
              autoplay: true,
              dots: true,
              dotPosition: 'bottom',
              infinite: true,
              slidesToShow: 3,
              slidesToScroll: 1,
            },
          }}
        >
          <React.Suspense fallback={null}>
            <RouterProvider router={router} />
          </React.Suspense>

        </StylesContext.Provider>
      </ConfigProvider>
    </HelmetProvider>
  );
}

export default App;
