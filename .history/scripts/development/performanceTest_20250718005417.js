#!/usr/bin/env node

// ** Performance Testing Script
// ** Demonstrates benchmarking and monitoring capabilities

import { runInvoiceBenchmark, runLoadTest, quickPerformanceCheck } from '../../server/utils/performance/invoiceBenchmark.js';
import { getGlobalStats, resetGlobalStats } from '../../server/utils/performance/performanceMonitor.js';

console.log('🚀 VEA TIMECLOCK PERFORMANCE TESTING');
console.log('====================================');

async function runFullPerformanceTest() {
  console.log('\n📊 Starting comprehensive performance testing...\n');

  try {
    // Reset global stats for clean measurement
    resetGlobalStats();

    // 1. Quick Performance Check
    console.log('1️⃣ QUICK PERFORMANCE CHECK');
    console.log('-'.repeat(30));
    await quickPerformanceCheck(1);

    // 2. Benchmark Comparison (Original vs Optimized)
    console.log('\n2️⃣ BENCHMARK COMPARISON');
    console.log('-'.repeat(30));
    const benchmarkResults = await runInvoiceBenchmark({
      iterations: 5,
      accountId: 1,
      startDate: '2025-06-22',
      endDate: '2025-06-28'
    });

    // 3. Load Test on Optimized Version
    console.log('\n3️⃣ LOAD TEST SIMULATION');
    console.log('-'.repeat(30));
    const loadTestResults = await runLoadTest({
      concurrent: 3,
      totalRequests: 20,
      implementation: 'Optimized',
      accountId: 1
    });

    // 4. Global Statistics Summary
    console.log('\n4️⃣ GLOBAL PERFORMANCE SUMMARY');
    console.log('-'.repeat(30));
    const globalStats = getGlobalStats();

    console.log('📈 Overall Performance Statistics:');
    console.log(`   Total Requests: ${globalStats.totalRequests}`);
    console.log(`   Average Response Time: ${globalStats.averages.responseTime}ms`);
    console.log(`   Average Memory Usage: ${globalStats.averages.memoryUsed}MB`);
    console.log(`   Average DB Queries: ${globalStats.averages.dbQueries}`);
    console.log(`   Slow Request Rate: ${globalStats.performance.slowRequestPercentage}%`);
    console.log(`   Error Rate: ${globalStats.performance.errorPercentage}%`);

    // 5. Performance Assessment & Recommendations
    console.log('\n5️⃣ PERFORMANCE ASSESSMENT');
    console.log('-'.repeat(30));

    const { improvements } = benchmarkResults.summary;

    console.log('✅ OPTIMIZATION RESULTS:');
    console.log(`   Response Time Improvement: ${improvements.responseTime}%`);
    console.log(`   Memory Usage Improvement: ${improvements.memoryUsage}%`);
    console.log(`   Database Query Reduction: ${improvements.databaseQueries}%`);

    // Performance level assessment
    if (improvements.responseTime >= 50 && improvements.databaseQueries >= 50) {
      console.log('\n🎉 EXCELLENT: Optimizations show significant improvements!');
      console.log('   ✅ Ready for production deployment');
      console.log('   ✅ Will handle increased load efficiently');
    } else if (improvements.responseTime >= 20 || improvements.databaseQueries >= 20) {
      console.log('\n✅ GOOD: Optimizations show meaningful improvements');
      console.log('   ⚠️  Consider additional optimizations for better scalability');
    } else {
      console.log('\n⚠️  MINIMAL: Limited performance improvements detected');
      console.log('   🔍 Review optimization implementation');
    }

    // Scalability assessment
    console.log('\n📈 SCALABILITY ASSESSMENT:');
    const { throughput } = loadTestResults;
    console.log(`   Current Throughput: ${throughput.toFixed(1)} requests/second`);

    if (throughput >= 10) {
      console.log('   🚀 Excellent scalability for high-load scenarios');
    } else if (throughput >= 5) {
      console.log('   ✅ Good scalability for moderate-load scenarios');
    } else {
      console.log('   ⚠️  Limited scalability - consider further optimizations');
    }

    // Resource usage assessment
    const avgMemory = globalStats.averages.memoryUsed;
    console.log('\n💾 RESOURCE USAGE ASSESSMENT:');
    console.log(`   Average Memory per Request: ${avgMemory}MB`);

    if (avgMemory <= 50) {
      console.log('   ✅ Excellent memory efficiency');
    } else if (avgMemory <= 100) {
      console.log('   ✅ Good memory usage');
    } else {
      console.log('   ⚠️  High memory usage - consider memory optimizations');
    }

    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:');

    if (improvements.responseTime < 50) {
      console.log('   🔧 Consider implementing caching for timecard settings');
      console.log('   🔧 Add database indexes for frequently queried columns');
    }

    if (improvements.databaseQueries < 50) {
      console.log('   🔧 Review database query optimization opportunities');
      console.log('   🔧 Consider database-level aggregation functions');
    }

    if (avgMemory > 100) {
      console.log('   🔧 Implement streaming for large datasets');
      console.log('   🔧 Add pagination for memory-intensive operations');
    }

    if (throughput < 5) {
      console.log('   🔧 Consider implementing response caching');
      console.log('   🔧 Review concurrent request handling');
    }

    console.log('\n🎯 PRODUCTION READINESS CHECKLIST:');
    const checklist = [
      { item: 'Response time < 2 seconds', passed: globalStats.averages.responseTime < 2000 },
      { item: 'Memory usage < 100MB', passed: avgMemory < 100 },
      { item: 'Database queries < 10', passed: globalStats.averages.dbQueries < 10 },
      { item: 'Error rate < 5%', passed: globalStats.performance.errorPercentage < 5 },
      { item: 'Throughput > 5 req/sec', passed: throughput > 5 }
    ];

    checklist.forEach(check => {
      const icon = check.passed ? '✅' : '❌';
      console.log(`   ${icon} ${check.item}`);
    });

    const passedChecks = checklist.filter(c => c.passed).length;
    const readinessScore = (passedChecks / checklist.length) * 100;

    console.log(`\n📊 PRODUCTION READINESS SCORE: ${readinessScore.toFixed(0)}%`);

    if (readinessScore >= 80) {
      console.log('🎉 READY FOR PRODUCTION!');
    } else if (readinessScore >= 60) {
      console.log('⚠️  NEEDS MINOR IMPROVEMENTS');
    } else {
      console.log('🚨 REQUIRES SIGNIFICANT OPTIMIZATION');
    }

  } catch (error) {
    console.error('\n❌ Performance test failed:', error);
    process.exit(1);
  }
}

// Command line interface
const args = process.argv.slice(2);
const command = args[0];

switch (command) {
  case 'quick':
    console.log('Running quick performance check...');
    quickPerformanceCheck().catch(console.error);
    break;

  case 'benchmark':
    console.log('Running benchmark comparison...');
    runInvoiceBenchmark({
      iterations: parseInt(args[1]) || 3,
      accountId: parseInt(args[2]) || 1
    }).catch(console.error);
    break;

  case 'load':
    console.log('Running load test...');
    runLoadTest({
      concurrent: parseInt(args[1]) || 3,
      totalRequests: parseInt(args[2]) || 15,
      implementation: args[3] || 'Optimized'
    }).catch(console.error);
    break;

  case 'full':
  default:
    runFullPerformanceTest().catch(console.error);
    break;
}

// Display usage information
if (command === 'help' || command === '--help' || command === '-h') {
  console.log('\n📖 USAGE:');
  console.log('  node scripts/performanceTest.js [command] [options]');
  console.log('\n📋 COMMANDS:');
  console.log('  full                    Run comprehensive performance test (default)');
  console.log('  quick                   Run quick performance check');
  console.log('  benchmark [iterations] [accountId]  Run benchmark comparison');
  console.log('  load [concurrent] [total] [impl]    Run load test');
  console.log('  help                    Show this help message');
  console.log('\n📝 EXAMPLES:');
  console.log('  node scripts/performanceTest.js');
  console.log('  node scripts/performanceTest.js quick');
  console.log('  node scripts/performanceTest.js benchmark 5 1');
  console.log('  node scripts/performanceTest.js load 5 25 Optimized');
} 