
import { request } from '../../server/db/index.js';

const INACTIVITY_THRESHOLD_DAYS = 180; // 6 months

export async function archiveInactiveEmployees() {

  const sql = `SELECT 
                  e.id AS employee_id,
                  e.first_name,
                  e.last_name,
                  e.phone,
                  er.id AS employment_id,
                  er.start_date,
                  er.created_at,
                  c.name AS client_name,
                  d.name AS department_name,
                  last_punch.last_date,
                  DATE_PART('day', NOW() - COALESCE(last_punch.last_date, er.created_at))::int AS days_since_last_activity
                FROM employees e
                JOIN employment_records er ON er.employee_id = e.id AND er.active = TRUE
                LEFT JOIN clients c ON c.id = er.client_id
                LEFT JOIN departments d ON d.id = er.department_id
                LEFT JOIN (
                  SELECT employment_id, MAX(start_date) AS last_date
                  FROM timeclock
                  GROUP BY employment_id
                ) last_punch ON last_punch.employment_id = er.id
                WHERE COALESCE(last_punch.last_date, er.created_at) < NOW() - INTERVAL '${INACTIVITY_THRESHOLD_DAYS} days'
                ORDER BY days_since_last_activity DESC`;

  const result = await request(sql);

  if (result.length === 0) {
    console.log('No inactive employees found to archive.');
    return;
  }

  console.log(`🔄 Archiving ${result.length} inactive employees...`);

  for (const row of result) {
    await archiveEmployee(row.employment_id);
  }

  console.log(`✅ Archiving complete.`);

}

// ** ========== ARCHIVE EMPLOYEE ========== **
// This function archives an employee's record by marking it as inactive, setting the end date, and clearing shuttle usage.

async function archiveEmployee(employmentId) {

  const ending_notes = `Archived due to inactivity for more than ${INACTIVITY_THRESHOLD_DAYS} days by the system.`;
  const sql = `UPDATE 
                  employment_records 
                    SET
                      active = FALSE,
                      ended_at = NOW(),
                      ending_notes = $2,
                      use_shuttle = FALSE
                    WHERE id = $1`;

  await request(sql, [employmentId, ending_notes]);
  console.log(`📦 Archived employment record ID ${employmentId}`);

}