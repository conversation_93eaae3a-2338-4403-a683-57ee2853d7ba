
import { request } from '../../server/db/index.js';
import dayjs from '../../server/utils/dayjs.js';
import { TIMEZONE } from '../../server/consts.js';
import { createGoogleAuth } from '../../server/libs/google/index.js';
import { uploadToS3 } from '../../server/libs/aws/index.js';

import fs from 'fs';
import fsPromises from 'fs/promises';
import { google } from 'googleapis';
import { decrypt } from 'node-qpdf2';
import cliProgress from 'cli-progress';

const userId = 75;
const downloadedFile = 'paystub.pdf';
const unlockedFile = 'paystub_unlocked.pdf';

// ** ==========================================================================================================
// Function to extract paystubs every Wednesday
// This function checks if today is Wednesday and processes paystubs for the previous day (Tuesday).

export async function extractPaystubs(date) {

  // Check if the day is wednesday:
  const today = dayjs().tz(TIMEZONE);
  if (today.day() !== 3 && !date) {
    console.log('❌ Not Wednesday, skipping paystub extraction.');
    return;
  }

  const tuesdayDate = date ? date : today.subtract(1, 'day').format('YYYY-MM-DD');
  console.log(`🔄 Processing paystubs for date: ${tuesdayDate}`)

  try {
    await processPayStubBatch(tuesdayDate);

  } catch (err) {

    console.error('❌ Error processing paystubs:', err.message || err);
    await logErrorToFile(`Error processing paystubs for ${tuesdayDate}: ${err.message || err}`);

  }

}

// ** ==========================================================================================================
// Function to extract paystubs for multiple weeks (useful for catch-up processing)
// This function processes paystubs for the last N weeks of Tuesdays

export async function extractPaystubsMultipleWeeks(weeksBack = 3) {

  console.log(`🔄 Starting paystub extraction for the last ${weeksBack} weeks...`);

  const today = dayjs().tz(TIMEZONE);
  const tuesdayDates = [];

  // Generate list of Tuesday dates for the last N weeks
  for (let i = 0; i < weeksBack; i++) {
    // Find the most recent Tuesday, then go back i weeks
    let targetDate = today;

    // If today is Wednesday or later in the week, get last Tuesday
    // If today is Monday or Tuesday, get the Tuesday from the previous week + i weeks back
    if (today.day() >= 3) {
      // Wednesday (3) or later - get last Tuesday
      targetDate = today.subtract(today.day() - 2, 'day');
    } else {
      // Monday (1) or Tuesday (2) - get previous Tuesday
      targetDate = today.subtract(today.day() + 5, 'day');
    }

    // Go back i additional weeks
    targetDate = targetDate.subtract(i, 'week');
    tuesdayDates.push(targetDate.format('YYYY-MM-DD'));
  }

  console.log(`📅 Processing paystubs for these Tuesday dates: ${tuesdayDates.join(', ')}`);

  let successCount = 0;
  let errorCount = 0;

  for (const tuesdayDate of tuesdayDates) {
    console.log(`\n🔄 Processing paystubs for ${tuesdayDate}...`);

    try {
      await processPayStubBatch(tuesdayDate);
      successCount++;
      console.log(`✅ Successfully processed paystubs for ${tuesdayDate}`);

      // Add a small delay between batches to be gentle on Gmail API
      await new Promise(resolve => setTimeout(resolve, 2000));

    } catch (err) {
      errorCount++;
      console.error(`❌ Error processing paystubs for ${tuesdayDate}:`, err.message || err);
      await logErrorToFile(`Error processing paystubs for ${tuesdayDate}: ${err.message || err}`);
    }
  }

  console.log(`\n📊 Multi-week extraction complete:`);
  console.log(`   ✅ Successful: ${successCount} weeks`);
  console.log(`   ❌ Errors: ${errorCount} weeks`);
  console.log(`   📅 Total weeks processed: ${weeksBack}`);

  return { successCount, errorCount, totalWeeks: weeksBack, processedDates: tuesdayDates };
}

// ** ==========================================================================================================

async function processPayStubBatch(email_date) {

  if (!email_date) {
    return console.error('❌ No email date provided');
  }

  const sql = `SELECT google_refresh_token FROM users WHERE id = $1`;
  const result = await request(sql, [userId]);
  const REFRESH_TOKEN = result[0]?.google_refresh_token;
  if (!REFRESH_TOKEN) throw new Error('❌ Missing refresh token');

  const oauth2Client = createGoogleAuth();
  oauth2Client.setCredentials({ refresh_token: REFRESH_TOKEN });
  const gmail = google.gmail({ version: 'v1', auth: oauth2Client });

  // ** Date settings:
  const date = dayjs(email_date);
  const afterEpoch = date.startOf('day').unix();
  const beforeEpoch = date.endOf('day').unix();
  const gmailSearchQuery = `subject:"Pay Stub from VERMONT EMPLOYMENT AGENCY" after:${afterEpoch} before:${beforeEpoch}`;

  let nextPageToken = null;
  let totalProcessed = 0;
  const progressBar = new cliProgress.SingleBar({}, cliProgress.Presets.shades_classic);

  const totalCount = await countMessages(gmail, gmailSearchQuery);
  console.log('🔄 Fetching paystub emails...');
  progressBar.start(totalCount, 0);

  do {

    const listRes
      = await gmail.users.messages.list({
        userId: 'me',
        q: gmailSearchQuery,
        maxResults: 100,
        pageToken: nextPageToken,
      });

    const messages = listRes.data.messages || [];

    for (const msg of messages) {
      totalProcessed++;
      progressBar.update(totalProcessed);
      process.stdout.write(`Progress: ${totalProcessed} / ${totalCount}\r`);

      try {
        await processSinglePayStub(gmail, msg.id);
        await new Promise(r => setTimeout(r, 300));

      } catch (err) {

        const fullName = err?.employeeName ? ` for \x1b[32m${err.employeeName}\x1b[0m` : '';
        const err_msg = `❌ Failed for message ${msg.id}${fullName}: ${err.message || 'Unknown error'}`;
        console.error(err_msg);
        await logErrorToFile(err_msg);
        progressBar.stop();
        continue;
      }
    }

    nextPageToken = listRes.data.nextPageToken;
  } while (nextPageToken);

  progressBar.stop();
  console.log(`✅ [${totalProcessed}/${totalCount}] Completed batch processing: ${totalProcessed} emails processed.`);

}

// ** ==========================================================================================================

async function processSinglePayStub(gmail, messageId) {

  const messageRes = await gmail.users.messages.get({
    userId: 'me',
    id: messageId,
    format: 'full',
  });

  const fromHeader = messageRes.data.payload.headers.find(h => h.name === 'To')?.value;
  const bodySnippet = messageRes.data.snippet || '';
  const emailDate = new Date(Number(messageRes.data.internalDate));

  let employee;
  try {
    employee = await findEmployeeSmart({
      emailValue: fromHeader,
      emailBody: bodySnippet,
    });
  } catch (err) {
    err.employeeName = 'Unknown';
    throw err;
  }

  const pdfPart = findPdfAttachment(messageRes.data.payload);
  if (!pdfPart) throw new Error('❌ No PDF attachment found');

  const fileName = `${employee.id}-${pdfPart.filename}`;

  const checkSql = `SELECT id FROM employee_paystubs WHERE employee_id = $1 AND email_id = $2`;
  const existing = await request(checkSql, [employee.id, messageId]);
  if (existing.length > 0) {
    // console.log(`⚠️ Already processed: ${fileName}`);
    return;
  }

  const attachmentRes = await gmail.users.messages.attachments.get({
    userId: 'me',
    messageId: messageId,
    id: pdfPart.body.attachmentId,
  });

  const pdfData = attachmentRes.data.data.replace(/-/g, '+').replace(/_/g, '/');
  const buffer = Buffer.from(pdfData, 'base64');
  fs.writeFileSync(downloadedFile, buffer);

  const password = (employee.lastName.length < 4 ? employee.lastName : employee.lastName.slice(0, 4)).toLowerCase() + employee.ssn.slice(-4);
  await unlockPdf(downloadedFile, unlockedFile, password, `${employee.firstName} ${employee.lastName}`);

  const payDate = dayjs(emailDate).add(2, 'day').format('YYYY-MM-DD'); // Thursday
  const startDate = dayjs(emailDate).subtract(9, 'day').format('YYYY-MM-DD'); // Sunday prior week
  const endDate = dayjs(emailDate).subtract(3, 'day').format('YYYY-MM-DD'); // Saturday prior week

  const s3Key = `accounts/${employee.account_id}/paystubs/${fileName}`;
  const fileBuffer = await fsPromises.readFile(unlockedFile);

  await uploadToS3({
    key: s3Key,
    body: fileBuffer,
    contentType: 'application/pdf',
  });

  const insertSql = `
    INSERT INTO employee_paystubs (employee_id, email_id, file_name, start_date, end_date, pay_date)
    VALUES ($1, $2, $3, $4, $5, $6)
  `;
  await request(insertSql, [
    employee.id,
    messageId,
    fileName,
    startDate,
    endDate,
    payDate,
  ]);

  // console.log(`✅ Uploaded and recorded: ${fileName}`);
}

// ** ==========================================================================================================

async function countMessages(gmail, query) {
  let total = 0;
  let pageToken = null;

  do {
    const res = await gmail.users.messages.list({
      userId: 'me',
      q: query,
      maxResults: 100,
      pageToken,
    });

    total += res.data.messages?.length || 0;
    pageToken = res.data.nextPageToken;
  } while (pageToken);

  return total;
}

async function findEmployeeSmart({ emailValue, emailBody }) {

  let firstName, lastName, email;

  if (emailValue) {

    const [namePart, emailPart] = emailValue.split('<');
    const [first, ...lastParts] = namePart.trim().split(' ');
    firstName = first;
    lastName = lastParts.join(' ') || '';
    email = emailPart?.replace('>', '').trim();
  }

  if ((!firstName || !lastName) && emailBody) {
    const match = emailBody.match(/Dear\s+([A-Za-z]+)\s+([A-Za-z]+)/);
    if (match) {
      firstName = match[1];
      lastName = match[2];
    }
  }

  if (!firstName || !lastName) {
    await logErrorToFile('❌ Could not extract employee name from email', emailBody);
    throw new Error('❌ Could not extract employee name.');
  }

  const baseSql = `SELECT 
                  COALESCE(e.account_id, 1) AS account_id,
                  e.id,
                  e.first_name,
                  e.last_name,
                  e.ssn
                   FROM employees e 
                   INNER JOIN employment_records er ON er.employee_id = e.id 
                   WHERE
                   (LOWER(e.email) = $1 OR (LOWER(e.first_name) = $2 AND LOWER(e.last_name) = $3))
                   LIMIT 1`;

  const queryParams = email
    ? [email.toLowerCase(), firstName.toLowerCase(), lastName.toLowerCase()]
    : [null, firstName.toLowerCase(), lastName.toLowerCase()];

  const result = await request(baseSql, queryParams);

  if (result.length > 0) {

    const emp = result[0];

    if (emp.ssn.includes('0000')) {
      console.warn(`⚠️ SSN mismatch detected for \x1b[1;32m${emp.first_name} ${emp.last_name}\x1b[0m: SSN ends with 0000`);
      await logErrorToFile(`⚠️ SSN mismatch for ${emp.first_name} ${emp.last_name}: SSN=${emp.ssn}`);
    }

    return {
      account_id: emp.account_id,
      id: emp.id,
      firstName: emp.first_name,
      lastName: emp.last_name,
      ssn: emp.ssn.replace(/[^0-9]/g, '')
    };
  }

  throw new Error(`❌ No employee found for \x1b[35;1m${firstName} ${lastName}\x1b[0m`);
}

// ** ==========================================================================================================

async function unlockPdf(inputFile, outputFile, password, employeeName) {
  try {
    await decrypt({ input: inputFile, output: outputFile, password });
  } catch (err) {
    const error = new Error(`❌ Failed to unlock PDF: ${err.message}`);
    error.employeeName = employeeName;
    throw error;
  }
}

function findPdfAttachment(payload) {
  if (!payload) return null;
  if ((payload.mimeType === 'application/pdf' || payload.mimeType === 'application/octet-stream') && payload.filename && payload.body?.attachmentId) {
    return payload;
  }
  if (payload.parts && Array.isArray(payload.parts)) {
    for (const part of payload.parts) {
      const found = findPdfAttachment(part);
      if (found) return found;
    }
  }
  return null;
}

// ** ==========================================================================================================

async function logErrorToFile(message) {
  const logMessage = `[${new Date().toISOString()}] ${message}\n`;
  await fsPromises.appendFile('paystub_errors.log', logMessage);
}

// ** ==========================================================================================================