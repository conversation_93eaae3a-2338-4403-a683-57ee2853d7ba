
import { OP_UserLogs } from './remove_login_history.js';
import { runAutomatedSMS } from './automatedSMS.js';
import { extractPaystubs } from './extract_paystubs.js';
import { archiveInactiveEmployees } from './archive_inactive_employees.js';
import { renewHerokuToken } from './heroku_token_renewal.js';

// ** RUN JOBS ** ==========================================================

(async () => {
  console.log('🌅 Running everyday 8am job...');

  try {
    // Check and renew Heroku token if needed
    await renewHerokuToken();

    // Run the comprehensive automated SMS system (birthdays + important days)
    await runAutomatedSMS();

    await archiveInactiveEmployees();
    await OP_UserLogs();
    await extractPaystubs();

    console.log('✅ All scheduled tasks completed.');
    process.exit(0);

  } catch (err) {
    console.error('🚨 Scheduled task failed:', err);
    process.exit(1);
  }
})();