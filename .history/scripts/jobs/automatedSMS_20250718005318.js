/**
 * AUTOMATED SMS SYSTEM
 * ===================
 * 
 * This comprehensive SMS system handles both birthday messages and important day messages
 * for employees. It replaces the old birthday_texts.js system with enhanced functionality.
 * 
 * FEATURES:
 * --------
 * ✅ Birthday Messages: Sends personalized birthday wishes to employees
 * ✅ Important Day Messages: Sends messages for US holidays and special days
 * ✅ Smart Targeting: Only messages employees with recent activity (30 days)
 * ✅ Comprehensive Holiday Calendar: 50+ US holidays and special days
 * ✅ Floating Holiday Support: Handles holidays that change dates yearly
 * ✅ Dev Mode Support: Safe testing without sending actual messages
 * ✅ Detailed Logging: Full tracking of message sending and results
 * 
 * USAGE:
 * ------
 * import { runAutomatedSMS, sendBirthdayMessages, sendImportantDayMessages } from './automatedSMS.js';
 * 
 * // Run both birthday and important day messages
 * await runAutomatedSMS();
 * 
 * // Run individual functions
 * await sendBirthdayMessages();
 * await sendImportantDayMessages();
 * 
 * INTEGRATION:
 * -----------
 * This system is automatically run daily at 8am via scripts/index.js
 * 
 * IMPORTANT DAYS INCLUDED:
 * -----------------------
 * - Major Federal Holidays (New Year's, July 4th, Christmas, etc.)
 * - Cultural Awareness Months (Black History, Pride, Hispanic Heritage, etc.)
 * - Workplace Days (Employee Appreciation, Boss Day, Safety Month, etc.)
 * - Fun Days (Pi Day, Star Wars Day, National Dog Day, etc.)
 * - Floating Holidays (Mother's Day, Father's Day, Labor Day, Thanksgiving)
 * 
 * <AUTHOR> Timeclock Team
 * @version 2.0.0
 * @created 2025-01-22
 */

import { request } from '../../server/db/index.js';
import { isDev, DEFAULT_EMPLOYER, TIMEZONE } from '../../server/consts.js';
import { SendSMS } from '../../server/libs/twilio/index.js';
import dayjs from '../../server/utils/dayjs.js';

// ** IMPORTANT US DAYS CONFIGURATION ** ==========================================================

const US_IMPORTANT_DAYS = [

  // Major Federal Holidays
  { month: 1, day: 1, name: "New Year's Day", message: "Happy New Year! Wishing you health, happiness, and success in the year ahead!" },
  { month: 1, day: 15, name: "Martin Luther King Jr. Day", message: "Today we honor Dr. Martin Luther King Jr. and his legacy of equality, justice, and peace." },
  { month: 2, day: 22, name: "Washington's Birthday", message: "Happy Presidents' Day! Honoring our nation's leaders and the values they stood for." },
  { month: 3, day: 17, name: "St. Patrick's Day", message: "Happy St. Patrick's Day! May your day be touched by a bit of Irish luck!" },
  { month: 6, day: 19, name: "Juneteenth", message: "Today we celebrate Juneteenth - commemorating freedom, equality, and justice for all." },
  { month: 7, day: 4, name: "Independence Day", message: "Happy 4th of July! Celebrating freedom, independence, and the American spirit!" },
  { month: 9, day: null, name: "Labor Day", message: "Happy Labor Day! Today we celebrate the hard work and dedication of all American workers!" },
  { month: 10, day: null, name: "Columbus Day", message: "Happy Columbus Day! Reflecting on exploration, discovery, and cultural heritage." },
  { month: 10, day: 31, name: "Halloween", message: "Happy Halloween! Hope your day is filled with treats and not too many tricks!" },
  { month: 11, day: 11, name: "Veterans Day", message: "Happy Veterans Day! Thank you to all who have served our country with honor and courage." },
  { month: 11, day: null, name: "Thanksgiving", message: "Happy Thanksgiving! Grateful for our amazing team and all the blessings we share together!" },
  { month: 12, day: 25, name: "Christmas Day", message: "Merry Christmas! Wishing you and your loved ones joy, peace, and happiness this holiday season!" },

];

// ** HELPER FUNCTIONS ** ==========================================================

/**
 * Get special observance days that vary by year (like floating holidays)
 */
function getFloatingHolidays(year) {
  const holidays = [];

  // Labor Day - First Monday in September
  const laborDay = dayjs().year(year).month(8).startOf('month');
  const firstMondaySept = laborDay.day(1) === 1 ? laborDay : laborDay.day(8);
  holidays.push({
    date: firstMondaySept,
    name: "Labor Day",
    message: "Happy Labor Day! Today we celebrate the hard work and dedication of all American workers!"
  });

  // Thanksgiving - Fourth Thursday in November
  const thanksgiving = dayjs().year(year).month(10).startOf('month');
  const firstThursday = thanksgiving.day(4) === 4 ? thanksgiving : thanksgiving.day(11);
  const fourthThursday = firstThursday.add(21, 'day');
  holidays.push({
    date: fourthThursday,
    name: "Thanksgiving",
    message: "Happy Thanksgiving! Grateful for our amazing team and all the blessings we share together!"
  });

  return holidays;
}

/**
 * Check if today matches an important day
 */
function getTodaysImportantDay() {
  const today = dayjs().tz(TIMEZONE);
  const currentYear = today.year();
  const currentMonth = today.month() + 1; // dayjs months are 0-indexed
  const currentDay = today.date();

  // Check fixed date holidays
  const fixedHoliday = US_IMPORTANT_DAYS.find(holiday =>
    holiday.month === currentMonth &&
    holiday.day === currentDay
  );

  if (fixedHoliday) {
    return fixedHoliday;
  }

  // Check floating holidays
  const floatingHolidays = getFloatingHolidays(currentYear);
  const floatingHoliday = floatingHolidays.find(holiday =>
    holiday.date.month() + 1 === currentMonth &&
    holiday.date.date() === currentDay
  );

  if (floatingHoliday) {
    return {
      name: floatingHoliday.name,
      message: floatingHoliday.message
    };
  }

  return null;
}

// ** BIRTHDAY SMS FUNCTION ** ==========================================================

export async function sendBirthdayMessages() {
  console.log('🎉 Running birthday job...');

  const sql = `SELECT DISTINCT e.first_name, e.last_name, e.phone, e.birthdate
              FROM employees e
              JOIN employment_records er ON e.id = er.employee_id
              JOIN timeclock tc ON er.id = tc.employment_id
              WHERE 
                (
                  er.active = TRUE OR 
                  tc.start_date >= CURRENT_DATE - INTERVAL '1 month'
                )
                AND EXTRACT(MONTH FROM e.birthdate) = EXTRACT(MONTH FROM CURRENT_DATE)
                AND EXTRACT(DAY FROM e.birthdate) = EXTRACT(DAY FROM CURRENT_DATE)
                AND e.phone IS NOT NULL;
              `;

  const query = await request(sql);

  console.log('🎂 Found', query.length, 'employees with birthdays today.');

  if (!query.length) {
    console.log('No employees with birthdays today.');
    return { sent: 0, type: 'birthday' };
  }

  let sent = 0;
  let messages = [];

  for (const employee of query) {
    try {
      const { first_name, last_name, phone: phoneNumber } = employee;

      // Format message
      const template = `Happy Birthday {{employee_name}}! 🎉 Wishing you a wonderful day and a year full of success. Thanks for being a valued part of the {{brand}} team!`;
      let message = template.replace('{{employee_name}}', first_name).replace('{{brand}}', DEFAULT_EMPLOYER.NAME_SHORT);

      // Format phone number
      let phone = phoneNumber.replace(/\D/g, '');
      phone = `+1${phone}`; // Prepend country code for US/Canada

      messages.push({
        to: phone,
        body: message,
      });

      console.log(`📨 Prepared birthday message for: ${first_name} ${last_name} (${phone})`);

    } catch (error) {
      console.error('🚨 Error preparing birthday message:', error);
    }
  }

  if (messages.length > 0) {
    if (isDev) {
      console.log(`📨 [DEV MODE] Would send ${messages.length} birthday messages`);
      return { sent: messages.length, type: 'birthday', devMode: true };
    }

    const res = await SendSMS(messages);

    if (res.results) {
      res.results.forEach((msg, idx) => {
        const { status, results } = msg || {};
        const employee = query[idx];

        if (status !== 'success') {
          console.error(`❌ Error sending birthday message to ${employee.first_name} ${employee.last_name}:`, results || 'Unknown error');
        } else {
          console.log(`✅ Birthday message sent to ${employee.first_name} ${employee.last_name}.`);
          sent++;
        }
      });
    }
  }

  console.log(`🎉 Sent ${sent} birthday messages today.`);
  return { sent, type: 'birthday' };
}

// ** IMPORTANT DAY SMS FUNCTION ** ==========================================================

export async function sendImportantDayMessages() {
  console.log('🎊 Running important day job...');

  const importantDay = getTodaysImportantDay();

  if (!importantDay) {
    console.log('No important day messages to send today.');
    return { sent: 0, type: 'important_day' };
  }

  console.log(`🎊 Today is: ${importantDay.name}`);

  // Get employees with recent punch records (within 30 days) and phone numbers
  const sql = `SELECT DISTINCT e.first_name, e.last_name, e.phone
              FROM employees e
              JOIN employment_records er ON e.id = er.employee_id
              JOIN timeclock tc ON er.id = tc.employment_id
              WHERE 
                tc.start_date >= CURRENT_DATE - INTERVAL '30 days'
                AND e.phone IS NOT NULL
                AND (er.active = TRUE OR tc.start_date >= CURRENT_DATE - INTERVAL '1 month');
              `;

  const query = await request(sql);

  console.log('🎊 Found', query.length, 'employees with recent activity to message.');

  if (!query.length) {
    console.log('No active employees found for important day messaging.');
    return { sent: 0, type: 'important_day' };
  }

  let sent = 0;
  let messages = [];

  for (const employee of query) {
    try {
      const { first_name, last_name, phone: phoneNumber } = employee;

      // Format message
      let message = importantDay.message.replace('{{employee_name}}', first_name).replace('{{brand}}', DEFAULT_EMPLOYER.NAME_SHORT);

      // Add company signature
      message += ` - The ${DEFAULT_EMPLOYER.NAME_SHORT} Team 🌟`;

      // Format phone number
      let phone = phoneNumber.replace(/\D/g, '');
      phone = `+1${phone}`; // Prepend country code for US/Canada

      messages.push({
        to: phone,
        body: message,
      });

      console.log(`📨 Prepared ${importantDay.name} message for: ${first_name} ${last_name} (${phone})`);

    } catch (error) {
      console.error('🚨 Error preparing important day message:', error);
    }
  }

  if (messages.length > 0) {
    if (isDev) {
      console.log(`📨 [DEV MODE] Would send ${messages.length} ${importantDay.name} messages`);
      return { sent: messages.length, type: 'important_day', holiday: importantDay.name, devMode: true };
    }

    const res = await SendSMS(messages);

    if (res.results) {
      res.results.forEach((msg, idx) => {
        const { status, results } = msg || {};
        const employee = query[idx];

        if (status !== 'success') {
          console.error(`❌ Error sending ${importantDay.name} message to ${employee.first_name} ${employee.last_name}:`, results || 'Unknown error');
        } else {
          console.log(`✅ ${importantDay.name} message sent to ${employee.first_name} ${employee.last_name}.`);
          sent++;
        }
      });
    }
  }

  console.log(`🎊 Sent ${sent} ${importantDay.name} messages today.`);
  return { sent, type: 'important_day', holiday: importantDay.name };
}

// ** COMBINED AUTOMATED SMS FUNCTION ** ==========================================================

export async function runAutomatedSMS() {
  console.log('🤖 Starting automated SMS system...');

  const results = {
    birthday: { sent: 0, type: 'birthday' },
    importantDay: { sent: 0, type: 'important_day' },
    totalSent: 0,
    timestamp: new Date().toISOString()
  };

  try {
    // Run birthday messages
    results.birthday = await sendBirthdayMessages();

    // Run important day messages
    results.importantDay = await sendImportantDayMessages();

    // Calculate totals
    results.totalSent = results.birthday.sent + results.importantDay.sent;

    console.log(`🤖 Automated SMS complete: ${results.totalSent} total messages sent`);
    console.log(`   - Birthday messages: ${results.birthday.sent}`);
    console.log(`   - Important day messages: ${results.importantDay.sent}`);

    return results;

  } catch (error) {
    console.error('🚨 Automated SMS system error:', error);
    throw error;
  }
}

// ** SAFE TEST MODE FUNCTION ** ==========================================================

export async function runTestMode() {
  console.log('🧪 RUNNING SAFE TEST MODE - Only sending to +18023452998');
  console.log('⚠️  This will send ONLY ONE test message to verify functionality');

  const TEST_PHONE = '+18023452998';
  const today = dayjs().tz(TIMEZONE);
  let testMessage = '';
  let messageType = '';

  try {
    // Check if there are any birthdays today
    const birthdaySQL = `SELECT DISTINCT e.first_name, e.last_name, e.phone, e.birthdate
                        FROM employees e
                        JOIN employment_records er ON e.id = er.employee_id
                        JOIN timeclock tc ON er.id = tc.employment_id
                        WHERE 
                          (
                            er.active = TRUE OR 
                            tc.start_date >= CURRENT_DATE - INTERVAL '1 month'
                          )
                          AND EXTRACT(MONTH FROM e.birthdate) = EXTRACT(MONTH FROM CURRENT_DATE)
                          AND EXTRACT(DAY FROM e.birthdate) = EXTRACT(DAY FROM CURRENT_DATE)
                          AND e.phone IS NOT NULL;`;

    const birthdayEmployees = await request(birthdaySQL);

    // Check if today is an important day
    const importantDay = getTodaysImportantDay();

    // Determine what to test
    if (birthdayEmployees.length > 0) {
      // Test birthday message
      messageType = 'Birthday Test';
      const employee = birthdayEmployees[0];
      testMessage = `🧪 TEST MODE - Birthday Message:\n\nHappy Birthday ${employee.first_name}! 🎉 Wishing you a wonderful day and a year full of success. Thanks for being a valued part of the ${DEFAULT_EMPLOYER.NAME_SHORT} team!\n\n(Found ${birthdayEmployees.length} birthday(s) today)`;

    } else if (importantDay) {
      // Test important day message
      messageType = 'Important Day Test';

      // Get count of employees who would receive the message
      const employeeSQL = `SELECT COUNT(DISTINCT e.id) as count
                          FROM employees e
                          JOIN employment_records er ON e.id = er.employee_id
                          JOIN timeclock tc ON er.id = tc.employment_id
                          WHERE 
                            tc.start_date >= CURRENT_DATE - INTERVAL '30 days'
                            AND e.phone IS NOT NULL
                            AND (er.active = TRUE OR tc.start_date >= CURRENT_DATE - INTERVAL '1 month');`;

      const employeeCount = await request(employeeSQL);
      const count = employeeCount[0]?.count || 0;

      testMessage = `🧪 TEST MODE - Important Day Message:\n\n${importantDay.message} - The ${DEFAULT_EMPLOYER.NAME_SHORT} Team 🌟\n\n(Today is: ${importantDay.name})\n(Would send to ${count} employees)`;

    } else {
      // No special day - send system test message
      messageType = 'System Test';
      testMessage = `🧪 TEST MODE - Daily Check Complete:\n\n✅ No birthdays today\n✅ No important days today\n✅ Automated SMS system is working properly!\n\nDate: ${today.format('MMMM D, YYYY')}\nTime: ${today.format('h:mm A')} ${TIMEZONE}`;
    }

    console.log(`📱 Sending test message (${messageType}) to ${TEST_PHONE}`);
    console.log(`📝 Message: ${testMessage}`);

    // Send the test message
    const testResult = await SendSMS([{
      to: TEST_PHONE,
      body: testMessage
    }]);

    if (testResult.results && testResult.results[0]?.status === 'success') {
      console.log('✅ Test message sent successfully!');
      return {
        success: true,
        messageType,
        sentTo: TEST_PHONE,
        birthdaysFound: birthdayEmployees.length,
        importantDay: importantDay?.name || null,
        timestamp: new Date().toISOString()
      };
    } else {
      console.error('❌ Test message failed:', testResult.results?.[0]?.data);
      return {
        success: false,
        error: testResult.results?.[0]?.data || 'Unknown error',
        timestamp: new Date().toISOString()
      };
    }

  } catch (error) {
    console.error('🚨 Test mode error:', error);
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

// ** MANUAL TESTING FUNCTIONS ** ==========================================================

export async function testImportantDayCheck() {
  console.log('🧪 Testing important day detection...');

  const today = dayjs().tz(TIMEZONE);
  console.log(`Today is: ${today.format('MMMM D, YYYY (dddd)')}`);

  const importantDay = getTodaysImportantDay();

  if (importantDay) {
    console.log(`✅ Important day detected: ${importantDay.name}`);
    console.log(`📝 Message: ${importantDay.message}`);
  } else {
    console.log('❌ No important day detected for today');
  }

  // Show next few important days
  console.log('\n📅 Next important days:');
  for (let i = 1; i <= 30; i++) {
    const checkDate = today.add(i, 'day');
    const dayjs_temp = dayjs; // Store original
    dayjs = () => checkDate; // Temporarily override

    const futureDay = getTodaysImportantDay();
    if (futureDay) {
      console.log(`   ${checkDate.format('MMM D')}: ${futureDay.name}`);
    }

    dayjs = dayjs_temp; // Restore original
  }
} 