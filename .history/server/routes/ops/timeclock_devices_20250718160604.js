// ** Database:
import { request } from '../../db/index.js';

// ** Utils
import { TIMEZONE } from '../../consts.js';

// ** ==============================================================================================
// ** Function: FetchDeviceFleet - Get all timeclock devices with enhanced data

export async function FetchDeviceFleet(req, res) {
  const { account_id } = req.body;

  if (!account_id) {
    return res.status(400).json({
      status: 'error',
      message: 'Account ID is required'
    });
  }

  try {
    const accountCondition = account_id == 1 ? 'IS NULL OR d.account_id = 1' : `= ${account_id}`;

    const sql = `
      SELECT 
        d.id,
        d.archived,
        d.account_id,
        d.active,
        d.name,
        d.brand,
        d.battery_level,
        d.device_no,
        d.charge_status,
        d.last_event,
        d.device_code,
        d.on_hand,
        d.software_version,
        d.vea_app_vers,
        d.serial_number,
        d.model,
        loc.location,
        c.id AS client_id,
        c.name AS client_name,
        c.logo AS client_logo,
        CASE 
          WHEN d.last_event IS NULL THEN 'never_connected'
          WHEN d.last_event < (NOW() AT TIME ZONE 'UTC') - INTERVAL '30 minutes' THEN 'disconnected'
          WHEN d.last_event < (NOW() AT TIME ZONE 'UTC') - INTERVAL '5 minutes' THEN 'recently_active'
          ELSE 'online'
        END AS connection_status,
        CASE
          WHEN d.last_event IS NULL THEN 0
          WHEN d.last_event > (NOW() AT TIME ZONE 'UTC') - INTERVAL '5 minutes' THEN 3
          WHEN d.last_event > (NOW() AT TIME ZONE 'UTC') - INTERVAL '30 minutes' THEN 2
          ELSE 1
        END AS connection_priority,
        EXISTS (
          SELECT 1 FROM timeclock 
          WHERE start_device_id = d.id OR end_device_id = d.id
        ) AS have_records
      FROM devices d
      LEFT JOIN clients c ON c.id = d.client_id
      LEFT JOIN client_locations loc ON loc.id = d.location_id
      WHERE d.account_id ${accountCondition}
        AND (d.archived = false OR d.archived IS NULL)
      ORDER BY 
        d.active DESC,
        connection_priority DESC,
        c.name NULLS LAST,
        d.device_no,
        d.name ASC
    `;

    const result = await request(sql);

    return res.status(200).json({
      status: 'success',
      data: result
    });

  } catch (error) {
    console.error('[FetchDeviceFleet] Error:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Failed to fetch timeclock devices'
    });
  }
}

// ** ==============================================================================================
// ** Function: ManageDevice - CRUD operations for timeclock devices

export async function ManageDevice(req, res) {
  const {
    db_event,
    account_id,
    user_id,
    id,
    device_id,
    device_name,
    client_id,
    location_id,
    departments,
    device_no,
    brand,
    active,
    serial_number,
    model
  } = req.body;

  if (!db_event || !account_id) {
    return res.status(400).json({
      status: 'error',
      message: 'Missing required parameters: db_event, account_id'
    });
  }

  try {
    let result;

    switch (db_event) {
      case 'add':
        if (!device_id || !device_name) {
          return res.status(400).json({
            status: 'error',
            message: 'Device ID and name are required for adding device'
          });
        }

        const insertSql = `
          INSERT INTO devices (
            name, device_code, device_no, brand, account_id, client_id, 
            location_id, departments, active, app_version, serial_number, model
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
          RETURNING *
        `;

        result = await request(insertSql, [
          device_name,
          device_id,
          device_no || null,
          brand || null,
          account_id,
          client_id || null,
          location_id || null,
          departments ? `{${departments.join(',')}}` : null,
          active !== false,
          1, // app_version
          serial_number || null,
          model || null
        ]);

        break;

      case 'edit':
        if (!id) {
          return res.status(400).json({
            status: 'error',
            message: 'Device ID is required for editing'
          });
        }

        const updateSql = `
          UPDATE devices 
          SET 
            name = COALESCE($1, name),
            device_code = COALESCE($2, device_code),
            device_no = COALESCE($3, device_no),
            brand = COALESCE($4, brand),
            client_id = $5,
            location_id = $6,
            departments = $7,
            active = COALESCE($8, active),
            serial_number = $9,
            model = $10
          WHERE id = $11 AND account_id = $12
          RETURNING *
        `;

        result = await request(updateSql, [
          device_name || null,
          device_id || null,
          device_no || null,
          brand || null,
          client_id || null,
          location_id || null,
          departments ? `{${departments.join(',')}}` : null,
          active,
          serial_number || null,
          model || null,
          id,
          account_id
        ]);

        if (result.length === 0) {
          return res.status(404).json({
            status: 'error',
            message: 'Device not found or access denied'
          });
        }

        break;

      case 'delete':
        if (!id) {
          return res.status(400).json({
            status: 'error',
            message: 'Device ID is required for deletion'
          });
        }

        const deleteSql = `
          UPDATE devices 
          SET archived = true
          WHERE id = $1 AND account_id = $2
          RETURNING *
        `;

        result = await request(deleteSql, [id, account_id]);

        if (result.length === 0) {
          return res.status(404).json({
            status: 'error',
            message: 'Device not found or access denied'
          });
        }

        break;

      default:
        return res.status(400).json({
          status: 'error',
          message: 'Invalid operation. Supported operations: add, edit, delete'
        });
    }

    return res.status(200).json({
      status: 'success',
      message: `Device ${db_event === 'add' ? 'created' : db_event === 'edit' ? 'updated' : 'deleted'} successfully`,
      data: result[0] || null
    });

  } catch (error) {
    console.error('[ManageDevice] Error:', error);

    // Handle specific database errors
    if (error.code === '23505') { // Unique constraint violation
      return res.status(400).json({
        status: 'error',
        message: 'Device code already exists. Please use a unique device code.'
      });
    }

    return res.status(500).json({
      status: 'error',
      message: 'Failed to process device operation'
    });
  }
}

// ** ==============================================================================================
// ** Function: PatchDeviceStatus - Update specific device status fields

export async function PatchDeviceStatus(req, res) {
  const { id, field, value } = req.body;

  if (!id || !field || value === undefined) {
    return res.status(400).json({
      status: 'error',
      message: 'Device ID, field, and value are required'
    });
  }

  // Whitelist allowed fields for security
  const allowedFields = [
    'active',
    'archived',
    'battery_level',
    'charge_status',
    'last_event',
    'software_version'
  ];

  if (!allowedFields.includes(field)) {
    return res.status(400).json({
      status: 'error',
      message: `Field '${field}' is not allowed for update. Allowed fields: ${allowedFields.join(', ')}`
    });
  }

  try {
    const sql = `
      UPDATE devices 
      SET ${field} = $1
      WHERE id = $2
      RETURNING *
    `;

    const result = await request(sql, [value, id]);

    if (result.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Device not found'
      });
    }

    return res.status(200).json({
      status: 'success',
      message: `Device ${field} updated successfully`,
      data: result[0]
    });

  } catch (error) {
    console.error('[PatchDeviceStatus] Error:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Failed to update device status'
    });
  }
}

// ** ==============================================================================================
// ** Function: AnalyzeDeviceFleet - Get analytics and insights about device fleet

export async function AnalyzeDeviceFleet(req, res) {
  const { account_id } = req.body;

  if (!account_id) {
    return res.status(400).json({
      status: 'error',
      message: 'Account ID is required'
    });
  }

  try {
    const sql = `
      SELECT 
        COUNT(*) as total_devices,
        COUNT(CASE WHEN active = true THEN 1 END) as active_devices,
        COUNT(CASE WHEN client_id IS NOT NULL THEN 1 END) as assigned_devices,
        COUNT(CASE WHEN last_event > NOW() - INTERVAL '5 minutes' THEN 1 END) as online_devices,
        COUNT(CASE WHEN last_event > NOW() - INTERVAL '30 minutes' AND last_event <= NOW() - INTERVAL '5 minutes' THEN 1 END) as recently_active_devices,
        COUNT(CASE WHEN last_event <= NOW() - INTERVAL '30 minutes' OR last_event IS NULL THEN 1 END) as disconnected_devices,
        COUNT(CASE WHEN app_version = 1 THEN 1 END) as legacy_devices,
        COUNT(CASE WHEN app_version = 2 THEN 1 END) as modern_devices
      FROM devices 
      WHERE account_id = $1 AND (archived = false OR archived IS NULL)
    `;

    const result = await request(sql, [account_id]);

    return res.status(200).json({
      status: 'success',
      data: result[0]
    });

  } catch (error) {
    console.error('[AnalyzeDeviceFleet] Error:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Failed to fetch device statistics'
    });
  }
}

// ** ==============================================================================================
// ** Function: GetDevice - Get detailed information for a specific device

export async function GetDevice(req, res) {
  const { id, account_id } = req.body;

  console.log('[GetDevice] Request:', { id, account_id });

  if (!id || !account_id) {
    return res.status(400).json({
      status: 'error',
      message: 'Device ID and account_id are required'
    });
  }

  try {
    // First, let's check if the device exists at all (without account_id filter)
    const deviceExistsQuery = `
      SELECT id, account_id, archived, active, name
      FROM devices
      WHERE id = $1
    `;
    const deviceExists = await request(deviceExistsQuery, [id]);
    console.log('[GetDevice] Device exists check:', deviceExists);

    const sql = `
      SELECT
        d.*,
        c.name AS client_name,
        c.logo AS client_logo,
        cl.location AS location_name,
        cl.id AS location_id,
        cl.street,
        cl.city,
        cl.state,
        cl.zipcode,
        d.departments,
        NOT EXISTS (
          SELECT 1 FROM timeclock
          WHERE start_device_id = d.id OR end_device_id = d.id
        ) AS is_deletable
      FROM devices d
      LEFT JOIN clients c ON d.client_id = c.id
      LEFT JOIN client_locations cl ON d.location_id = cl.id
      WHERE d.id = $1 AND d.account_id = $2 AND (d.archived = false OR d.archived IS NULL)
    `;

    const result = await request(sql, [id, account_id]);
    console.log('[GetDevice] Query result:', result);

    if (result.length === 0) {
      // Provide more specific error message based on what we found
      if (deviceExists.length === 0) {
        console.log('[GetDevice] Device does not exist');
        return res.status(404).json({
          status: 'error',
          message: 'Device not found'
        });
      } else {
        const device = deviceExists[0];
        console.log('[GetDevice] Device exists but filtered out:', device);

        if (device.account_id !== account_id) {
          return res.status(404).json({
            status: 'error',
            message: 'Device not found or access denied'
          });
        } else if (device.archived === true) {
          return res.status(404).json({
            status: 'error',
            message: 'Device is archived'
          });
        } else {
          return res.status(404).json({
            status: 'error',
            message: 'Device not found'
          });
        }
      }
    }

    return res.status(200).json({
      status: 'success',
      data: result[0]
    });

  } catch (error) {
    console.error('[GetDevice] Error:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Failed to fetch device data'
    });
  }
}

// ** ==============================================================================================
// ** Function: EditDevice - Update device with Slack notification

export async function EditDevice(req, res) {
  const {
    id,
    account_id,
    user_id,
    device_number,
    device_id,
    name,
    client_id,
    location_id,
    departments,
    brand,
    serial_number,
    model
  } = req.body;

  console.log('[EditDevice] Request:', { id, account_id, device_number, device_id, name });

  // Validate required fields
  if (!id || !account_id || !device_number || !device_id || !name) {
    return res.status(400).json({
      status: 'error',
      message: 'Missing required fields: id, account_id, device_number, device_id, name'
    });
  }

  try {
    // Check if device exists and belongs to account
    const existingDevice = await request(
      'SELECT * FROM devices WHERE id = $1 AND account_id = $2',
      [id, account_id]
    );

    if (existingDevice.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'Device not found or access denied'
      });
    }

    // Update device
    const updateSql = `
      UPDATE devices 
      SET 
        name = $1,
        device_code = $2,
        device_no = $3,
        brand = $4,
        client_id = $5,
        location_id = $6,
        departments = $7,
        serial_number = $8,
        model = $9
      WHERE id = $10 AND account_id = $11
      RETURNING *
    `;

    const result = await request(updateSql, [
      name,
      device_id,
      device_number,
      brand || null,
      client_id || null,
      location_id || null,
      departments ? (Array.isArray(departments) ? `{${departments.join(',')}}` : departments) : null,
      serial_number || null,
      model || null,
      id,
      account_id
    ]);

    if (result.length === 0) {
      return res.status(500).json({
        status: 'error',
        message: 'Failed to update device'
      });
    }

    const updatedDevice = result[0];

    // Get user info for Slack notification
    const userInfo = user_id ? await request('SELECT name, email FROM users WHERE id = $1', [user_id]) : [];
    const userName = userInfo.length > 0 ? userInfo[0].name : 'Unknown User';

    // Get client info if assigned
    const clientInfo = client_id ? await request('SELECT name FROM clients WHERE id = $1', [client_id]) : [];
    const clientName = clientInfo.length > 0 ? clientInfo[0].name : 'Unassigned';

    // Send Slack notification
    try {
      const slackMessage = {
        text: `📱 Timeclock Device Updated`,
        blocks: [
          {
            type: 'header',
            text: {
              type: 'plain_text',
              text: '📱 Timeclock Device Updated'
            }
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Device:* ${name} (#${device_number})`
              },
              {
                type: 'mrkdwn',
                text: `*Device ID:* ${device_id}`
              },
              {
                type: 'mrkdwn',
                text: `*Client:* ${clientName}`
              },
              {
                type: 'mrkdwn',
                text: `*Brand:* ${brand || 'Not specified'}`
              },
              {
                type: 'mrkdwn',
                text: `*Updated by:* ${userName}`
              },
              {
                type: 'mrkdwn',
                text: `*Time:* ${new Date().toLocaleString()}`
              }
            ]
          }
        ]
      };

      // Import Slack function
      const { SendSlackLog } = await import('../../libs/slack/index.js');
      await SendSlackLog(slackMessage);
      console.log('[EditDevice] Slack notification sent successfully');
    } catch (slackError) {
      console.error('[EditDevice] Slack notification failed:', slackError);
      // Don't fail the request if Slack fails
    }

    console.log(`[EditDevice] Device ${id} updated by user ${user_id}: ${name}`);

    return res.status(200).json({
      status: 'success',
      message: 'Device updated successfully',
      data: updatedDevice
    });

  } catch (error) {
    console.error('[EditDevice] Error:', error);

    // Handle specific database errors
    if (error.code === '23505') { // Unique constraint violation
      return res.status(400).json({
        status: 'error',
        message: 'Device code already exists. Please use a unique device code.'
      });
    }

    return res.status(500).json({
      status: 'error',
      message: 'Failed to update device'
    });
  }
}

// ** ==============================================================================================
// ** Function: GetDeviceActivity - Get paginated clock-in/out records for a specific device

export async function GetDeviceActivity(req, res) {
  const { device_id, page = 1, page_size = 20 } = req.body;

  if (!device_id) {
    return res.status(400).json({
      status: 'error',
      message: 'Device ID is required'
    });
  }

  // Validate pagination parameters
  const currentPage = Math.max(1, parseInt(page));
  const pageSize = Math.min(100, Math.max(1, parseInt(page_size))); // Max 100 records per page
  const offset = (currentPage - 1) * pageSize;

  try {
    // Build the main query for clock-in/out records
    const sql = `
      WITH device_records AS (
        -- Clock-in records (start_device_id)
        SELECT 
          tc.id,
          tc.employment_id,
          tc.start_date as event_date,
          CASE 
            WHEN tc.punch_type IS NULL OR tc.punch_type = 1 THEN 'CLOCK IN'
            WHEN tc.punch_type = 2 THEN 'MEAL IN'
            WHEN tc.punch_type = 3 THEN 'BREAK IN'
            ELSE 'CLOCK IN'
          END as event_type,
          tc.start_snap as snapshot,
          'IN' as direction,
          tc.start_date as sort_date
        FROM timeclock tc
        WHERE tc.start_device_id = $1 AND tc.start_date IS NOT NULL
        
        UNION ALL
        
        -- Clock-out records (end_device_id)
        SELECT 
          tc.id,
          tc.employment_id,
          tc.end_date as event_date,
          CASE 
            WHEN tc.punch_type IS NULL OR tc.punch_type = 1 THEN 'CLOCK OUT'
            WHEN tc.punch_type = 2 THEN 'MEAL OUT'
            WHEN tc.punch_type = 3 THEN 'BREAK OUT'
            ELSE 'CLOCK OUT'
          END as event_type,
          tc.end_snap as snapshot,
          'OUT' as direction,
          tc.end_date as sort_date
        FROM timeclock tc
        WHERE tc.end_device_id = $1 AND tc.end_date IS NOT NULL
      )
      SELECT 
        dr.id,
        dr.event_date,
        dr.event_type,
        dr.snapshot,
        dr.direction,
        CONCAT(e.first_name, ' ', e.last_name) as employee_name,
        e.id as employee_id,
        e.avatar,
        e.photo,
        c.id as client_id,
        c.name as client_name,
        d.name as department_name,
        -- Add device info (same for all records, but needed for response)
        (SELECT dev.serial_number FROM devices dev WHERE dev.id = $1 LIMIT 1) as device_serial_number,
        (SELECT dev.model FROM devices dev WHERE dev.id = $1 LIMIT 1) as device_model,
        (SELECT dev.name FROM devices dev WHERE dev.id = $1 LIMIT 1) as device_name
      FROM device_records dr
      JOIN employment_records er ON dr.employment_id = er.id
      JOIN employees e ON er.employee_id = e.id
      LEFT JOIN clients c ON er.client_id = c.id
      LEFT JOIN departments d ON er.department_id = d.id
      WHERE dr.event_date IS NOT NULL
      ORDER BY dr.sort_date DESC, dr.id DESC
      LIMIT $2 OFFSET $3
    `;

    // Get total count for pagination
    const countSql = `
      SELECT COUNT(*) as total_count
      FROM (
        SELECT tc.id FROM timeclock tc 
        WHERE tc.start_device_id = $1 AND tc.start_date IS NOT NULL
        UNION ALL
        SELECT tc.id FROM timeclock tc 
        WHERE tc.end_device_id = $1 AND tc.end_date IS NOT NULL
      ) device_events
    `;

    // Execute both queries in parallel
    const [records, countResult] = await Promise.all([
      request(sql, [device_id, pageSize, offset]),
      request(countSql, [device_id])
    ]);

    const totalCount = parseInt(countResult[0]?.total_count || 0);
    const totalPages = Math.ceil(totalCount / pageSize);

    return res.status(200).json({
      status: 'success',
      data: {
        records: records.map((record, index) => ({
          ...record,
          row_number: offset + index + 1, // Add row number for table display
          event_date: record.event_date ? new Date(record.event_date).toISOString() : null
        })),
        pagination: {
          current_page: currentPage,
          page_size: pageSize,
          total_count: totalCount,
          total_pages: totalPages,
          has_next: currentPage < totalPages,
          has_prev: currentPage > 1
        }
      }
    });

  } catch (error) {
    console.error('[GetDeviceActivity] Error:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Failed to fetch device activity data'
    });
  }
} 