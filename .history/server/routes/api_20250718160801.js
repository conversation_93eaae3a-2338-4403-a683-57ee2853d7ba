import express from 'express';
import EventEmitter from 'events';
import rateLimit from 'express-rate-limit';
import multer from 'multer';
import { request } from '../db/index.js';

import { isDev } from '../consts.js';

// -----------------------------------------------------------------------------
// ** Account Imports
import { GetAccountByDomain, GetAccountTimecardSettings } from './ops/account.js';

// ** Auth Imports
import { AuthenticateUser, ValidateToken } from './ops/auth.js';

// ** Employee Imports
import {
  NavSearch,
  GetEmployee,
  GetEmployees,
  GetEmployeeSnapshots,
  UpdateEmployeeAvatar,
  DeleteEmployeeAvatar,
  SearchSSN,
  GetEmployeeEmploymentsById,
  GetActiveEmployees,
  UpdateEmployee,
  GetEmployeesByClientsAndDepartments,
  GetUniqueStatesAndCities
} from './ops/employee.js'; 

// ** Employment Imports
import {
  GetEmploymentHistory,
  CheckEmployeePIN,
  UpdateEmployeePIN,
  SendBulkSMS_API,
  GetSMSPricing
} from './ops/employment.js';

// ** Supplemental Forms Imports
import {
  GetEmployeeSupplementalFormData,
  GetEmployee218ETFormData,
  GenerateSupplementalForm,
  SaveSupplementalFormToDatabase,
  GetEmployeeSupplementalForms,
  DeleteSupplementalForm,
  DownloadSupplementalForm,
  ViewSupplementalForm,
  UploadFileToS3
} from './ops/supplemental_forms.js';

// ** Timecard Imports
import {
  GetTimecardData,
  GetEmployeeEmployments,
  GetEmployeesInTheDepartment
} from './ops/timecard.js';

// ** Client Imports
import {
  GetClients,
  GetClient,
  GetClientDepartments,
  GetClientEmployees,
  ClientOps,
  GetClientLocations
} from './ops/client.js';

// ** Department Imports
import { DepartmentOps } from './ops/department.js';

// ** Payroll Imports
import { GetPayrollData } from './ops/payroll.js';

// ** Finance Imports
import {
  GetInvoiceData,
  GetInvoiceDataWithOvertime,
  GetInvoiceDataWithOvertimeOptimized,
  GetEmployeeInvoiceData,
  DebugTimeclockData,
  GetFinanceStatistics,
  GetFinanceStatisticsWithOvertime,
  GetFinanceFilters
} from './ops/finance.js';

// ** Invoice PDF Imports
import {
  GenerateCompleteInvoicePDF,
  GetInvoicePreviewData
} from './ops/invoicePDF.js';

// ** Application Imports
import {
  GetApplications,
  GetApplication,
  GenerateGovernmentForm,
  SaveUploadedDocument,
  DeleteUploadedDocument,
  DeleteBunnyCDNDocument,
  UpdateApplicationNote,
  InsertApplication,
  UpdateApplication,
  DeleteApplication
} from './ops/application.js';

// ** Training Imports
import {
  GetTrainings,
  GetTrainingRecords,
  PrintTrainings,
  RegisterTraining,
  TrainingLogin
} from './ops/training.js';

// ** Dashboard Imports
import {
  GetOnboardEmployees,
  GetOnboardEmployeesChartData,
  GetDashboardStatistics,
  GetDashboardInsights,
  GetRealTimeMetrics,
  RefreshDashboardCache
} from './ops/dashboard.js';

// ** Transportation Imports:
import {
  GetVehicles,
  VehicleOps,
  PassengerOps,
  GetDrivers,
  DriverOps,
  UpdateDriverLicense,
  DeleteDriverLicense
} from './ops/transportation.js';

/* Mobile App Imports */
import { GetMobileAppUsers, GetMobileLogs, SyncFirebaseUIDs, SendFCMNotificationToUser } from './ops/mobileapp.js';

/* Device Fleet Management Imports */
import {
  FetchDeviceFleet,
  ManageDevice,
  PatchDeviceStatus,
  AnalyzeDeviceFleet,
  GetDevice,
  EditDevice,
  GetDeviceActivity,
  GetNextDeviceNumber
} from './ops/timeclock_devices.js';

// ** Libraries
import { GeneratePDF, GenerateInvoicePDF } from '../libs/pdf/index.js'; // PDF API
import { GetSubmission } from '../libs/docuseal/index.js'; // DocuSeal API
import { downloadFromS3, uploadToS3 } from '../libs/aws/index.js'; // AWS S3 API
import { GoogleAuth, RefreshGoogleToken } from '../libs/google/index.js'; // Google API
import { SendFCMNotification } from '../libs/firebase/notification.js'; // Firebase FCM API
import { FirebaseAPI as FirebaseAPIFunction } from '../libs/firebase/index.js'; // Firebase API
import { GetHerokuBuildLogs, GetHerokuBuildLogContent } from '../libs/heroku/index.js'; // Heroku API

// ** Other Imports
import {
  SystemInfo
} from './ops/other.js';
import { GetSystemUsers, GetSystemAccounts, AddSystemUser, GetSystemUser } from './ops/system.js';

// ** Performance Testing Imports
import {
  RunInvoiceBenchmark,
  RunLoadTest,
  QuickPerformanceCheck,
  GetPerformanceStats,
  ResetPerformanceStats,
  GetSystemResources
} from './ops/performance.js';



// -----------------------------------------------------------------------------

const router = express.Router();
const Bus = new EventEmitter();
Bus.setMaxListeners(100);

const REQUEST_LIMIT = 85;

const limiter = rateLimit({
  windowMs: 60 * 1000,
  max: REQUEST_LIMIT,
  keyGenerator: (req) => req.ip,
  message: {
    status: 'error',
    message: 'Too many requests, please try again later.'
  }
});

if (!isDev) {
  // Apply rate limiting middleware only in production
  router.use(limiter);
}

const upload = multer();

// ** Handlers
const handlers = {

  // -- Endpoints:
  GetAccountByDomain,
  GetAccountTimecardSettings,
  AuthenticateUser,
  ValidateToken,
  GetEmploymentHistory,
  GetApplications,
  GetApplication,
  GenerateGovernmentForm,
  SaveUploadedDocument,
  UpdateApplicationNote,
  GetSubmission,
  GeneratePDF,
  GenerateInvoicePDF,
  DeleteUploadedDocument,
  DeleteBunnyCDNDocument,
  InsertApplication,
  UpdateApplication,
  DeleteApplication,
  GetPayrollData,
  GetInvoiceData,
  GetInvoiceDataWithOvertime,
  GetInvoiceDataWithOvertimeOptimized,
  GetEmployeeInvoiceData,
  DebugTimeclockData,
  GetFinanceStatistics,
  GetFinanceStatisticsWithOvertime,
  GetFinanceFilters,
  GenerateCompleteInvoicePDF,
  GetInvoicePreviewData,

  // -- Training:
  GetTrainings,
  GetTrainingRecords,
  PrintTrainings,
  RegisterTraining,
  TrainingLogin,

  // -- Employees:
  NavSearch,
  GetEmployee,
  GetEmployees,
  GetEmployeeSnapshots,
  UpdateEmployeeAvatar,
  DeleteEmployeeAvatar,
  SearchSSN,
  GetEmployeeEmploymentsById,
  GetActiveEmployees,
  UpdateEmployee,
  GetEmployeesByClientsAndDepartments,
  GetUniqueStatesAndCities,

  // -- Employments:
  CheckEmployeePIN,
  UpdateEmployeePIN,

  // -- SMS:
  SendBulkSMS: SendBulkSMS_API,
  GetSMSPricing,

  // -- Timecard:
  GetTimecardData,
  GetEmployeeEmployments,
  GetEmployeesInTheDepartment,

  // -- Clients:
  GetClients,
  GetClient,
  GetClientDepartments,
  GetClientEmployees,
  ClientOps,
  GetClientLocations,

  // -- Departments:
  DepartmentOps,

  // -- Transportation:
  GetVehicles,
  VehicleOps,
  PassengerOps,
  GetDrivers,
  DriverOps,
  UpdateDriverLicense,
  DeleteDriverLicense,

  // -- Dashboard:
  GetOnboardEmployees,
  GetOnboardEmployeesChartData,
  GetDashboardStatistics,
  GetDashboardInsights,
  GetRealTimeMetrics,
  RefreshDashboardCache,

  // -- Mobile App:
  GetMobileAppUsers,
  GetMobileLogs,
  SyncFirebaseUIDs,
  SendFCMNotificationToUser,

  // -- Device Fleet Management:
  FetchDeviceFleet,
  ManageDevice,
  PatchDeviceStatus,
  AnalyzeDeviceFleet,
  GetDevice,
  EditDevice,
  GetDeviceActivity,

  // -- Others:
  SystemInfo,
  GetHerokuBuildLogs,
  GetHerokuBuildLogContent,
  GetSystemUsers,
  GetSystemAccounts,
  AddSystemUser,
  GetSystemUser,

  // -- Performance Testing:
  RunInvoiceBenchmark,
  RunLoadTest,
  QuickPerformanceCheck,
  GetPerformanceStats,
  ResetPerformanceStats,
  GetSystemResources,

  // -- APIs
  downloadFromS3,
  uploadToS3,
  GoogleAuth,
  RefreshGoogleToken,
  SendFCMNotification,

  // -- Supplemental Forms
  GetEmployeeSupplementalFormData,
  GetEmployee218ETFormData,
  GenerateSupplementalForm,
  SaveSupplementalFormToDatabase,
  GetEmployeeSupplementalForms,
  DeleteSupplementalForm,
  DownloadSupplementalForm,
  ViewSupplementalForm,
  UploadFileToS3,

  FirebaseAPI: async (req, res) => {
    try {
      console.log('[FirebaseAPI Handler] Processing request:', req.body);
      const result = await FirebaseAPIFunction(req.body);
      console.log('[FirebaseAPI Handler] Sending response:', result);
      res.json(result);
    } catch (error) {
      console.error('[FirebaseAPI Handler] Error:', error);
      res.status(500).json({ status: 'error', message: 'Internal server error' });
    }
  }

};

// -------------------------------------------------------------

router.use('/:op', upload.none(), async (req, res) => {

  const { op } = req.params;
  const handler = handlers[op];

  if (handler) {

    try {

      await handler(req, res);
      //console.log(`[${op}] Handler success`);

    } catch (error) {
      console.error(`[${op}] Handler error:`, error);
      res.status(500).json({ status: 'error', message: 'Internal server error' });
    }

  } else {

    res.status(404).json({
      status: 'error',
      message: 'Operation not found'
    });

  }
});





// GET /api/dashboard-stats?start=YYYY-MM-DD&end=YYYY-MM-DD (Legacy endpoint for backward compatibility)
router.get('/dashboard-stats', async (req, res) => {
  try {
    const { start, end, account_id } = req.query;
    const userAccountId = req.user?.account_id || account_id;

    if (!userAccountId) {
      return res.status(400).json({ error: 'Missing account_id' });
    }

    // Convert query params to request body format expected by ops function
    req.body = {
      account_id: userAccountId,
      start,
      end
    };

    // Use the proper ops function
    await handlers.GetDashboardStatistics(req, res);
  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

export default router;