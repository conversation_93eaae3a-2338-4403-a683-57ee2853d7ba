---
type: "agent_requested"
description: "Core development guidelines for VEA Timeclock covering documentation standards, UI design principles, and implementation workflow."
---
### UI

**Rules:**
- Follow modern design principles aligned with the Ant Design and Bootstrap 5 frameworks.
- Maintain a clean, intuitive interface with consistent spacing and alignment.
- Use clear, concise labels for all buttons and inputs (max 2-3 words when possible).
- Ensure the interface is fully responsive across all devices (mobile, tablet, desktop).
- Follow the established color scheme with PRIMARY_COLOR (#2E6454) as the main brand color.
- Implement loading states for all asynchronous operations.
- Provide meaningful error messages that guide users toward resolution.
- Maintain accessibility standards (WCAG 2.1 AA) for all UI components.

---

### Build & Run

**Rules:**
- Always respect the project own port if 'npm run dev' command is run, if the port is already using, then kill first then run the command with the correct port.
- Implement changes without asking for permission, unless multiple critical options require user input.
- Ensure all code changes pass the existing test suite (`npm test`).
- Optimize bundle size by using code splitting and lazy loading where appropriate.
- Follow the established project structure for new files and components.
- Set the appropriate environment variables for development vs. production.
- Ensure compatibility with the Node.js version specified in package.json.
- Document any new environment variables or configuration changes.