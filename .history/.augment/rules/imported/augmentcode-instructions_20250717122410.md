---
type: "agent_requested"
description: "Core development guidelines for VEA Timeclock covering documentation standards, UI design principles, and implementation workflow."
---
- UI
  - Follow modern design principles (Ant Design + Bootstrap 5)
  - Maintain clean layout with consistent spacing & alignment
  - Use concise labels (2–3 words max for buttons/inputs)
  - Fully responsive on mobile, tablet, desktop
  - Primary color: #2E6454
  - Show loading states for async actions
  - Display helpful, actionable error messages
  - Meet WCAG 2.1 AA accessibility standards

- Build & Run
  - Implement changes without asking unless critical decision needed
  - All code must pass `npm test`
  - Optimize bundle with code splitting & lazy loading
  - Follow existing project structure for all new files/components
  - Use correct env vars for dev vs. prod
  - Match Node.js version in `package.json`
  - Document all env/config changes (e.g. `.env.example`)