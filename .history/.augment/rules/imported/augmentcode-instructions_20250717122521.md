---
type: "always_apply"
description: "Core development guidelines for VEA Timeclock covering documentation standards, UI design principles, and implementation workflow."
---
- UI
  - Follow modern design principles (Ant Design + Bootstrap 5)
  - Maintain clean layout with consistent spacing & alignment
  - Use concise labels (2–3 words max for buttons/inputs)
  - Fully responsive on mobile, tablet, desktop
  - Primary color: #2E6454
  - Show loading states for async actions
  - Display helpful, actionable error messages
  - Meet WCAG 2.1 AA accessibility standards

- Build & Run
  - Implement changes without asking unless critical decision needed
  - All code must pass `npm test`
  - Optimize bundle with code splitting & lazy loading
  - Follow existing project structure for all new files/components
  - Use correct env vars for dev vs. prod
  - Match Node.js version in `package.json`
  - Document all env/config changes (e.g. `.env.example`)

- Code Quality
  - Follow consistent code style (Prettier + ESLint)
  - Write self-explanatory, modular, reusable code
  - Avoid hardcoding — use constants and config files
  - Prefer composition over inheritance
  - Minimize side effects; keep functions pure where possible

- Git & Collaboration
  - Use clear, conventional commit messages (e.g. feat:, fix:, chore:)
  - Always branch from `main`/`dev` and open pull requests
  - Keep PRs focused and small (one purpose per PR)
  - Resolve merge conflicts proactively
  - Link commits/PRs to related issues or features

- Testing
  - Write unit tests for core logic and utilities
  - Cover critical user flows with integration tests
  - Use mocks/stubs for external dependencies
  - Ensure test suite runs cleanly before every release

- Performance
  - Avoid unnecessary re-renders and large component trees
  - Debounce or throttle frequent updates (e.g. search, scroll)
  - Cache expensive operations and network calls when possible
  - Measure and optimize Time to Interactive and Largest Contentful Paint

- Security
  - Sanitize all user input on both client and server
  - Use HTTPS for all API and static asset requests
  - Never commit secrets or credentials to the repo
  - Use helmet, rate-limiters, and CSRF protection on backend

- Documentation
  - Keep `README.md` up to date with install/run/test instructions
  - Document custom hooks, utils, and services as needed
  - Include inline comments for complex logic
  - Maintain an `.env.example` file with all required variables