---
type: "always_apply"
description: "Backend API implementation guide for VEA Timeclock system, covering architecture, patterns, error handling, database operations, security, and examples for creating new endpoints with POST-based centralized routing"
---
# VEA Timeclock Backend API Implementation Guide

## Overview

This guide provides a comprehensive overview of the backend API architecture in the VEA Timeclock system. It covers the standard patterns, best practices, and implementation steps for creating new API endpoints.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Implementation Process](#implementation-process)
3. [Standard Patterns](#standard-patterns)
4. [Error Handling](#error-handling)
5. [Database Operations](#database-operations)
6. [Security Considerations](#security-considerations)
7. [Testing and Debugging](#testing-and-debugging)
8. [Examples](#examples)

## Architecture Overview

The VEA Timeclock backend follows a unified POST-based architecture with centralized routing. All API operations are handled through a single route pattern with operation-specific handlers.

### Core Architecture

```javascript
// server/routes/api.js
router.use('/:op', upload.none(), async (req, res) => {
  const { op } = req.params;
  const handler = handlers[op];
  
  if (handler) {
    await handler(req, res);
  } else {
    res.status(404).json({
      status: 'error',
      message: 'Operation not found'
    });
  }
});
```

### Handler Registration

```javascript
const handlers = {
  GetEmployee,
  GetClients,
  UpdateEmployee,
  DeleteRecord,
  // ... all operations
};
```

## Implementation Process

### 1. Create Operation Function

Create a new function in an appropriate file under `server/routes/ops/`:

```javascript
// server/routes/ops/module.js
export async function OperationName(req, res) {
  try {
    const { param1, param2, account_id } = req.body || {};
    
    // Validation
    if (!param1) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required parameter: param1'
      });
    }
    
    // Business logic
    const result = await someOperation(param1, param2);
    
    return res.json({
      status: 'success',
      data: result
    });
    
  } catch (error) {
    console.error('OperationName error:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Internal server error'
    });
  }
}
```

### 2. Register the Handler

Add your operation to the handlers object in `server/routes/api.js`:

```javascript
// Import your new operation
import { OperationName } from './ops/module.js';

// Add to handlers object
const handlers = {
  // Existing handlers
  GetEmployee,
  GetClients,
  // Add your new operation
  OperationName
};
```

### 3. Create Frontend Hook

```typescript
// admin/src/hooks/useModule.ts
const useOperation = () => {
  const { user } = useAuthStore();
  const account_id = user?.account?.id;
  
  async function executeOperation(payload: OperationPayload) {
    if (!account_id) {
      throw new Error('Account ID is missing');
    }
    
    try {
      const res = await api('OperationName', {
        ...payload,
        account_id,
      });
      
      if (res.status === 'error') {
        throw new Error(res.message || 'Operation failed');
      }
      
      return res.data;
    } catch (error) {
      console.error('[OperationName Error]', error);
      throw error;
    }
  }
  
  return useMutation({
    mutationFn: executeOperation,
    onSuccess: () => {
      // Handle success
    },
    onError: (error) => {
      // Handle error
    }
  });
};
```

## Standard Patterns

### 1. Single-Purpose Operations

Each operation should have a single, well-defined purpose:

```javascript
export async function GetEmployeeDetails(req, res) {
  // Only fetches employee details
}

export async function UpdateEmployeeDetails(req, res) {
  // Only updates employee details
}
```

### 2. Multi-Action Operations

For related actions, use an event-based pattern:

```javascript
export async function EmployeeOps(req, res) {
  const { event, ...data } = req.body || {};
  
  if (!event) {
    return res.status(400).json({
      status: 'error',
      message: 'Event is required'
    });
  }
  
  const handlers = {
    'add': AddEmployee,
    'update': UpdateEmployee,
    'delete': DeleteEmployee,
    'activate': ActivateEmployee,
    'deactivate': DeactivateEmployee
  };
  
  if (handlers[event]) {
    return await handlers[event](req, res, data);
  } else {
    return res.status(404).json({
      status: 'error',
      message: 'Event not found'
    });
  }
}
```

### 3. Pagination Pattern

For endpoints that return lists:

```javascript
export async function GetEmployeeList(req, res) {
  const { 
    account_id, 
    page = 1, 
    limit = 10, 
    search = '', 
    sortBy = 'created_at', 
    sortOrder = 'DESC' 
  } = req.body || {};
  
  // Calculate offset
  const offset = (page - 1) * limit;
  
  // Build query with search and sorting
  let query = `
    SELECT * FROM employees 
    WHERE account_id = $1
  `;
  
  const params = [account_id];
  let paramIndex = 2;
  
  if (search) {
    query += ` AND (
      first_name ILIKE $${paramIndex} OR 
      last_name ILIKE $${paramIndex} OR 
      email ILIKE $${paramIndex}
    )`;
    params.push(`%${search}%`);
    paramIndex++;
  }
  
  // Add sorting
  query += ` ORDER BY ${sortBy} ${sortOrder}`;
  
  // Add pagination
  query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
  params.push(limit, offset);
  
  // Count total for pagination
  const countQuery = `
    SELECT COUNT(*) FROM employees 
    WHERE account_id = $1
    ${search ? ` AND (
      first_name ILIKE $2 OR 
      last_name ILIKE $2 OR 
      email ILIKE $2
    )` : ''}
  `;
  
  const countParams = [account_id];
  if (search) {
    countParams.push(`%${search}%`);
  }
  
  try {
    const [results, countResult] = await Promise.all([
      request(query, params),
      request(countQuery, countParams)
    ]);
    
    const total = parseInt(countResult[0].count);
    const totalPages = Math.ceil(total / limit);
    
    return res.json({
      status: 'success',
      data: results,
      pagination: {
        current: page,
        pageSize: limit,
        total,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });
  } catch (error) {
    console.error('GetEmployeeList error:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Failed to fetch employee list'
    });
  }
}
```

### 4. File Upload Pattern

For operations that involve file uploads:

```javascript
// In server/routes/uploader.js
router.post('/:path(*)', upload.single('file'), async (req, res) => {
  const { file } = req;
  const { path } = req.params;
  const { filename } = req.body;
  
  if (!file || !path) {
    return res.status(400).json({
      status: 'error',
      message: 'Missing file or path'
    });
  }
  
  try {
    const key = `${path}/${filename || file.originalname}`;
    
    const result = await uploadToS3({
      key,
      body: file.buffer,
      contentType: file.mimetype
    });
    
    return res.json({
      status: 'success',
      data: {
        key,
        url: result.Location,
        filename: filename || file.originalname
      }
    });
  } catch (error) {
    console.error('File upload error:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Failed to upload file'
    });
  }
});
```

## Error Handling

### Standard Error Responses

```javascript
// Validation error
return res.status(400).json({
  status: 'error',
  message: 'Missing required parameter'
});

// Not found error
return res.status(404).json({
  status: 'error',
  message: 'Resource not found'
});

// Server error
return res.status(500).json({
  status: 'error',
  message: 'Internal server error'
});
```

### Error Logging

Always log errors with context:

```javascript
try {
  // Operation code
} catch (error) {
  console.error('[OperationName] Error details:', error);
  return res.status(500).json({
    status: 'error',
    message: 'Operation failed'
  });
}
```

## Database Operations

### Using the Database Client

```javascript
import { request } from '../../db/index.js';

// Single query
const result = await request(
  'SELECT * FROM employees WHERE id = $1',
  [employeeId]
);

// Transaction
const client = await getClient();
try {
  await client.query('BEGIN');
  
  const result1 = await client.query(
    'INSERT INTO employees (name) VALUES ($1) RETURNING id',
    ['John Doe']
  );
  
  const employeeId = result1.rows[0].id;
  
  await client.query(
    'INSERT INTO employee_details (employee_id, field) VALUES ($1, $2)',
    [employeeId, 'value']
  );
  
  await client.query('COMMIT');
  return employeeId;
} catch (error) {
  await client.query('ROLLBACK');
  throw error;
} finally {
  client.release();
}
```

### Query Building

For complex queries, use a builder pattern:

```javascript
function buildEmployeeQuery(filters) {
  let query = 'SELECT * FROM employees WHERE 1=1';
  const params = [];
  let paramIndex = 1;
  
  if (filters.account_id) {
    query += ` AND account_id = $${paramIndex}`;
    params.push(filters.account_id);
    paramIndex++;
  }
  
  if (filters.status) {
    query += ` AND status = $${paramIndex}`;
    params.push(filters.status);
    paramIndex++;
  }
  
  if (filters.search) {
    query += ` AND (
      first_name ILIKE $${paramIndex} OR
      last_name ILIKE $${paramIndex} OR
      email ILIKE $${paramIndex}
    )`;
    params.push(`%${filters.search}%`);
    paramIndex++;
  }
  
  return { query, params };
}
```

## Security Considerations

### Data Isolation

Always include account_id in queries:

```javascript
const result = await request(
  'SELECT * FROM employees WHERE account_id = $1 AND id = $2',
  [account_id, employee_id]
);
```

### Input Validation

Validate all input parameters:

```javascript
if (!employee_id || !account_id) {
  return res.status(400).json({
    status: 'error',
    message: 'Missing required parameters'
  });
}

// Validate numeric values
if (isNaN(parseInt(employee_id))) {
  return res.status(400).json({
    status: 'error',
    message: 'Invalid employee ID'
  });
}
```

### Parameterized Queries

Always use parameterized queries to prevent SQL injection:

```javascript
// GOOD - Parameterized query
const result = await request(
  'SELECT * FROM employees WHERE id = $1',
  [employeeId]
);

// BAD - String concatenation (vulnerable to SQL injection)
const result = await request(
  `SELECT * FROM employees WHERE id = ${employeeId}`
);
```

## Testing and Debugging

### Manual Testing

Test your API endpoints using Postman or curl:

```bash
curl -X POST http://localhost:3000/api/GetEmployee \
  -H "Content-Type: application/json" \
  -d '{"employee_id": 123, "account_id": 456}'
```

### Logging for Debugging

Add detailed logging for debugging:

```javascript
export async function OperationName(req, res) {
  console.log('[OperationName] Request body:', req.body);
  
  try {
    // Operation code
    console.log('[OperationName] Operation result:', result);
    
    return res.json({
      status: 'success',
      data: result
    });
  } catch (error) {
    console.error('[OperationName] Error:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Operation failed'
    });
  }
}
```

## Examples

### Example 1: Get Employee Details

```javascript
// server/routes/ops/employee.js
export async function GetEmployee(req, res) {
  try {
    const { employee_id, account_id } = req.body || {};
    
    // Validation
    if (!employee_id || !account_id) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required parameters'
      });
    }
    
    // Query database
    const query = `
      SELECT e.*, 
        c.name as client_name,
        d.name as department_name
      FROM employees e
      LEFT JOIN clients c ON e.client_id = c.id
      LEFT JOIN departments d ON e.department_id = d.id
      WHERE e.id = $1 AND e.account_id = $2
    `;
    
    const result = await request(query, [employee_id, account_id]);
    
    if (!result.length) {
      return res.status(404).json({
        status: 'error',
        message: 'Employee not found'
      });
    }
    
    return res.json({
      status: 'success',
      data: result[0]
    });
    
  } catch (error) {
    console.error('[GetEmployee] Error:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Failed to fetch employee details'
    });
  }
}
```

### Example 2: Update Employee

```javascript
// server/routes/ops/employee.js
export async function UpdateEmployee(req, res) {
  try {
    const { 
      employee_id, 
      account_id,
      first_name,
      last_name,
      email,
      phone,
      status
    } = req.body || {};
    
    // Validation
    if (!employee_id || !account_id) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required parameters'
      });
    }
    
    // Build update query dynamically
    const updates = [];
    const params = [employee_id, account_id];
    let paramIndex = 3;
    
    if (first_name !== undefined) {
      updates.push(`first_name = $${paramIndex++}`);
      params.push(first_name);
    }
    
    if (last_name !== undefined) {
      updates.push(`last_name = $${paramIndex++}`);
      params.push(last_name);
    }
    
    if (email !== undefined) {
      updates.push(`email = $${paramIndex++}`);
      params.push(email);
    }
    
    if (phone !== undefined) {
      updates.push(`phone = $${paramIndex++}`);
      params.push(phone);
    }
    
    if (status !== undefined) {
      updates.push(`status = $${paramIndex++}`);
      params.push(status);
    }
    
    // Add updated_at timestamp
    updates.push(`updated_at = NOW()`);
    
    if (updates.length === 0) {
      return res.status(400).json({
        status: 'error',
        message: 'No fields to update'
      });
    }
    
    const query = `
      UPDATE employees
      SET ${updates.join(', ')}
      WHERE id = $1 AND account_id = $2
      RETURNING *
    `;
    
    const result = await request(query, params);
    
    if (!result.length) {
      return res.status(404).json({
        status: 'error',
        message: 'Employee not found'
      });
    }
    
    return res.json({
      status: 'success',
      data: result[0],
      message: 'Employee updated successfully'
    });
    
  } catch (error) {
    console.error('[UpdateEmployee] Error:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Failed to update employee'
    });
  }
}
```

### Example 3: Form-Specific Data

```javascript
// server/routes/ops/supplemental_forms.js
export async function GetEmployeeSupplementalFormData(req, res) {
  try {
    const { employee_id, account_id, form_type } = req.body || {};
    
    // Validation
    if (!employee_id || !account_id || !form_type) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required parameters'
      });
    }
    
    // Get employee data
    const employeeQuery = `
      SELECT e.*, c.name as client_name
      FROM employees e
      LEFT JOIN clients c ON e.client_id = c.id
      WHERE e.id = $1 AND e.account_id = $2
    `;
    
    const employeeResult = await request(employeeQuery, [employee_id, account_id]);
    
    if (!employeeResult.length) {
      return res.status(404).json({
        status: 'error',
        message: 'Employee not found'
      });
    }
    
    const employee = employeeResult[0];
    
    // Form-specific data mapping
    const formConfig = {
      '218E': {
        fields: ['ssn', 'address', 'phone', 'email'],
        title: 'Employment Information Form',
        editable: ['address', 'phone', 'email']
      },
      '218ET': {
        fields: ['last_4_ssn', 'last_day_of_work', 'reason_for_leaving'],
        title: 'End of Employment Form',
        editable: ['last_day_of_work', 'reason_for_leaving']
      },
      '281': {
        fields: ['ssn', 'income', 'employment_start_date'],
        title: 'Income Verification Form',
        editable: []
      }
    };
    
    // Check if form type exists
    if (!formConfig[form_type]) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid form type'
      });
    }
    
    // Prepare response data with only required fields
    const responseData = {
      employee_name: `${employee.first_name} ${employee.last_name}`,
      employer: employee.client_name || 'Not Assigned',
    };
    
    // Add form-specific fields
    formConfig[form_type].fields.forEach(field => {
      if (field === 'ssn') {
        responseData.ssn = employee.ssn;
      } else if (field === 'last_4_ssn') {
        responseData.last_4_ssn = employee.ssn ? employee.ssn.slice(-4) : '';
      } else {
        responseData[field] = employee[field] || '';
      }
    });
    
    // Add form configuration for frontend
    responseData._form_config = formConfig[form_type];
    
    return res.json({
      status: 'success',
      data: responseData
    });
    
  } catch (error) {
    console.error('[GetEmployeeSupplementalFormData] Error:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Failed to fetch form data'
    });
  }
}
```

---

By following these patterns and best practices, you can create consistent, maintainable, and secure API endpoints for the VEA Timeclock system.